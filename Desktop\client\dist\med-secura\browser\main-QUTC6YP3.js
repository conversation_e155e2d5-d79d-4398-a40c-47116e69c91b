import{a as W,b as nt,c as Qe,d as ze,e as Do,f as ne,g as <PERSON>,h as Nt,i as vi,j as hn,k as le,l as sr}from"./chunk-WOUCJQGE.js";import{a as _n,b as Wo,c as $o,d as yi,e as Yo,f as vn,g as Ci,h as or,i as Vt,j as Ut,k as rr,l as ar}from"./chunk-VFZ3QRB6.js";import{$ as po,$a as Eo,$b as xe,A as Pe,Aa as pt,Ab as Fo,Ac as Ye,Ba as _o,Bc as ee,Cb as zt,Cc as Fe,D as v,Dc as er,E as x,Ea as vo,Eb as No,Ec as tr,F as sn,Fa as xo,Fb as zo,G as ln,Gb as Ro,Gc as nr,Ha as gt,Hb as bn,Hc as Pn,I as so,Ia as <PERSON>,Ib as <PERSON>,Ic as <PERSON>,<PERSON> as je,<PERSON>a as U,Jb as Vo,Jc as ir,<PERSON> as hi,<PERSON> as un,<PERSON><PERSON> as Lt,<PERSON> as bi,<PERSON> as yo,Lc as lr,<PERSON> as Tt,Ma as fn,N as cn,O as d,Oa as A,P,Q as lo,R as E,Ra as ut,S as _,Sa as Co,T as It,Ta as Po,Tb as Be,U as p,Ua as Mo,Ub as Uo,V as _i,Va as wo,Vb as Rt,W as ae,Wa as At,Wb as it,X as Oe,Xa as Oo,Xb as Q,Y as co,Ya as So,Yb as Ct,Zb as me,_ as mo,_a as Ft,_b as qe,a as B,aa as go,ab as j,ac as jo,b as Ce,ba as r,bb as _e,bc as Bo,c as io,ca as a,cb as ft,cc as xi,d as ke,da as g,db as ko,dc as fe,e as an,ea as dn,eb as To,ec as ht,f as Zt,fa as mn,fb as Io,fc as qo,g as oo,ga as V,gb as ve,gc as se,h as ro,ha as h,hc as Ho,i as ie,ia as b,ic as H,j as lt,ja as uo,jc as S,k as ce,ka as fo,kc as Z,l as we,la as ho,lc as De,ma as bo,mc as Zo,na as dt,nc as Ko,o as re,oa as l,oc as pe,pa as T,pc as Ae,qa as M,qc as Qo,ra as pn,s as ao,sa as z,sc as We,t as ct,ta as R,tc as $e,ua as L,uc as xn,v as Kt,va as et,vc as yn,w as Y,wa as mt,wc as Cn,x as be,xa as Dt,xc as Go,ya as gn,yc as Jo,z as de,za as tt,zb as Ao,zc as Xo}from"./chunk-YV65XDJO.js";function cr(o){return new Y(3e3,!1)}function $r(){return new Y(3100,!1)}function Yr(){return new Y(3101,!1)}function Hr(o){return new Y(3001,!1)}function Zr(o){return new Y(3003,!1)}function Kr(o){return new Y(3004,!1)}function Qr(o,e){return new Y(3005,!1)}function Gr(){return new Y(3006,!1)}function Jr(){return new Y(3007,!1)}function Xr(o,e){return new Y(3008,!1)}function ea(o){return new Y(3002,!1)}function ta(o,e,t,n,i){return new Y(3010,!1)}function na(){return new Y(3011,!1)}function ia(){return new Y(3012,!1)}function oa(){return new Y(3200,!1)}function ra(){return new Y(3202,!1)}function aa(){return new Y(3013,!1)}function sa(o){return new Y(3014,!1)}function la(o){return new Y(3015,!1)}function ca(o){return new Y(3016,!1)}function da(o,e){return new Y(3404,!1)}function ma(o){return new Y(3502,!1)}function pa(o){return new Y(3503,!1)}function ga(){return new Y(3300,!1)}function ua(o){return new Y(3504,!1)}function fa(o){return new Y(3301,!1)}function ha(o,e){return new Y(3302,!1)}function ba(o){return new Y(3303,!1)}function _a(o,e){return new Y(3400,!1)}function va(o){return new Y(3401,!1)}function xa(o){return new Y(3402,!1)}function ya(o,e){return new Y(3505,!1)}function bt(o){switch(o.length){case 0:return new Nt;case 1:return o[0];default:return new vi(o)}}function Pr(o,e,t=new Map,n=new Map){let i=[],s=[],c=-1,m=null;if(e.forEach(u=>{let f=u.get("offset"),C=f==c,y=C&&m||new Map;u.forEach((D,N)=>{let O=N,I=D;if(N!=="offset")switch(O=o.normalizePropertyName(O,i),I){case hn:I=t.get(N);break;case nt:I=n.get(N);break;default:I=o.normalizeStyleValue(N,O,I,i);break}y.set(O,I)}),C||s.push(y),m=y,c=f}),i.length)throw ma(i);return s}function Wi(o,e,t,n){switch(e){case"start":o.onStart(()=>n(t&&Pi(t,"start",o)));break;case"done":o.onDone(()=>n(t&&Pi(t,"done",o)));break;case"destroy":o.onDestroy(()=>n(t&&Pi(t,"destroy",o)));break}}function Pi(o,e,t){let n=t.totalTime,i=!!t.disabled,s=$i(o.element,o.triggerName,o.fromState,o.toState,e||o.phaseName,n??o.totalTime,i),c=o._data;return c!=null&&(s._data=c),s}function $i(o,e,t,n,i="",s=0,c){return{element:o,triggerName:e,fromState:t,toState:n,phaseName:i,totalTime:s,disabled:!!c}}function Ve(o,e,t){let n=o.get(e);return n||o.set(e,n=t),n}function dr(o){let e=o.indexOf(":"),t=o.substring(1,e),n=o.slice(e+1);return[t,n]}var Ca=typeof document>"u"?null:document.documentElement;function Yi(o){let e=o.parentNode||o.host||null;return e===Ca?null:e}function Pa(o){return o.substring(1,6)=="ebkit"}var Pt=null,mr=!1;function Ma(o){Pt||(Pt=wa()||{},mr=Pt.style?"WebkitAppearance"in Pt.style:!1);let e=!0;return Pt.style&&!Pa(o)&&(e=o in Pt.style,!e&&mr&&(e="Webkit"+o.charAt(0).toUpperCase()+o.slice(1)in Pt.style)),e}function wa(){return typeof document<"u"?document.body:null}function Mr(o,e){for(;e;){if(e===o)return!0;e=Yi(e)}return!1}function wr(o,e,t){if(t)return Array.from(o.querySelectorAll(e));let n=o.querySelector(e);return n?[n]:[]}var Hi=(()=>{class o{validateStyleProperty(t){return Ma(t)}containsElement(t,n){return Mr(t,n)}getParentElement(t){return Yi(t)}query(t,n,i){return wr(t,n,i)}computeStyle(t,n,i){return i||""}animate(t,n,i,s,c,m=[],u){return new Nt(i,s)}static \u0275fac=function(n){return new(n||o)};static \u0275prov=be({token:o,factory:o.\u0275fac})}return o})(),Ot=class{static NOOP=new Hi},St=class{};var Oa=1e3,Or="{{",Sa="}}",Sr="ng-enter",ki="ng-leave",Mn="ng-trigger",kn=".ng-trigger",pr="ng-animating",Ti=".ng-animating";function at(o){if(typeof o=="number")return o;let e=o.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:Ii(parseFloat(e[1]),e[2])}function Ii(o,e){switch(e){case"s":return o*Oa;default:return o}}function Tn(o,e,t){return o.hasOwnProperty("duration")?o:Ea(o,e,t)}function Ea(o,e,t){let n=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,i,s=0,c="";if(typeof o=="string"){let m=o.match(n);if(m===null)return e.push(cr(o)),{duration:0,delay:0,easing:""};i=Ii(parseFloat(m[1]),m[2]);let u=m[3];u!=null&&(s=Ii(parseFloat(u),m[4]));let f=m[5];f&&(c=f)}else i=o;if(!t){let m=!1,u=e.length;i<0&&(e.push($r()),m=!0),s<0&&(e.push(Yr()),m=!0),m&&e.splice(u,0,cr(o))}return{duration:i,delay:s,easing:c}}function ka(o){return o.length?o[0]instanceof Map?o:o.map(e=>new Map(Object.entries(e))):[]}function ot(o,e,t){e.forEach((n,i)=>{let s=Zi(i);t&&!t.has(i)&&t.set(i,o.style[s]),o.style[s]=n})}function wt(o,e){e.forEach((t,n)=>{let i=Zi(n);o.style[i]=""})}function Qt(o){return Array.isArray(o)?o.length==1?o[0]:Do(o):o}function Ta(o,e,t){let n=e.params||{},i=Er(o);i.length&&i.forEach(s=>{n.hasOwnProperty(s)||t.push(Hr(s))})}var Di=new RegExp(`${Or}\\s*(.+?)\\s*${Sa}`,"g");function Er(o){let e=[];if(typeof o=="string"){let t;for(;t=Di.exec(o);)e.push(t[1]);Di.lastIndex=0}return e}function Jt(o,e,t){let n=`${o}`,i=n.replace(Di,(s,c)=>{let m=e[c];return m==null&&(t.push(Zr(c)),m=""),m.toString()});return i==n?o:i}var Ia=/-+([a-z0-9])/g;function Zi(o){return o.replace(Ia,(...e)=>e[1].toUpperCase())}function Da(o,e){return o===0||e===0}function Aa(o,e,t){if(t.size&&e.length){let n=e[0],i=[];if(t.forEach((s,c)=>{n.has(c)||i.push(c),n.set(c,s)}),i.length)for(let s=1;s<e.length;s++){let c=e[s];i.forEach(m=>c.set(m,Ki(o,m)))}}return e}function Le(o,e,t){switch(e.type){case W.Trigger:return o.visitTrigger(e,t);case W.State:return o.visitState(e,t);case W.Transition:return o.visitTransition(e,t);case W.Sequence:return o.visitSequence(e,t);case W.Group:return o.visitGroup(e,t);case W.Animate:return o.visitAnimate(e,t);case W.Keyframes:return o.visitKeyframes(e,t);case W.Style:return o.visitStyle(e,t);case W.Reference:return o.visitReference(e,t);case W.AnimateChild:return o.visitAnimateChild(e,t);case W.AnimateRef:return o.visitAnimateRef(e,t);case W.Query:return o.visitQuery(e,t);case W.Stagger:return o.visitStagger(e,t);default:throw Kr(e.type)}}function Ki(o,e){return window.getComputedStyle(o)[e]}var Fa=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),In=class extends St{normalizePropertyName(e,t){return Zi(e)}normalizeStyleValue(e,t,n,i){let s="",c=n.toString().trim();if(Fa.has(t)&&n!==0&&n!=="0")if(typeof n=="number")s="px";else{let m=n.match(/^[+-]?[\d\.]+([a-z]*)$/);m&&m[1].length==0&&i.push(Qr(e,n))}return c+s}};var Dn="*";function Na(o,e){let t=[];return typeof o=="string"?o.split(/\s*,\s*/).forEach(n=>za(n,t,e)):t.push(o),t}function za(o,e,t){if(o[0]==":"){let u=Ra(o,t);if(typeof u=="function"){e.push(u);return}o=u}let n=o.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(n==null||n.length<4)return t.push(la(o)),e;let i=n[1],s=n[2],c=n[3];e.push(gr(i,c));let m=i==Dn&&c==Dn;s[0]=="<"&&!m&&e.push(gr(c,i))}function Ra(o,e){switch(o){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(t,n)=>parseFloat(n)>parseFloat(t);case":decrement":return(t,n)=>parseFloat(n)<parseFloat(t);default:return e.push(ca(o)),"* => *"}}var wn=new Set(["true","1"]),On=new Set(["false","0"]);function gr(o,e){let t=wn.has(o)||On.has(o),n=wn.has(e)||On.has(e);return(i,s)=>{let c=o==Dn||o==i,m=e==Dn||e==s;return!c&&t&&typeof i=="boolean"&&(c=i?wn.has(o):On.has(o)),!m&&n&&typeof s=="boolean"&&(m=s?wn.has(e):On.has(e)),c&&m}}var kr=":self",La=new RegExp(`s*${kr}s*,?`,"g");function Tr(o,e,t,n){return new Ai(o).build(e,t,n)}var ur="",Ai=class{_driver;constructor(e){this._driver=e}build(e,t,n){let i=new Fi(t);return this._resetContextStyleTimingState(i),Le(this,Qt(e),i)}_resetContextStyleTimingState(e){e.currentQuerySelector=ur,e.collectedStyles=new Map,e.collectedStyles.set(ur,new Map),e.currentTime=0}visitTrigger(e,t){let n=t.queryCount=0,i=t.depCount=0,s=[],c=[];return e.name.charAt(0)=="@"&&t.errors.push(Gr()),e.definitions.forEach(m=>{if(this._resetContextStyleTimingState(t),m.type==W.State){let u=m,f=u.name;f.toString().split(/\s*,\s*/).forEach(C=>{u.name=C,s.push(this.visitState(u,t))}),u.name=f}else if(m.type==W.Transition){let u=this.visitTransition(m,t);n+=u.queryCount,i+=u.depCount,c.push(u)}else t.errors.push(Jr())}),{type:W.Trigger,name:e.name,states:s,transitions:c,queryCount:n,depCount:i,options:null}}visitState(e,t){let n=this.visitStyle(e.styles,t),i=e.options&&e.options.params||null;if(n.containsDynamicStyles){let s=new Set,c=i||{};n.styles.forEach(m=>{m instanceof Map&&m.forEach(u=>{Er(u).forEach(f=>{c.hasOwnProperty(f)||s.add(f)})})}),s.size&&t.errors.push(Xr(e.name,[...s.values()]))}return{type:W.State,name:e.name,style:n,options:i?{params:i}:null}}visitTransition(e,t){t.queryCount=0,t.depCount=0;let n=Le(this,Qt(e.animation),t),i=Na(e.expr,t.errors);return{type:W.Transition,matchers:i,animation:n,queryCount:t.queryCount,depCount:t.depCount,options:Mt(e.options)}}visitSequence(e,t){return{type:W.Sequence,steps:e.steps.map(n=>Le(this,n,t)),options:Mt(e.options)}}visitGroup(e,t){let n=t.currentTime,i=0,s=e.steps.map(c=>{t.currentTime=n;let m=Le(this,c,t);return i=Math.max(i,t.currentTime),m});return t.currentTime=i,{type:W.Group,steps:s,options:Mt(e.options)}}visitAnimate(e,t){let n=Ba(e.timings,t.errors);t.currentAnimateTimings=n;let i,s=e.styles?e.styles:ne({});if(s.type==W.Keyframes)i=this.visitKeyframes(s,t);else{let c=e.styles,m=!1;if(!c){m=!0;let f={};n.easing&&(f.easing=n.easing),c=ne(f)}t.currentTime+=n.duration+n.delay;let u=this.visitStyle(c,t);u.isEmptyStep=m,i=u}return t.currentAnimateTimings=null,{type:W.Animate,timings:n,style:i,options:null}}visitStyle(e,t){let n=this._makeStyleAst(e,t);return this._validateStyleAst(n,t),n}_makeStyleAst(e,t){let n=[],i=Array.isArray(e.styles)?e.styles:[e.styles];for(let m of i)typeof m=="string"?m===nt?n.push(m):t.errors.push(ea(m)):n.push(new Map(Object.entries(m)));let s=!1,c=null;return n.forEach(m=>{if(m instanceof Map&&(m.has("easing")&&(c=m.get("easing"),m.delete("easing")),!s)){for(let u of m.values())if(u.toString().indexOf(Or)>=0){s=!0;break}}}),{type:W.Style,styles:n,easing:c,offset:e.offset,containsDynamicStyles:s,options:null}}_validateStyleAst(e,t){let n=t.currentAnimateTimings,i=t.currentTime,s=t.currentTime;n&&s>0&&(s-=n.duration+n.delay),e.styles.forEach(c=>{typeof c!="string"&&c.forEach((m,u)=>{let f=t.collectedStyles.get(t.currentQuerySelector),C=f.get(u),y=!0;C&&(s!=i&&s>=C.startTime&&i<=C.endTime&&(t.errors.push(ta(u,C.startTime,C.endTime,s,i)),y=!1),s=C.startTime),y&&f.set(u,{startTime:s,endTime:i}),t.options&&Ta(m,t.options,t.errors)})})}visitKeyframes(e,t){let n={type:W.Keyframes,styles:[],options:null};if(!t.currentAnimateTimings)return t.errors.push(na()),n;let i=1,s=0,c=[],m=!1,u=!1,f=0,C=e.steps.map(J=>{let X=this._makeStyleAst(J,t),oe=X.offset!=null?X.offset:ja(X.styles),ye=0;return oe!=null&&(s++,ye=X.offset=oe),u=u||ye<0||ye>1,m=m||ye<f,f=ye,c.push(ye),X});u&&t.errors.push(ia()),m&&t.errors.push(oa());let y=e.steps.length,D=0;s>0&&s<y?t.errors.push(ra()):s==0&&(D=i/(y-1));let N=y-1,O=t.currentTime,I=t.currentAnimateTimings,K=I.duration;return C.forEach((J,X)=>{let oe=D>0?X==N?1:D*X:c[X],ye=oe*K;t.currentTime=O+I.delay+ye,I.duration=ye,this._validateStyleAst(J,t),J.offset=oe,n.styles.push(J)}),n}visitReference(e,t){return{type:W.Reference,animation:Le(this,Qt(e.animation),t),options:Mt(e.options)}}visitAnimateChild(e,t){return t.depCount++,{type:W.AnimateChild,options:Mt(e.options)}}visitAnimateRef(e,t){return{type:W.AnimateRef,animation:this.visitReference(e.animation,t),options:Mt(e.options)}}visitQuery(e,t){let n=t.currentQuerySelector,i=e.options||{};t.queryCount++,t.currentQuery=e;let[s,c]=Va(e.selector);t.currentQuerySelector=n.length?n+" "+s:s,Ve(t.collectedStyles,t.currentQuerySelector,new Map);let m=Le(this,Qt(e.animation),t);return t.currentQuery=null,t.currentQuerySelector=n,{type:W.Query,selector:s,limit:i.limit||0,optional:!!i.optional,includeSelf:c,animation:m,originalSelector:e.selector,options:Mt(e.options)}}visitStagger(e,t){t.currentQuery||t.errors.push(aa());let n=e.timings==="full"?{duration:0,delay:0,easing:"full"}:Tn(e.timings,t.errors,!0);return{type:W.Stagger,animation:Le(this,Qt(e.animation),t),timings:n,options:null}}};function Va(o){let e=!!o.split(/\s*,\s*/).find(t=>t==kr);return e&&(o=o.replace(La,"")),o=o.replace(/@\*/g,kn).replace(/@\w+/g,t=>kn+"-"+t.slice(1)).replace(/:animating/g,Ti),[o,e]}function Ua(o){return o?B({},o):null}var Fi=class{errors;queryCount=0;depCount=0;currentTransition=null;currentQuery=null;currentQuerySelector=null;currentAnimateTimings=null;currentTime=0;collectedStyles=new Map;options=null;unsupportedCSSPropertiesFound=new Set;constructor(e){this.errors=e}};function ja(o){if(typeof o=="string")return null;let e=null;if(Array.isArray(o))o.forEach(t=>{if(t instanceof Map&&t.has("offset")){let n=t;e=parseFloat(n.get("offset")),n.delete("offset")}});else if(o instanceof Map&&o.has("offset")){let t=o;e=parseFloat(t.get("offset")),t.delete("offset")}return e}function Ba(o,e){if(o.hasOwnProperty("duration"))return o;if(typeof o=="number"){let s=Tn(o,e).duration;return Mi(s,0,"")}let t=o;if(t.split(/\s+/).some(s=>s.charAt(0)=="{"&&s.charAt(1)=="{")){let s=Mi(0,0,"");return s.dynamic=!0,s.strValue=t,s}let i=Tn(t,e);return Mi(i.duration,i.delay,i.easing)}function Mt(o){return o?(o=B({},o),o.params&&(o.params=Ua(o.params))):o={},o}function Mi(o,e,t){return{duration:o,delay:e,easing:t}}function Qi(o,e,t,n,i,s,c=null,m=!1){return{type:1,element:o,keyframes:e,preStyleProps:t,postStyleProps:n,duration:i,delay:s,totalTime:i+s,easing:c,subTimeline:m}}var Xt=class{_map=new Map;get(e){return this._map.get(e)||[]}append(e,t){let n=this._map.get(e);n||this._map.set(e,n=[]),n.push(...t)}has(e){return this._map.has(e)}clear(){this._map.clear()}},qa=1,Wa=":enter",$a=new RegExp(Wa,"g"),Ya=":leave",Ha=new RegExp(Ya,"g");function Ir(o,e,t,n,i,s=new Map,c=new Map,m,u,f=[]){return new Ni().buildKeyframes(o,e,t,n,i,s,c,m,u,f)}var Ni=class{buildKeyframes(e,t,n,i,s,c,m,u,f,C=[]){f=f||new Xt;let y=new zi(e,t,f,i,s,C,[]);y.options=u;let D=u.delay?at(u.delay):0;y.currentTimeline.delayNextStep(D),y.currentTimeline.setStyles([c],null,y.errors,u),Le(this,n,y);let N=y.timelines.filter(O=>O.containsAnimation());if(N.length&&m.size){let O;for(let I=N.length-1;I>=0;I--){let K=N[I];if(K.element===t){O=K;break}}O&&!O.allowOnlyTimelineStyles()&&O.setStyles([m],null,y.errors,u)}return N.length?N.map(O=>O.buildKeyframes()):[Qi(t,[],[],[],0,D,"",!1)]}visitTrigger(e,t){}visitState(e,t){}visitTransition(e,t){}visitAnimateChild(e,t){let n=t.subInstructions.get(t.element);if(n){let i=t.createSubContext(e.options),s=t.currentTimeline.currentTime,c=this._visitSubInstructions(n,i,i.options);s!=c&&t.transformIntoNewTimeline(c)}t.previousNode=e}visitAnimateRef(e,t){let n=t.createSubContext(e.options);n.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],t,n),this.visitReference(e.animation,n),t.transformIntoNewTimeline(n.currentTimeline.currentTime),t.previousNode=e}_applyAnimationRefDelays(e,t,n){for(let i of e){let s=i?.delay;if(s){let c=typeof s=="number"?s:at(Jt(s,i?.params??{},t.errors));n.delayNextStep(c)}}}_visitSubInstructions(e,t,n){let s=t.currentTimeline.currentTime,c=n.duration!=null?at(n.duration):null,m=n.delay!=null?at(n.delay):null;return c!==0&&e.forEach(u=>{let f=t.appendInstructionToTimeline(u,c,m);s=Math.max(s,f.duration+f.delay)}),s}visitReference(e,t){t.updateOptions(e.options,!0),Le(this,e.animation,t),t.previousNode=e}visitSequence(e,t){let n=t.subContextCount,i=t,s=e.options;if(s&&(s.params||s.delay)&&(i=t.createSubContext(s),i.transformIntoNewTimeline(),s.delay!=null)){i.previousNode.type==W.Style&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=An);let c=at(s.delay);i.delayNextStep(c)}e.steps.length&&(e.steps.forEach(c=>Le(this,c,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>n&&i.transformIntoNewTimeline()),t.previousNode=e}visitGroup(e,t){let n=[],i=t.currentTimeline.currentTime,s=e.options&&e.options.delay?at(e.options.delay):0;e.steps.forEach(c=>{let m=t.createSubContext(e.options);s&&m.delayNextStep(s),Le(this,c,m),i=Math.max(i,m.currentTimeline.currentTime),n.push(m.currentTimeline)}),n.forEach(c=>t.currentTimeline.mergeTimelineCollectedStyles(c)),t.transformIntoNewTimeline(i),t.previousNode=e}_visitTiming(e,t){if(e.dynamic){let n=e.strValue,i=t.params?Jt(n,t.params,t.errors):n;return Tn(i,t.errors)}else return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,t){let n=t.currentAnimateTimings=this._visitTiming(e.timings,t),i=t.currentTimeline;n.delay&&(t.incrementTime(n.delay),i.snapshotCurrentStyles());let s=e.style;s.type==W.Keyframes?this.visitKeyframes(s,t):(t.incrementTime(n.duration),this.visitStyle(s,t),i.applyStylesToKeyframe()),t.currentAnimateTimings=null,t.previousNode=e}visitStyle(e,t){let n=t.currentTimeline,i=t.currentAnimateTimings;!i&&n.hasCurrentStyleProperties()&&n.forwardFrame();let s=i&&i.easing||e.easing;e.isEmptyStep?n.applyEmptyStep(s):n.setStyles(e.styles,s,t.errors,t.options),t.previousNode=e}visitKeyframes(e,t){let n=t.currentAnimateTimings,i=t.currentTimeline.duration,s=n.duration,m=t.createSubContext().currentTimeline;m.easing=n.easing,e.styles.forEach(u=>{let f=u.offset||0;m.forwardTime(f*s),m.setStyles(u.styles,u.easing,t.errors,t.options),m.applyStylesToKeyframe()}),t.currentTimeline.mergeTimelineCollectedStyles(m),t.transformIntoNewTimeline(i+s),t.previousNode=e}visitQuery(e,t){let n=t.currentTimeline.currentTime,i=e.options||{},s=i.delay?at(i.delay):0;s&&(t.previousNode.type===W.Style||n==0&&t.currentTimeline.hasCurrentStyleProperties())&&(t.currentTimeline.snapshotCurrentStyles(),t.previousNode=An);let c=n,m=t.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!i.optional,t.errors);t.currentQueryTotal=m.length;let u=null;m.forEach((f,C)=>{t.currentQueryIndex=C;let y=t.createSubContext(e.options,f);s&&y.delayNextStep(s),f===t.element&&(u=y.currentTimeline),Le(this,e.animation,y),y.currentTimeline.applyStylesToKeyframe();let D=y.currentTimeline.currentTime;c=Math.max(c,D)}),t.currentQueryIndex=0,t.currentQueryTotal=0,t.transformIntoNewTimeline(c),u&&(t.currentTimeline.mergeTimelineCollectedStyles(u),t.currentTimeline.snapshotCurrentStyles()),t.previousNode=e}visitStagger(e,t){let n=t.parentContext,i=t.currentTimeline,s=e.timings,c=Math.abs(s.duration),m=c*(t.currentQueryTotal-1),u=c*t.currentQueryIndex;switch(s.duration<0?"reverse":s.easing){case"reverse":u=m-u;break;case"full":u=n.currentStaggerTime;break}let C=t.currentTimeline;u&&C.delayNextStep(u);let y=C.currentTime;Le(this,e.animation,t),t.previousNode=e,n.currentStaggerTime=i.currentTime-y+(i.startTime-n.currentTimeline.startTime)}},An={},zi=class o{_driver;element;subInstructions;_enterClassName;_leaveClassName;errors;timelines;parentContext=null;currentTimeline;currentAnimateTimings=null;previousNode=An;subContextCount=0;options={};currentQueryIndex=0;currentQueryTotal=0;currentStaggerTime=0;constructor(e,t,n,i,s,c,m,u){this._driver=e,this.element=t,this.subInstructions=n,this._enterClassName=i,this._leaveClassName=s,this.errors=c,this.timelines=m,this.currentTimeline=u||new Fn(this._driver,t,0),m.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,t){if(!e)return;let n=e,i=this.options;n.duration!=null&&(i.duration=at(n.duration)),n.delay!=null&&(i.delay=at(n.delay));let s=n.params;if(s){let c=i.params;c||(c=this.options.params={}),Object.keys(s).forEach(m=>{(!t||!c.hasOwnProperty(m))&&(c[m]=Jt(s[m],c,this.errors))})}}_copyOptions(){let e={};if(this.options){let t=this.options.params;if(t){let n=e.params={};Object.keys(t).forEach(i=>{n[i]=t[i]})}}return e}createSubContext(e=null,t,n){let i=t||this.element,s=new o(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,n||0));return s.previousNode=this.previousNode,s.currentAnimateTimings=this.currentAnimateTimings,s.options=this._copyOptions(),s.updateOptions(e),s.currentQueryIndex=this.currentQueryIndex,s.currentQueryTotal=this.currentQueryTotal,s.parentContext=this,this.subContextCount++,s}transformIntoNewTimeline(e){return this.previousNode=An,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,t,n){let i={duration:t??e.duration,delay:this.currentTimeline.currentTime+(n??0)+e.delay,easing:""},s=new Ri(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,i,e.stretchStartingKeyframe);return this.timelines.push(s),i}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,t,n,i,s,c){let m=[];if(i&&m.push(this.element),e.length>0){e=e.replace($a,"."+this._enterClassName),e=e.replace(Ha,"."+this._leaveClassName);let u=n!=1,f=this._driver.query(this.element,e,u);n!==0&&(f=n<0?f.slice(f.length+n,f.length):f.slice(0,n)),m.push(...f)}return!s&&m.length==0&&c.push(sa(t)),m}},Fn=class o{_driver;element;startTime;_elementTimelineStylesLookup;duration=0;easing=null;_previousKeyframe=new Map;_currentKeyframe=new Map;_keyframes=new Map;_styleSummary=new Map;_localTimelineStyles=new Map;_globalTimelineStyles;_pendingStyles=new Map;_backFill=new Map;_currentEmptyStepKeyframe=null;constructor(e,t,n,i){this._driver=e,this.element=t,this.startTime=n,this._elementTimelineStylesLookup=i,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(t),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(t,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){let t=this._keyframes.size===1&&this._pendingStyles.size;this.duration||t?(this.forwardTime(this.currentTime+e),t&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,t){return this.applyStylesToKeyframe(),new o(this._driver,e,t||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=qa,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,t){this._localTimelineStyles.set(e,t),this._globalTimelineStyles.set(e,t),this._styleSummary.set(e,{time:this.currentTime,value:t})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[t,n]of this._globalTimelineStyles)this._backFill.set(t,n||nt),this._currentKeyframe.set(t,nt);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,t,n,i){t&&this._previousKeyframe.set("easing",t);let s=i&&i.params||{},c=Za(e,this._globalTimelineStyles);for(let[m,u]of c){let f=Jt(u,s,n);this._pendingStyles.set(m,f),this._localTimelineStyles.has(m)||this._backFill.set(m,this._globalTimelineStyles.get(m)??nt),this._updateStyle(m,f)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((e,t)=>{this._currentKeyframe.set(t,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,t)=>{this._currentKeyframe.has(t)||this._currentKeyframe.set(t,e)}))}snapshotCurrentStyles(){for(let[e,t]of this._localTimelineStyles)this._pendingStyles.set(e,t),this._updateStyle(e,t)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let e=[];for(let t in this._currentKeyframe)e.push(t);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((t,n)=>{let i=this._styleSummary.get(n);(!i||t.time>i.time)&&this._updateStyle(n,t.value)})}buildKeyframes(){this.applyStylesToKeyframe();let e=new Set,t=new Set,n=this._keyframes.size===1&&this.duration===0,i=[];this._keyframes.forEach((m,u)=>{let f=new Map([...this._backFill,...m]);f.forEach((C,y)=>{C===hn?e.add(y):C===nt&&t.add(y)}),n||f.set("offset",u/this.duration),i.push(f)});let s=[...e.values()],c=[...t.values()];if(n){let m=i[0],u=new Map(m);m.set("offset",0),u.set("offset",1),i=[m,u]}return Qi(this.element,i,s,c,this.duration,this.startTime,this.easing,!1)}},Ri=class extends Fn{keyframes;preStyleProps;postStyleProps;_stretchStartingKeyframe;timings;constructor(e,t,n,i,s,c,m=!1){super(e,t,c.delay),this.keyframes=n,this.preStyleProps=i,this.postStyleProps=s,this._stretchStartingKeyframe=m,this.timings={duration:c.duration,delay:c.delay,easing:c.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:t,duration:n,easing:i}=this.timings;if(this._stretchStartingKeyframe&&t){let s=[],c=n+t,m=t/c,u=new Map(e[0]);u.set("offset",0),s.push(u);let f=new Map(e[0]);f.set("offset",fr(m)),s.push(f);let C=e.length-1;for(let y=1;y<=C;y++){let D=new Map(e[y]),N=D.get("offset"),O=t+N*n;D.set("offset",fr(O/c)),s.push(D)}n=c,t=0,i="",e=s}return Qi(this.element,e,this.preStyleProps,this.postStyleProps,n,t,i,!0)}};function fr(o,e=3){let t=Math.pow(10,e-1);return Math.round(o*t)/t}function Za(o,e){let t=new Map,n;return o.forEach(i=>{if(i==="*"){n??=e.keys();for(let s of n)t.set(s,nt)}else for(let[s,c]of i)t.set(s,c)}),t}function hr(o,e,t,n,i,s,c,m,u,f,C,y,D){return{type:0,element:o,triggerName:e,isRemovalTransition:i,fromState:t,fromStyles:s,toState:n,toStyles:c,timelines:m,queriedElements:u,preStyleProps:f,postStyleProps:C,totalTime:y,errors:D}}var wi={},Nn=class{_triggerName;ast;_stateStyles;constructor(e,t,n){this._triggerName=e,this.ast=t,this._stateStyles=n}match(e,t,n,i){return Ka(this.ast.matchers,e,t,n,i)}buildStyles(e,t,n){let i=this._stateStyles.get("*");return e!==void 0&&(i=this._stateStyles.get(e?.toString())||i),i?i.buildStyles(t,n):new Map}build(e,t,n,i,s,c,m,u,f,C){let y=[],D=this.ast.options&&this.ast.options.params||wi,N=m&&m.params||wi,O=this.buildStyles(n,N,y),I=u&&u.params||wi,K=this.buildStyles(i,I,y),J=new Set,X=new Map,oe=new Map,ye=i==="void",Et={params:Dr(I,D),delay:this.ast.options?.delay},Je=C?[]:Ir(e,t,this.ast.animation,s,c,O,K,Et,f,y),Ee=0;return Je.forEach(Ie=>{Ee=Math.max(Ie.duration+Ie.delay,Ee)}),y.length?hr(t,this._triggerName,n,i,ye,O,K,[],[],X,oe,Ee,y):(Je.forEach(Ie=>{let vt=Ie.element,kt=Ve(X,vt,new Set);Ie.preStyleProps.forEach(xt=>kt.add(xt));let Xi=Ve(oe,vt,new Set);Ie.postStyleProps.forEach(xt=>Xi.add(xt)),vt!==t&&J.add(vt)}),hr(t,this._triggerName,n,i,ye,O,K,Je,[...J.values()],X,oe,Ee))}};function Ka(o,e,t,n,i){return o.some(s=>s(e,t,n,i))}function Dr(o,e){let t=B({},e);return Object.entries(o).forEach(([n,i])=>{i!=null&&(t[n]=i)}),t}var Li=class{styles;defaultParams;normalizer;constructor(e,t,n){this.styles=e,this.defaultParams=t,this.normalizer=n}buildStyles(e,t){let n=new Map,i=Dr(e,this.defaultParams);return this.styles.styles.forEach(s=>{typeof s!="string"&&s.forEach((c,m)=>{c&&(c=Jt(c,i,t));let u=this.normalizer.normalizePropertyName(m,t);c=this.normalizer.normalizeStyleValue(m,u,c,t),n.set(m,c)})}),n}};function Qa(o,e,t){return new Vi(o,e,t)}var Vi=class{name;ast;_normalizer;transitionFactories=[];fallbackTransition;states=new Map;constructor(e,t,n){this.name=e,this.ast=t,this._normalizer=n,t.states.forEach(i=>{let s=i.options&&i.options.params||{};this.states.set(i.name,new Li(i.style,s,n))}),br(this.states,"true","1"),br(this.states,"false","0"),t.transitions.forEach(i=>{this.transitionFactories.push(new Nn(e,i,this.states))}),this.fallbackTransition=Ga(e,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,t,n,i){return this.transitionFactories.find(c=>c.match(e,t,n,i))||null}matchStyles(e,t,n){return this.fallbackTransition.buildStyles(e,t,n)}};function Ga(o,e,t){let n=[(c,m)=>!0],i={type:W.Sequence,steps:[],options:null},s={type:W.Transition,animation:i,matchers:n,options:null,queryCount:0,depCount:0};return new Nn(o,s,e)}function br(o,e,t){o.has(e)?o.has(t)||o.set(t,o.get(e)):o.has(t)&&o.set(e,o.get(t))}var Ja=new Xt,Ui=class{bodyNode;_driver;_normalizer;_animations=new Map;_playersById=new Map;players=[];constructor(e,t,n){this.bodyNode=e,this._driver=t,this._normalizer=n}register(e,t){let n=[],i=[],s=Tr(this._driver,t,n,i);if(n.length)throw pa(n);this._animations.set(e,s)}_buildPlayer(e,t,n){let i=e.element,s=Pr(this._normalizer,e.keyframes,t,n);return this._driver.animate(i,s,e.duration,e.delay,e.easing,[],!0)}create(e,t,n={}){let i=[],s=this._animations.get(e),c,m=new Map;if(s?(c=Ir(this._driver,t,s,Sr,ki,new Map,new Map,n,Ja,i),c.forEach(C=>{let y=Ve(m,C.element,new Map);C.postStyleProps.forEach(D=>y.set(D,null))})):(i.push(ga()),c=[]),i.length)throw ua(i);m.forEach((C,y)=>{C.forEach((D,N)=>{C.set(N,this._driver.computeStyle(y,N,nt))})});let u=c.map(C=>{let y=m.get(C.element);return this._buildPlayer(C,new Map,y)}),f=bt(u);return this._playersById.set(e,f),f.onDestroy(()=>this.destroy(e)),this.players.push(f),f}destroy(e){let t=this._getPlayer(e);t.destroy(),this._playersById.delete(e);let n=this.players.indexOf(t);n>=0&&this.players.splice(n,1)}_getPlayer(e){let t=this._playersById.get(e);if(!t)throw fa(e);return t}listen(e,t,n,i){let s=$i(t,"","","");return Wi(this._getPlayer(e),n,s,i),()=>{}}command(e,t,n,i){if(n=="register"){this.register(e,i[0]);return}if(n=="create"){let c=i[0]||{};this.create(e,t,c);return}let s=this._getPlayer(e);switch(n){case"play":s.play();break;case"pause":s.pause();break;case"reset":s.reset();break;case"restart":s.restart();break;case"finish":s.finish();break;case"init":s.init();break;case"setPosition":s.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(e);break}}},_r="ng-animate-queued",Xa=".ng-animate-queued",Oi="ng-animate-disabled",es=".ng-animate-disabled",ts="ng-star-inserted",ns=".ng-star-inserted",is=[],Ar={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},os={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},Ge="__ng_removed",en=class{namespaceId;value;options;get params(){return this.options.params}constructor(e,t=""){this.namespaceId=t;let n=e&&e.hasOwnProperty("value"),i=n?e.value:e;if(this.value=as(i),n){let s=e,{value:c}=s,m=io(s,["value"]);this.options=m}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){let t=e.params;if(t){let n=this.options.params;Object.keys(t).forEach(i=>{n[i]==null&&(n[i]=t[i])})}}},Gt="void",Si=new en(Gt),ji=class{id;hostElement;_engine;players=[];_triggers=new Map;_queue=[];_elementListeners=new Map;_hostClassName;constructor(e,t,n){this.id=e,this.hostElement=t,this._engine=n,this._hostClassName="ng-tns-"+e,Ze(t,this._hostClassName)}listen(e,t,n,i){if(!this._triggers.has(t))throw ha(n,t);if(n==null||n.length==0)throw ba(t);if(!ss(n))throw _a(n,t);let s=Ve(this._elementListeners,e,[]),c={name:t,phase:n,callback:i};s.push(c);let m=Ve(this._engine.statesByElement,e,new Map);return m.has(t)||(Ze(e,Mn),Ze(e,Mn+"-"+t),m.set(t,Si)),()=>{this._engine.afterFlush(()=>{let u=s.indexOf(c);u>=0&&s.splice(u,1),this._triggers.has(t)||m.delete(t)})}}register(e,t){return this._triggers.has(e)?!1:(this._triggers.set(e,t),!0)}_getTrigger(e){let t=this._triggers.get(e);if(!t)throw va(e);return t}trigger(e,t,n,i=!0){let s=this._getTrigger(t),c=new tn(this.id,t,e),m=this._engine.statesByElement.get(e);m||(Ze(e,Mn),Ze(e,Mn+"-"+t),this._engine.statesByElement.set(e,m=new Map));let u=m.get(t),f=new en(n,this.id);if(!(n&&n.hasOwnProperty("value"))&&u&&f.absorbOptions(u.options),m.set(t,f),u||(u=Si),!(f.value===Gt)&&u.value===f.value){if(!ds(u.params,f.params)){let I=[],K=s.matchStyles(u.value,u.params,I),J=s.matchStyles(f.value,f.params,I);I.length?this._engine.reportError(I):this._engine.afterFlush(()=>{wt(e,K),ot(e,J)})}return}let D=Ve(this._engine.playersByElement,e,[]);D.forEach(I=>{I.namespaceId==this.id&&I.triggerName==t&&I.queued&&I.destroy()});let N=s.matchTransition(u.value,f.value,e,f.params),O=!1;if(!N){if(!i)return;N=s.fallbackTransition,O=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:t,transition:N,fromState:u,toState:f,player:c,isFallbackTransition:O}),O||(Ze(e,_r),c.onStart(()=>{jt(e,_r)})),c.onDone(()=>{let I=this.players.indexOf(c);I>=0&&this.players.splice(I,1);let K=this._engine.playersByElement.get(e);if(K){let J=K.indexOf(c);J>=0&&K.splice(J,1)}}),this.players.push(c),D.push(c),c}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(t=>t.delete(e)),this._elementListeners.forEach((t,n)=>{this._elementListeners.set(n,t.filter(i=>i.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);let t=this._engine.playersByElement.get(e);t&&(t.forEach(n=>n.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,t){let n=this._engine.driver.query(e,kn,!0);n.forEach(i=>{if(i[Ge])return;let s=this._engine.fetchNamespacesByElement(i);s.size?s.forEach(c=>c.triggerLeaveAnimation(i,t,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>n.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(e,t,n,i){let s=this._engine.statesByElement.get(e),c=new Map;if(s){let m=[];if(s.forEach((u,f)=>{if(c.set(f,u.value),this._triggers.has(f)){let C=this.trigger(e,f,Gt,i);C&&m.push(C)}}),m.length)return this._engine.markElementAsRemoved(this.id,e,!0,t,c),n&&bt(m).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){let t=this._elementListeners.get(e),n=this._engine.statesByElement.get(e);if(t&&n){let i=new Set;t.forEach(s=>{let c=s.name;if(i.has(c))return;i.add(c);let u=this._triggers.get(c).fallbackTransition,f=n.get(c)||Si,C=new en(Gt),y=new tn(this.id,c,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:c,transition:u,fromState:f,toState:C,player:y,isFallbackTransition:!0})})}}removeNode(e,t){let n=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,t),this.triggerLeaveAnimation(e,t,!0))return;let i=!1;if(n.totalAnimations){let s=n.players.length?n.playersByQueriedElement.get(e):[];if(s&&s.length)i=!0;else{let c=e;for(;c=c.parentNode;)if(n.statesByElement.get(c)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(e),i)n.markElementAsRemoved(this.id,e,!1,t);else{let s=e[Ge];(!s||s===Ar)&&(n.afterFlush(()=>this.clearElementCache(e)),n.destroyInnerAnimations(e),n._onRemovalComplete(e,t))}}insertNode(e,t){Ze(e,this._hostClassName)}drainQueuedTransitions(e){let t=[];return this._queue.forEach(n=>{let i=n.player;if(i.destroyed)return;let s=n.element,c=this._elementListeners.get(s);c&&c.forEach(m=>{if(m.name==n.triggerName){let u=$i(s,n.triggerName,n.fromState.value,n.toState.value);u._data=e,Wi(n.player,m.phase,u,m.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):t.push(n)}),this._queue=[],t.sort((n,i)=>{let s=n.transition.ast.depCount,c=i.transition.ast.depCount;return s==0||c==0?s-c:this._engine.driver.containsElement(n.element,i.element)?1:-1})}destroy(e){this.players.forEach(t=>t.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}},Bi=class{bodyNode;driver;_normalizer;players=[];newHostElements=new Map;playersByElement=new Map;playersByQueriedElement=new Map;statesByElement=new Map;disabledNodes=new Set;totalAnimations=0;totalQueuedPlayers=0;_namespaceLookup={};_namespaceList=[];_flushFns=[];_whenQuietFns=[];namespacesByHostElement=new Map;collectedEnterElements=[];collectedLeaveElements=[];onRemovalComplete=(e,t)=>{};_onRemovalComplete(e,t){this.onRemovalComplete(e,t)}constructor(e,t,n){this.bodyNode=e,this.driver=t,this._normalizer=n}get queuedPlayers(){let e=[];return this._namespaceList.forEach(t=>{t.players.forEach(n=>{n.queued&&e.push(n)})}),e}createNamespace(e,t){let n=new ji(e,t,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,t)?this._balanceNamespaceList(n,t):(this.newHostElements.set(t,n),this.collectEnterElement(t)),this._namespaceLookup[e]=n}_balanceNamespaceList(e,t){let n=this._namespaceList,i=this.namespacesByHostElement;if(n.length-1>=0){let c=!1,m=this.driver.getParentElement(t);for(;m;){let u=i.get(m);if(u){let f=n.indexOf(u);n.splice(f+1,0,e),c=!0;break}m=this.driver.getParentElement(m)}c||n.unshift(e)}else n.push(e);return i.set(t,e),e}register(e,t){let n=this._namespaceLookup[e];return n||(n=this.createNamespace(e,t)),n}registerTrigger(e,t,n){let i=this._namespaceLookup[e];i&&i.register(t,n)&&this.totalAnimations++}destroy(e,t){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let n=this._fetchNamespace(e);this.namespacesByHostElement.delete(n.hostElement);let i=this._namespaceList.indexOf(n);i>=0&&this._namespaceList.splice(i,1),n.destroy(t),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){let t=new Set,n=this.statesByElement.get(e);if(n){for(let i of n.values())if(i.namespaceId){let s=this._fetchNamespace(i.namespaceId);s&&t.add(s)}}return t}trigger(e,t,n,i){if(Sn(t)){let s=this._fetchNamespace(e);if(s)return s.trigger(t,n,i),!0}return!1}insertNode(e,t,n,i){if(!Sn(t))return;let s=t[Ge];if(s&&s.setForRemoval){s.setForRemoval=!1,s.setForMove=!0;let c=this.collectedLeaveElements.indexOf(t);c>=0&&this.collectedLeaveElements.splice(c,1)}if(e){let c=this._fetchNamespace(e);c&&c.insertNode(t,n)}i&&this.collectEnterElement(t)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,t){t?this.disabledNodes.has(e)||(this.disabledNodes.add(e),Ze(e,Oi)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),jt(e,Oi))}removeNode(e,t,n){if(Sn(t)){let i=e?this._fetchNamespace(e):null;i?i.removeNode(t,n):this.markElementAsRemoved(e,t,!1,n);let s=this.namespacesByHostElement.get(t);s&&s.id!==e&&s.removeNode(t,n)}else this._onRemovalComplete(t,n)}markElementAsRemoved(e,t,n,i,s){this.collectedLeaveElements.push(t),t[Ge]={namespaceId:e,setForRemoval:i,hasAnimation:n,removedBeforeQueried:!1,previousTriggersValues:s}}listen(e,t,n,i,s){return Sn(t)?this._fetchNamespace(e).listen(t,n,i,s):()=>{}}_buildInstruction(e,t,n,i,s){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,n,i,e.fromState.options,e.toState.options,t,s)}destroyInnerAnimations(e){let t=this.driver.query(e,kn,!0);t.forEach(n=>this.destroyActiveAnimationsForElement(n)),this.playersByQueriedElement.size!=0&&(t=this.driver.query(e,Ti,!0),t.forEach(n=>this.finishActiveQueriedAnimationOnElement(n)))}destroyActiveAnimationsForElement(e){let t=this.playersByElement.get(e);t&&t.forEach(n=>{n.queued?n.markedForDestroy=!0:n.destroy()})}finishActiveQueriedAnimationOnElement(e){let t=this.playersByQueriedElement.get(e);t&&t.forEach(n=>n.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return bt(this.players).onDone(()=>e());e()})}processLeaveNode(e){let t=e[Ge];if(t&&t.setForRemoval){if(e[Ge]=Ar,t.namespaceId){this.destroyInnerAnimations(e);let n=this._fetchNamespace(t.namespaceId);n&&n.clearElementCache(e)}this._onRemovalComplete(e,t.setForRemoval)}e.classList?.contains(Oi)&&this.markElementAsDisabled(e,!1),this.driver.query(e,es,!0).forEach(n=>{this.markElementAsDisabled(n,!1)})}flush(e=-1){let t=[];if(this.newHostElements.size&&(this.newHostElements.forEach((n,i)=>this._balanceNamespaceList(n,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let n=0;n<this.collectedEnterElements.length;n++){let i=this.collectedEnterElements[n];Ze(i,ts)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let n=[];try{t=this._flushAnimations(n,e)}finally{for(let i=0;i<n.length;i++)n[i]()}}else for(let n=0;n<this.collectedLeaveElements.length;n++){let i=this.collectedLeaveElements[n];this.processLeaveNode(i)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(n=>n()),this._flushFns=[],this._whenQuietFns.length){let n=this._whenQuietFns;this._whenQuietFns=[],t.length?bt(t).onDone(()=>{n.forEach(i=>i())}):n.forEach(i=>i())}}reportError(e){throw xa(e)}_flushAnimations(e,t){let n=new Xt,i=[],s=new Map,c=[],m=new Map,u=new Map,f=new Map,C=new Set;this.disabledNodes.forEach(w=>{C.add(w);let k=this.driver.query(w,Xa,!0);for(let F=0;F<k.length;F++)C.add(k[F])});let y=this.bodyNode,D=Array.from(this.statesByElement.keys()),N=yr(D,this.collectedEnterElements),O=new Map,I=0;N.forEach((w,k)=>{let F=Sr+I++;O.set(k,F),w.forEach($=>Ze($,F))});let K=[],J=new Set,X=new Set;for(let w=0;w<this.collectedLeaveElements.length;w++){let k=this.collectedLeaveElements[w],F=k[Ge];F&&F.setForRemoval&&(K.push(k),J.add(k),F.hasAnimation?this.driver.query(k,ns,!0).forEach($=>J.add($)):X.add(k))}let oe=new Map,ye=yr(D,Array.from(J));ye.forEach((w,k)=>{let F=ki+I++;oe.set(k,F),w.forEach($=>Ze($,F))}),e.push(()=>{N.forEach((w,k)=>{let F=O.get(k);w.forEach($=>jt($,F))}),ye.forEach((w,k)=>{let F=oe.get(k);w.forEach($=>jt($,F))}),K.forEach(w=>{this.processLeaveNode(w)})});let Et=[],Je=[];for(let w=this._namespaceList.length-1;w>=0;w--)this._namespaceList[w].drainQueuedTransitions(t).forEach(F=>{let $=F.player,he=F.element;if(Et.push($),this.collectedEnterElements.length){let Me=he[Ge];if(Me&&Me.setForMove){if(Me.previousTriggersValues&&Me.previousTriggersValues.has(F.triggerName)){let yt=Me.previousTriggersValues.get(F.triggerName),Ue=this.statesByElement.get(F.element);if(Ue&&Ue.has(F.triggerName)){let rn=Ue.get(F.triggerName);rn.value=yt,Ue.set(F.triggerName,rn)}}$.destroy();return}}let Xe=!y||!this.driver.containsElement(y,he),Ne=oe.get(he),st=O.get(he),te=this._buildInstruction(F,n,st,Ne,Xe);if(te.errors&&te.errors.length){Je.push(te);return}if(Xe){$.onStart(()=>wt(he,te.fromStyles)),$.onDestroy(()=>ot(he,te.toStyles)),i.push($);return}if(F.isFallbackTransition){$.onStart(()=>wt(he,te.fromStyles)),$.onDestroy(()=>ot(he,te.toStyles)),i.push($);return}let no=[];te.timelines.forEach(Me=>{Me.stretchStartingKeyframe=!0,this.disabledNodes.has(Me.element)||no.push(Me)}),te.timelines=no,n.append(he,te.timelines);let Wr={instruction:te,player:$,element:he};c.push(Wr),te.queriedElements.forEach(Me=>Ve(m,Me,[]).push($)),te.preStyleProps.forEach((Me,yt)=>{if(Me.size){let Ue=u.get(yt);Ue||u.set(yt,Ue=new Set),Me.forEach((rn,fi)=>Ue.add(fi))}}),te.postStyleProps.forEach((Me,yt)=>{let Ue=f.get(yt);Ue||f.set(yt,Ue=new Set),Me.forEach((rn,fi)=>Ue.add(fi))})});if(Je.length){let w=[];Je.forEach(k=>{w.push(ya(k.triggerName,k.errors))}),Et.forEach(k=>k.destroy()),this.reportError(w)}let Ee=new Map,Ie=new Map;c.forEach(w=>{let k=w.element;n.has(k)&&(Ie.set(k,k),this._beforeAnimationBuild(w.player.namespaceId,w.instruction,Ee))}),i.forEach(w=>{let k=w.element;this._getPreviousPlayers(k,!1,w.namespaceId,w.triggerName,null).forEach($=>{Ve(Ee,k,[]).push($),$.destroy()})});let vt=K.filter(w=>Cr(w,u,f)),kt=new Map;xr(kt,this.driver,X,f,nt).forEach(w=>{Cr(w,u,f)&&vt.push(w)});let xt=new Map;N.forEach((w,k)=>{xr(xt,this.driver,new Set(w),u,hn)}),vt.forEach(w=>{let k=kt.get(w),F=xt.get(w);kt.set(w,new Map([...k?.entries()??[],...F?.entries()??[]]))});let ui=[],eo=[],to={};c.forEach(w=>{let{element:k,player:F,instruction:$}=w;if(n.has(k)){if(C.has(k)){F.onDestroy(()=>ot(k,$.toStyles)),F.disabled=!0,F.overrideTotalTime($.totalTime),i.push(F);return}let he=to;if(Ie.size>1){let Ne=k,st=[];for(;Ne=Ne.parentNode;){let te=Ie.get(Ne);if(te){he=te;break}st.push(Ne)}st.forEach(te=>Ie.set(te,he))}let Xe=this._buildAnimation(F.namespaceId,$,Ee,s,xt,kt);if(F.setRealPlayer(Xe),he===to)ui.push(F);else{let Ne=this.playersByElement.get(he);Ne&&Ne.length&&(F.parentPlayer=bt(Ne)),i.push(F)}}else wt(k,$.fromStyles),F.onDestroy(()=>ot(k,$.toStyles)),eo.push(F),C.has(k)&&i.push(F)}),eo.forEach(w=>{let k=s.get(w.element);if(k&&k.length){let F=bt(k);w.setRealPlayer(F)}}),i.forEach(w=>{w.parentPlayer?w.syncPlayerEvents(w.parentPlayer):w.destroy()});for(let w=0;w<K.length;w++){let k=K[w],F=k[Ge];if(jt(k,ki),F&&F.hasAnimation)continue;let $=[];if(m.size){let Xe=m.get(k);Xe&&Xe.length&&$.push(...Xe);let Ne=this.driver.query(k,Ti,!0);for(let st=0;st<Ne.length;st++){let te=m.get(Ne[st]);te&&te.length&&$.push(...te)}}let he=$.filter(Xe=>!Xe.destroyed);he.length?ls(this,k,he):this.processLeaveNode(k)}return K.length=0,ui.forEach(w=>{this.players.push(w),w.onDone(()=>{w.destroy();let k=this.players.indexOf(w);this.players.splice(k,1)}),w.play()}),ui}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,t,n,i,s){let c=[];if(t){let m=this.playersByQueriedElement.get(e);m&&(c=m)}else{let m=this.playersByElement.get(e);if(m){let u=!s||s==Gt;m.forEach(f=>{f.queued||!u&&f.triggerName!=i||c.push(f)})}}return(n||i)&&(c=c.filter(m=>!(n&&n!=m.namespaceId||i&&i!=m.triggerName))),c}_beforeAnimationBuild(e,t,n){let i=t.triggerName,s=t.element,c=t.isRemovalTransition?void 0:e,m=t.isRemovalTransition?void 0:i;for(let u of t.timelines){let f=u.element,C=f!==s,y=Ve(n,f,[]);this._getPreviousPlayers(f,C,c,m,t.toState).forEach(N=>{let O=N.getRealPlayer();O.beforeDestroy&&O.beforeDestroy(),N.destroy(),y.push(N)})}wt(s,t.fromStyles)}_buildAnimation(e,t,n,i,s,c){let m=t.triggerName,u=t.element,f=[],C=new Set,y=new Set,D=t.timelines.map(O=>{let I=O.element;C.add(I);let K=I[Ge];if(K&&K.removedBeforeQueried)return new Nt(O.duration,O.delay);let J=I!==u,X=cs((n.get(I)||is).map(Ee=>Ee.getRealPlayer())).filter(Ee=>{let Ie=Ee;return Ie.element?Ie.element===I:!1}),oe=s.get(I),ye=c.get(I),Et=Pr(this._normalizer,O.keyframes,oe,ye),Je=this._buildPlayer(O,Et,X);if(O.subTimeline&&i&&y.add(I),J){let Ee=new tn(e,m,I);Ee.setRealPlayer(Je),f.push(Ee)}return Je});f.forEach(O=>{Ve(this.playersByQueriedElement,O.element,[]).push(O),O.onDone(()=>rs(this.playersByQueriedElement,O.element,O))}),C.forEach(O=>Ze(O,pr));let N=bt(D);return N.onDestroy(()=>{C.forEach(O=>jt(O,pr)),ot(u,t.toStyles)}),y.forEach(O=>{Ve(i,O,[]).push(N)}),N}_buildPlayer(e,t,n){return t.length>0?this.driver.animate(e.element,t,e.duration,e.delay,e.easing,n):new Nt(e.duration,e.delay)}},tn=class{namespaceId;triggerName;element;_player=new Nt;_containsRealPlayer=!1;_queuedCallbacks=new Map;destroyed=!1;parentPlayer=null;markedForDestroy=!1;disabled=!1;queued=!0;totalTime=0;constructor(e,t,n){this.namespaceId=e,this.triggerName=t,this.element=n}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((t,n)=>{t.forEach(i=>Wi(e,n,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){let t=this._player;t.triggerCallback&&e.onStart(()=>t.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,t){Ve(this._queuedCallbacks,e,[]).push(t)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){let t=this._player;t.triggerCallback&&t.triggerCallback(e)}};function rs(o,e,t){let n=o.get(e);if(n){if(n.length){let i=n.indexOf(t);n.splice(i,1)}n.length==0&&o.delete(e)}return n}function as(o){return o??null}function Sn(o){return o&&o.nodeType===1}function ss(o){return o=="start"||o=="done"}function vr(o,e){let t=o.style.display;return o.style.display=e??"none",t}function xr(o,e,t,n,i){let s=[];t.forEach(u=>s.push(vr(u)));let c=[];n.forEach((u,f)=>{let C=new Map;u.forEach(y=>{let D=e.computeStyle(f,y,i);C.set(y,D),(!D||D.length==0)&&(f[Ge]=os,c.push(f))}),o.set(f,C)});let m=0;return t.forEach(u=>vr(u,s[m++])),c}function yr(o,e){let t=new Map;if(o.forEach(m=>t.set(m,[])),e.length==0)return t;let n=1,i=new Set(e),s=new Map;function c(m){if(!m)return n;let u=s.get(m);if(u)return u;let f=m.parentNode;return t.has(f)?u=f:i.has(f)?u=n:u=c(f),s.set(m,u),u}return e.forEach(m=>{let u=c(m);u!==n&&t.get(u).push(m)}),t}function Ze(o,e){o.classList?.add(e)}function jt(o,e){o.classList?.remove(e)}function ls(o,e,t){bt(t).onDone(()=>o.processLeaveNode(e))}function cs(o){let e=[];return Fr(o,e),e}function Fr(o,e){for(let t=0;t<o.length;t++){let n=o[t];n instanceof vi?Fr(n.players,e):e.push(n)}}function ds(o,e){let t=Object.keys(o),n=Object.keys(e);if(t.length!=n.length)return!1;for(let i=0;i<t.length;i++){let s=t[i];if(!e.hasOwnProperty(s)||o[s]!==e[s])return!1}return!0}function Cr(o,e,t){let n=t.get(o);if(!n)return!1;let i=e.get(o);return i?n.forEach(s=>i.add(s)):e.set(o,n),t.delete(o),!0}var Bt=class{_driver;_normalizer;_transitionEngine;_timelineEngine;_triggerCache={};onRemovalComplete=(e,t)=>{};constructor(e,t,n){this._driver=t,this._normalizer=n,this._transitionEngine=new Bi(e.body,t,n),this._timelineEngine=new Ui(e.body,t,n),this._transitionEngine.onRemovalComplete=(i,s)=>this.onRemovalComplete(i,s)}registerTrigger(e,t,n,i,s){let c=e+"-"+i,m=this._triggerCache[c];if(!m){let u=[],f=[],C=Tr(this._driver,s,u,f);if(u.length)throw da(i,u);m=Qa(i,C,this._normalizer),this._triggerCache[c]=m}this._transitionEngine.registerTrigger(t,i,m)}register(e,t){this._transitionEngine.register(e,t)}destroy(e,t){this._transitionEngine.destroy(e,t)}onInsert(e,t,n,i){this._transitionEngine.insertNode(e,t,n,i)}onRemove(e,t,n){this._transitionEngine.removeNode(e,t,n)}disableAnimations(e,t){this._transitionEngine.markElementAsDisabled(e,t)}process(e,t,n,i){if(n.charAt(0)=="@"){let[s,c]=dr(n),m=i;this._timelineEngine.command(s,t,c,m)}else this._transitionEngine.trigger(e,t,n,i)}listen(e,t,n,i,s){if(n.charAt(0)=="@"){let[c,m]=dr(n);return this._timelineEngine.listen(c,t,m,s)}return this._transitionEngine.listen(e,t,n,i,s)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}};function ms(o,e){let t=null,n=null;return Array.isArray(e)&&e.length?(t=Ei(e[0]),e.length>1&&(n=Ei(e[e.length-1]))):e instanceof Map&&(t=Ei(e)),t||n?new ps(o,t,n):null}var ps=(()=>{class o{_element;_startStyles;_endStyles;static initialStylesByElement=new WeakMap;_state=0;_initialStyles;constructor(t,n,i){this._element=t,this._startStyles=n,this._endStyles=i;let s=o.initialStylesByElement.get(t);s||o.initialStylesByElement.set(t,s=new Map),this._initialStyles=s}start(){this._state<1&&(this._startStyles&&ot(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(ot(this._element,this._initialStyles),this._endStyles&&(ot(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(o.initialStylesByElement.delete(this._element),this._startStyles&&(wt(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(wt(this._element,this._endStyles),this._endStyles=null),ot(this._element,this._initialStyles),this._state=3)}}return o})();function Ei(o){let e=null;return o.forEach((t,n)=>{gs(n)&&(e=e||new Map,e.set(n,t))}),e}function gs(o){return o==="display"||o==="position"}var zn=class{element;keyframes;options;_specialStyles;_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_duration;_delay;_initialized=!1;_finished=!1;_started=!1;_destroyed=!1;_finalKeyframe;_originalOnDoneFns=[];_originalOnStartFns=[];domPlayer;time=0;parentPlayer=null;currentSnapshot=new Map;constructor(e,t,n,i){this.element=e,this.keyframes=t,this.options=n,this._specialStyles=i,this._duration=n.duration,this._delay=n.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;let t=()=>this._onFinish();this.domPlayer.addEventListener("finish",t),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",t)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){let t=[];return e.forEach(n=>{t.push(Object.fromEntries(n))}),t}_triggerWebAnimation(e,t,n){return e.animate(this._convertKeyframesToObject(t),n)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((n,i)=>{i!=="offset"&&e.set(i,this._finished?n:Ki(this.element,i))}),this.currentSnapshot=e}triggerCallback(e){let t=e==="start"?this._onStartFns:this._onDoneFns;t.forEach(n=>n()),t.length=0}},Rn=class{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}containsElement(e,t){return Mr(e,t)}getParentElement(e){return Yi(e)}query(e,t,n){return wr(e,t,n)}computeStyle(e,t,n){return Ki(e,t)}animate(e,t,n,i,s,c=[]){let m=i==0?"both":"forwards",u={duration:n,delay:i,fill:m};s&&(u.easing=s);let f=new Map,C=c.filter(N=>N instanceof zn);Da(n,i)&&C.forEach(N=>{N.currentSnapshot.forEach((O,I)=>f.set(I,O))});let y=ka(t).map(N=>new Map(N));y=Aa(e,y,f);let D=ms(e,y);return new zn(e,y,u,D)}};var En="@",Nr="@.disabled",Ln=class{namespaceId;delegate;engine;_onDestroy;\u0275type=0;constructor(e,t,n,i){this.namespaceId=e,this.delegate=t,this.engine=n,this._onDestroy=i}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,t){return this.delegate.createElement(e,t)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,t){this.delegate.appendChild(e,t),this.engine.onInsert(this.namespaceId,t,e,!1)}insertBefore(e,t,n,i=!0){this.delegate.insertBefore(e,t,n),this.engine.onInsert(this.namespaceId,t,e,i)}removeChild(e,t,n){this.parentNode(t)&&this.engine.onRemove(this.namespaceId,t,this.delegate)}selectRootElement(e,t){return this.delegate.selectRootElement(e,t)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,t,n,i){this.delegate.setAttribute(e,t,n,i)}removeAttribute(e,t,n){this.delegate.removeAttribute(e,t,n)}addClass(e,t){this.delegate.addClass(e,t)}removeClass(e,t){this.delegate.removeClass(e,t)}setStyle(e,t,n,i){this.delegate.setStyle(e,t,n,i)}removeStyle(e,t,n){this.delegate.removeStyle(e,t,n)}setProperty(e,t,n){t.charAt(0)==En&&t==Nr?this.disableAnimations(e,!!n):this.delegate.setProperty(e,t,n)}setValue(e,t){this.delegate.setValue(e,t)}listen(e,t,n,i){return this.delegate.listen(e,t,n,i)}disableAnimations(e,t){this.engine.disableAnimations(e,t)}},qi=class extends Ln{factory;constructor(e,t,n,i,s){super(t,n,i,s),this.factory=e,this.namespaceId=t}setProperty(e,t,n){t.charAt(0)==En?t.charAt(1)=="."&&t==Nr?(n=n===void 0?!0:!!n,this.disableAnimations(e,n)):this.engine.process(this.namespaceId,e,t.slice(1),n):this.delegate.setProperty(e,t,n)}listen(e,t,n,i){if(t.charAt(0)==En){let s=us(e),c=t.slice(1),m="";return c.charAt(0)!=En&&([c,m]=fs(c)),this.engine.listen(this.namespaceId,s,c,m,u=>{let f=u._data||-1;this.factory.scheduleListenerCallback(f,n,u)})}return this.delegate.listen(e,t,n,i)}};function us(o){switch(o){case"body":return document.body;case"document":return document;case"window":return window;default:return o}}function fs(o){let e=o.indexOf("."),t=o.substring(0,e),n=o.slice(e+1);return[t,n]}var Vn=class{delegate;engine;_zone;_currentId=0;_microtaskId=1;_animationCallbacksBuffer=[];_rendererCache=new Map;_cdRecurDepth=0;constructor(e,t,n){this.delegate=e,this.engine=t,this._zone=n,t.onRemovalComplete=(i,s)=>{s?.removeChild(null,i)}}createRenderer(e,t){let n="",i=this.delegate.createRenderer(e,t);if(!e||!t?.data?.animation){let f=this._rendererCache,C=f.get(i);if(!C){let y=()=>f.delete(i);C=new Ln(n,i,this.engine,y),f.set(i,C)}return C}let s=t.id,c=t.id+"-"+this._currentId;this._currentId++,this.engine.register(c,e);let m=f=>{Array.isArray(f)?f.forEach(m):this.engine.registerTrigger(s,c,e,f.name,f)};return t.data.animation.forEach(m),new qi(this,c,i,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,t,n){if(e>=0&&e<this._microtaskId){this._zone.run(()=>t(n));return}let i=this._animationCallbacksBuffer;i.length==0&&queueMicrotask(()=>{this._zone.run(()=>{i.forEach(s=>{let[c,m]=s;c(m)}),this._animationCallbacksBuffer=[]})}),i.push([t,n])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}componentReplaced(e){this.engine.flush(),this.delegate.componentReplaced?.(e)}};var bs=(()=>{class o extends Bt{constructor(t,n,i){super(t,n,i)}ngOnDestroy(){this.flush()}static \u0275fac=function(n){return new(n||o)(de(xo),de(Ot),de(St))};static \u0275prov=be({token:o,factory:o.\u0275fac})}return o})();function _s(){return new In}function vs(o,e,t){return new Vn(o,e,t)}var zr=[{provide:St,useFactory:_s},{provide:Bt,useClass:bs},{provide:lo,useFactory:vs,deps:[Oo,Bt,je]}],vd=[{provide:Ot,useFactory:()=>new Rn},{provide:bi,useValue:"BrowserAnimations"},...zr],xs=[{provide:Ot,useClass:Hi},{provide:bi,useValue:"NoopAnimations"},...zr];function Rr(){return[...xs]}function ys(o,e){o&1&&(r(0,"span",19),l(1," Email is required "),a())}function Cs(o,e){o&1&&(r(0,"span",19),l(1," Invalid email format "),a())}function Ps(o,e){o&1&&(r(0,"span",19),l(1," Password is required "),a())}function Ms(o,e){o&1&&(r(0,"span",19),l(1," Password must be at least 6 characters "),a())}function ws(o,e){if(o&1&&(r(0,"div",20),g(1,"i",21),l(2),a()),o&2){let t=b();d(2),M(" ",t.errorMessage," ")}}function Os(o,e){o&1&&g(0,"i",22)}var Un=class o{constructor(e,t,n,i,s){this.fb=e;this.authService=t;this.router=n;this.db=i;this.storage=s;this.loginForm=this.fb.group({email:["",[S.required,S.email]],password:["",[S.required,S.minLength(6)]]})}loginForm;loading=!1;errorMessage="";ngOnInit(){}onSubmit(){if(this.loginForm.valid){this.loading=!0;let{email:e,password:t}=this.loginForm.value;this.authService.login(e,t).subscribe({next:n=>{if(n.success){let i=this.db.current_doctor();if(!i){this.errorMessage="Error getting user information",this.loading=!1;return}i.role==="DOCTOR"?this.router.navigate(["/dashboard"]):(this.errorMessage="Invalid user role",this.loading=!1)}},error:n=>{console.error("Login error:",n),this.errorMessage=n.message||"Login failed. Please check your credentials and try again.",this.loading=!1}})}else this.errorMessage="Please fill in all required fields correctly.",this.loginForm.get("email")?.errors?this.errorMessage=this.getEmailErrorMessage():this.loginForm.get("password")?.errors&&(this.errorMessage=this.getPasswordErrorMessage())}redirectBasedOnRole(e){e.toLowerCase()==="doctor"&&this.router.navigate(["/dashboard"])}getEmailErrorMessage(){let e=this.loginForm.get("email");return e?.hasError("required")?"Email is required":e?.hasError("email")?"Please enter a valid email address":""}getPasswordErrorMessage(){let e=this.loginForm.get("password");return e?.hasError("required")?"Password is required":e?.hasError("minlength")?"Password must be at least 6 characters long":""}static \u0275fac=function(t){return new(t||o)(P(Ye),P(le),P(j),P(He),P(er))};static \u0275cmp=E({type:o,selectors:[["app-login"]],decls:31,vars:8,consts:[[1,"login-container"],[1,"login-card"],[1,"login-header"],[1,"bi","bi-person"],[3,"ngSubmit","formGroup"],[1,"form-group"],[1,"input-container"],[1,"bi","bi-envelope"],["type","email","formControlName","email","placeholder","Enter your email address"],["class","validation-error",4,"ngIf"],[1,"bi","bi-shield-lock"],["type","password","formControlName","password","placeholder","Enter your password"],[1,"remember-forgot"],["href","#"],["class","error-message",4,"ngIf"],["type","submit",1,"login-btn"],["class","bi bi-arrow-repeat spinner",4,"ngIf"],[1,"register-link"],["routerLink","/register"],[1,"validation-error"],[1,"error-message"],[1,"bi","bi-exclamation-circle"],[1,"bi","bi-arrow-repeat","spinner"]],template:function(t,n){if(t&1&&(r(0,"div",0)(1,"div",1)(2,"div",2),g(3,"i",3),r(4,"h2"),l(5,"Welcome Back"),a()(),r(6,"form",4),h("ngSubmit",function(){return n.onSubmit()}),r(7,"div",5)(8,"div",6),g(9,"i",7)(10,"input",8),a(),_(11,ys,2,0,"span",9)(12,Cs,2,0,"span",9),a(),r(13,"div",5)(14,"div",6),g(15,"i",10)(16,"input",11),a(),_(17,Ps,2,0,"span",9)(18,Ms,2,0,"span",9),a(),r(19,"div",12)(20,"a",13),l(21,"Forgot password?"),a()(),_(22,ws,3,1,"div",14),r(23,"button",15),_(24,Os,1,0,"i",16),r(25,"span"),l(26),a()()(),r(27,"p",17),l(28," Don't have an account? "),r(29,"a",18),l(30,"Register"),a()()()()),t&2){let i,s,c,m;d(6),p("formGroup",n.loginForm),d(5),p("ngIf",((i=n.loginForm.get("email"))==null||i.errors==null?null:i.errors.required)&&((i=n.loginForm.get("email"))==null?null:i.touched)),d(),p("ngIf",((s=n.loginForm.get("email"))==null||s.errors==null?null:s.errors.email)&&((s=n.loginForm.get("email"))==null?null:s.touched)),d(5),p("ngIf",((c=n.loginForm.get("password"))==null||c.errors==null?null:c.errors.required)&&((c=n.loginForm.get("password"))==null?null:c.touched)),d(),p("ngIf",((m=n.loginForm.get("password"))==null||m.errors==null?null:m.errors.minlength)&&((m=n.loginForm.get("password"))==null?null:m.touched)),d(4),p("ngIf",n.errorMessage),d(2),p("ngIf",n.loading),d(2),T(n.loading?"Logging in...":"Login")}},dependencies:[A,U,Fe,Ae,H,Z,De,We,$e,_e,At],styles:[".error-message[_ngcontent-%COMP%]{background:#fef2f2;color:#dc2626;padding:.75rem 1rem;border-radius:8px;font-size:.875rem;margin:1rem 0;display:flex;align-items:center;gap:.5rem}.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:static;transform:none}.spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite;display:inline-block;margin-right:8px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.login-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#f3f4f6,#e5e7eb);padding:2rem}.login-card[_ngcontent-%COMP%]{width:100%;max-width:480px;background:#fff;border-radius:20px;box-shadow:0 10px 25px #0000000d;padding:3rem 2.5rem;position:relative;overflow:hidden;animation:_ngcontent-%COMP%_fadeIn .5s ease-out}.login-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:2rem}.login-card[_ngcontent-%COMP%]   i.bi-person[_ngcontent-%COMP%]{font-size:2.5rem;color:#199a8e;margin-bottom:1rem}.bi-person[_ngcontent-%COMP%]{font-size:50px;width:70px;height:70px;display:flex;justify-content:center;align-items:center;border-radius:50%;background-color:#e6f7f5;color:#199a8e;padding:15px;margin:0 auto 1.5rem}.login-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#111827;font-size:1.5rem;font-weight:600;margin-bottom:.5rem}.login-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6b7280;margin-bottom:2rem}.input-container[_ngcontent-%COMP%]{position:relative;width:100%}.form-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem;display:flex;flex-direction:column}.form-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:#199a8e;font-size:1.1rem;z-index:1;pointer-events:none}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:.875rem 1rem .875rem 2.5rem;border:1px solid #e5e7eb;border-radius:8px;font-size:.875rem;color:#111827;transition:all .2s;background-color:#f9fafb;height:48px;box-sizing:border-box}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 3px #199a8e1a;background-color:#fff}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:#6b7280}.validation-error[_ngcontent-%COMP%]{color:#dc2626;font-size:.75rem;margin-top:.25rem;display:block}.remember-forgot[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1.5rem;font-size:.875rem}.remember-forgot[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#4b5563}.remember-forgot[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{accent-color:#199a8e}.remember-forgot[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#199a8e;text-decoration:none;font-weight:500;transition:color .2s}.remember-forgot[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#147d73}.login-btn[_ngcontent-%COMP%]{width:100%;padding:.875rem;background:linear-gradient(90deg,#199a8e,#1fb5a6);color:#fff;border:none;border-radius:12px;font-size:1rem;font-weight:500;cursor:pointer;transition:all .3s ease;height:48px;display:flex;align-items:center;justify-content:center;gap:.5rem;margin-bottom:1rem}.login-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #199a8e26}.login-btn[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}.admin-login-btn[_ngcontent-%COMP%]{width:100%;padding:.875rem;background:#fff;color:#199a8e;border:2px solid #199a8e;border-radius:12px;font-size:1rem;font-weight:500;cursor:pointer;transition:all .3s ease;height:48px;display:flex;align-items:center;justify-content:center;gap:.5rem}.admin-login-btn[_ngcontent-%COMP%]:hover{background:#199a8e0d}.register-link[_ngcontent-%COMP%]{text-align:center;margin-top:1.5rem;font-size:.875rem;color:#4b5563}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#199a8e;text-decoration:none;font-weight:500;margin-left:.25rem}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@media screen and (max-width: 1200px){.login-card[_ngcontent-%COMP%]{max-width:440px;padding:2.5rem 2rem}}@media screen and (max-width: 768px){.login-container[_ngcontent-%COMP%]{padding:1.5rem}.login-card[_ngcontent-%COMP%]{max-width:400px;padding:2rem 1.5rem}.bi-person[_ngcontent-%COMP%]{font-size:40px;width:60px;height:60px;padding:12px;margin-bottom:1.25rem}.login-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.4rem}.login-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.95rem;margin-bottom:1.75rem}.form-group[_ngcontent-%COMP%]{margin-bottom:1.25rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding:.75rem 1rem .75rem 2.25rem;height:44px;font-size:.85rem}.form-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.remember-forgot[_ngcontent-%COMP%]{font-size:.8rem;margin-bottom:1.25rem}.login-btn[_ngcontent-%COMP%]{height:44px;font-size:.95rem;padding:.75rem}}@media screen and (max-width: 480px){.login-container[_ngcontent-%COMP%]{padding:1rem}.login-card[_ngcontent-%COMP%]{max-width:100%;padding:1.75rem 1.25rem}.bi-person[_ngcontent-%COMP%]{font-size:35px;width:55px;height:55px;padding:10px;margin-bottom:1rem}.login-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.3rem}.login-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]{margin-bottom:1rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding:.7rem 1rem .7rem 2.25rem;height:42px;font-size:.8rem}.remember-forgot[_ngcontent-%COMP%]{flex-direction:column;gap:.75rem;align-items:flex-start;margin-bottom:1.5rem}.error-message[_ngcontent-%COMP%]{font-size:.8rem;padding:.6rem .8rem}}@media screen and (max-width: 320px){.login-card[_ngcontent-%COMP%]{padding:1.5rem 1rem}.bi-person[_ngcontent-%COMP%]{font-size:30px;width:50px;height:50px;padding:8px}.login-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.2rem}.login-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.85rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding:.65rem 1rem .65rem 2rem;height:40px}.login-btn[_ngcontent-%COMP%]{height:40px;font-size:.9rem;padding:.65rem}}"]})};function Es(o,e){o&1&&(r(0,"span",20),l(1," Full name is required "),a())}function ks(o,e){o&1&&(r(0,"span",20),l(1," Please enter both your first and last name "),a())}function Ts(o,e){if(o&1&&(r(0,"span",20),l(1),a()),o&2){let t=b();d(),M(" ",t.emailErrorMessage," ")}}function Is(o,e){o&1&&(r(0,"span",20),l(1," Password must be at least 6 characters long "),a())}function Ds(o,e){o&1&&(r(0,"span",20),l(1," Please confirm your password "),a())}function As(o,e){o&1&&(r(0,"span",20),l(1," Passwords do not match "),a())}function Fs(o,e){if(o&1&&(r(0,"div",21),l(1),a()),o&2){let t=b();d(),M(" ",t.errorMessage," ")}}var Bn=class o{constructor(e,t,n,i,s){this.fb=e;this.authService=t;this.router=n;this.doctorService=i;this.db=s;this.registerForm=this.fb.group({fullname:["",[S.required,S.pattern(/^[a-zA-Z]+ [a-zA-Z]+/)]],email:["",[S.required,S.email,this.validEmailDomainValidator()]],password:["",[S.required,S.minLength(6)]],confirmPassword:["",S.required],specialty:[""],medicalLicenseNumber:[""]},{validators:this.passwordMatchValidator}),this.registerForm.get("email")?.valueChanges.subscribe(()=>{this.errorMessage="",this.showErrorModal=!1,this.emailTouched&&this.updateEmailErrorMessage()})}registerForm;loading=!1;errorMessage="";showErrorModal=!1;errorModalTitle="";errorModalMessage="";errorModalAction="";selectedRole="doctor";emailTouched=!1;emailErrorMessage="";ngOnInit(){}redirectBasedOnRole(e){switch(e.toLowerCase()){case"doctor":let t=this.authService.getUserInfo();t&&t.specialization?this.router.navigate(["/dashboard"]):this.router.navigate(["/doctors-profile"]);break;case"patient":this.router.navigate(["/patient-dashboard"]);break;case"receptionist":this.router.navigate(["/receptionist"]);break;case"admin":this.router.navigate(["/admin/dashboard"]);break;default:console.error("Unknown role:",e),this.errorMessage="Invalid user role"}}validEmailDomainValidator(){return e=>{let t=e.value;if(!t)return null;if(!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(t))return{invalidEmailFormat:!0};let i=t.split("@")[1];if(!i)return{invalidDomain:!0};let s=i.split(".").pop();if(!s||s.length<2||s.length>63)return{invalidTLD:!0};let c=[/com[a-z]{1,}$/,/co[a-z]{3,}$/,/org[a-z]{1,}$/,/net[a-z]{1,}$/];for(let m of c)if(m.test(s))return{invalidTLD:!0};return null}}passwordMatchValidator(e){let t=e.get("password"),n=e.get("confirmPassword");return t&&n&&t.value!==n.value?(n.setErrors({passwordMismatch:!0}),{passwordMismatch:!0}):null}showDuplicateEmailError(){this.errorModalTitle="Email Already Registered",this.errorModalMessage="An account with this email already exists. Would you like to login instead?",this.errorModalAction="Go to Login",this.showErrorModal=!0}onEmailBlur(){this.emailTouched=!0,this.updateEmailErrorMessage()}updateEmailErrorMessage(){let e=this.registerForm.get("email");e&&e.errors&&this.emailTouched?e.errors.required?this.emailErrorMessage="Email is required":e.errors.email?this.emailErrorMessage="Please enter a valid email address":e.errors.invalidEmailFormat?this.emailErrorMessage="Please enter a valid email address":e.errors.invalidDomain?this.emailErrorMessage="Invalid email domain":e.errors.invalidTLD?this.emailErrorMessage="The email extension you entered appears to be invalid. Please use a valid email address (e.g., <EMAIL>)":this.emailErrorMessage="Please enter a valid email address":this.emailErrorMessage=""}onModalClose(){this.showErrorModal=!1,this.errorMessage="",this.registerForm.patchValue({email:""}),this.registerForm.get("email")?.markAsUntouched(),this.emailTouched=!1,this.emailErrorMessage=""}navigateToLogin(){this.router.navigate(["/login"])}onSubmit(){if(!this.showErrorModal)if(Object.keys(this.registerForm.controls).forEach(e=>{this.registerForm.get(e)?.markAsTouched()}),this.emailTouched=!0,this.updateEmailErrorMessage(),this.registerForm.valid){this.loading=!0,this.errorMessage="";let e=this.registerForm.value,t=e.fullname.split(" "),n=t[0],i=t.slice(1).join(" "),s={firstName:n,lastName:i,email:e.email,password:e.password,role:"doctor",specialty:e.specialty,medicalLicenseNumber:e.medicalLicenseNumber};this.authService.register(s).subscribe({next:c=>{console.log("Registration successful:",c),this.loading=!1,this.router.navigate(["/doctors-profile"])},error:c=>{this.loading=!1,console.error("Registration error:",c),c.message&&c.message.includes("email-already-in-use")?this.showDuplicateEmailError():this.errorMessage=c.message||"Registration failed. Please try again."}})}else{let e=this.registerForm.controls;e.fullname.errors?this.errorMessage=e.fullname.errors.pattern?"Please enter both your first and last name":"Full name is required":e.email.errors?this.updateEmailErrorMessage():e.password.errors?this.errorMessage=e.password.errors.required?"Password is required":"Password must be at least 6 characters long":e.confirmPassword.errors?this.errorMessage=e.confirmPassword.errors.passwordMismatch?"Passwords do not match":"Please confirm your password":this.errorMessage="Please fill in all required fields correctly"}}static \u0275fac=function(t){return new(t||o)(P(Ye),P(le),P(j),P(Ut),P(He))};static \u0275cmp=E({type:o,selectors:[["app-register"]],features:[et([le,Ut])],decls:37,vars:15,consts:[[1,"register-container"],[1,"register-card"],[1,"register-header"],[1,"bi","bi-person"],[3,"ngSubmit","formGroup"],[1,"form-group"],[1,"input-container"],[1,"bi","bi-person-circle"],["type","text","formControlName","fullname","placeholder","Enter your fullname"],["class","validation-error",4,"ngIf"],[1,"bi","bi-envelope"],["type","email","formControlName","email","placeholder","Enter your email",3,"blur"],[1,"bi","bi-shield-lock"],["type","password","formControlName","password","placeholder","Enter your password"],["type","password","formControlName","confirmPassword","placeholder","Confirm your password"],["class","error-message",4,"ngIf"],[3,"showChange","close","show","title","message","actionText","onAction"],["type","submit",1,"submit-button",3,"disabled"],[1,"login-link"],["routerLink","/login"],[1,"validation-error"],[1,"error-message"]],template:function(t,n){if(t&1&&(r(0,"div",0)(1,"div",1)(2,"div",2),g(3,"i",3),r(4,"h2"),l(5,"Create Account"),a()(),r(6,"form",4),h("ngSubmit",function(){return n.onSubmit()}),r(7,"div",5)(8,"div",6),g(9,"i",7)(10,"input",8),a(),_(11,Es,2,0,"span",9)(12,ks,2,0,"span",9),a(),r(13,"div",5)(14,"div",6),g(15,"i",10),r(16,"input",11),h("blur",function(){return n.onEmailBlur()}),a()(),_(17,Ts,2,1,"span",9),a(),r(18,"div",5)(19,"div",6),g(20,"i",12)(21,"input",13),a(),_(22,Is,2,0,"span",9),a(),r(23,"div",5)(24,"div",6),g(25,"i",12)(26,"input",14),a(),_(27,Ds,2,0,"span",9)(28,As,2,0,"span",9),a(),_(29,Fs,2,1,"div",15),r(30,"app-error-modal",16),h("showChange",function(s){return n.showErrorModal=s})("close",function(){return n.onModalClose()}),a(),r(31,"button",17),l(32),a()(),r(33,"p",18),l(34," Already have an account? "),r(35,"a",19),l(36,"Login"),a()()()()),t&2){let i,s,c,m,u;d(6),p("formGroup",n.registerForm),d(5),p("ngIf",((i=n.registerForm.get("fullname"))==null||i.errors==null?null:i.errors.required)&&((i=n.registerForm.get("fullname"))==null?null:i.touched)),d(),p("ngIf",((s=n.registerForm.get("fullname"))==null||s.errors==null?null:s.errors.pattern)&&((s=n.registerForm.get("fullname"))==null?null:s.touched)),d(5),p("ngIf",n.emailErrorMessage),d(5),p("ngIf",((c=n.registerForm.get("password"))==null?null:c.errors)&&((c=n.registerForm.get("password"))==null?null:c.touched)),d(5),p("ngIf",((m=n.registerForm.get("confirmPassword"))==null||m.errors==null?null:m.errors.required)&&((m=n.registerForm.get("confirmPassword"))==null?null:m.touched)),d(),p("ngIf",((u=n.registerForm.get("confirmPassword"))==null||u.errors==null?null:u.errors.passwordMismatch)&&((u=n.registerForm.get("confirmPassword"))==null?null:u.touched)),d(),p("ngIf",n.errorMessage),d(),p("show",n.showErrorModal)("title",n.errorModalTitle)("message",n.errorModalMessage)("actionText",n.errorModalAction)("onAction",n.navigateToLogin.bind(n)),d(),p("disabled",n.loading),d(),M(" ",n.loading?"Registering...":"Register"," ")}},dependencies:[A,U,Fe,Ae,H,Z,De,We,$e,_e,At,or],styles:[`.registration-icon[_ngcontent-%COMP%]{font-size:2.5rem;color:#199a8e;margin-bottom:1rem}.roles[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;margin-bottom:2rem}.toggle-switch[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:200px;background:#e6f7f5;border-radius:30px;padding:5px;position:relative}.toggle-switch[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{width:50%;text-align:center;padding:10px;cursor:pointer;font-size:14px;font-weight:600;z-index:2;position:relative;transition:all .3s ease;color:#199a8e}.toggle-switch[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{display:none}.toggle-btn[_ngcontent-%COMP%]{position:absolute;width:50%;height:calc(100% - 2px);top:1px;background:#199a8e;border-radius:30px;transition:all .3s ease;z-index:1}.toggle-switch[_ngcontent-%COMP%]   label.active[_ngcontent-%COMP%]{color:#fff}.error-message[_ngcontent-%COMP%]{color:#dc2626;font-size:.875rem;margin:.5rem 0;padding:.5rem;background-color:#fef2f2;border:1px solid #fee2e2;border-radius:6px;text-align:center}.spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite;display:inline-block;margin-right:8px}button[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed}.registration-container[_ngcontent-%COMP%], .register-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#f3f4f6,#e5e7eb);padding:2rem}.Registration-card[_ngcontent-%COMP%]{background:#fff;padding:2.5rem;border-radius:15px;box-shadow:0 4px 20px #00000014;width:600px;max-width:100%;margin:auto}.register-card[_ngcontent-%COMP%]   i.bi-person[_ngcontent-%COMP%]{font-size:2.5rem;color:#199a8e;margin-bottom:1rem}h2[_ngcontent-%COMP%]{font-size:24px;text-align:center;margin-bottom:8px;color:#2d3748}.register-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#111827;font-size:1.5rem;font-weight:600;margin-bottom:.5rem}h3[_ngcontent-%COMP%]{color:#2d3748;font-size:18px;margin:24px 0 16px;text-align:left}p[_ngcontent-%COMP%]{color:#6c757d;text-align:center;font-size:14px;margin-bottom:24px}.register-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6b7280;margin-bottom:2rem}.bi-lock-fill[_ngcontent-%COMP%], .bi-person-fill[_ngcontent-%COMP%], .bi-envelope-fill[_ngcontent-%COMP%], .bi-telephone-fill[_ngcontent-%COMP%], .bi-file-earmark-medical-fill[_ngcontent-%COMP%], .bi-hospital-fill[_ngcontent-%COMP%]{color:#199a8e;font-size:1.1rem!important;margin-right:10px}svg[_ngcontent-%COMP%]{margin:0 auto 1.5rem;display:block}.input-group[_ngcontent-%COMP%]{display:flex;align-items:center;border:1px solid #E2E8F0;border-radius:8px;padding:12px;margin-bottom:16px;transition:border-color .2s;background:#fff}.input-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem}.input-group[_ngcontent-%COMP%]:focus-within{border-color:#199a8e}.input-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:#199a8e;font-size:1.1rem}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{border:none;outline:none;flex:1;padding:0 8px;font-size:14px;color:#4a5568;background:transparent;width:100%}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:.875rem 1rem .875rem 2.75rem;border:1px solid #e5e7eb;border-radius:.5rem;font-size:.9375rem;color:#374151;transition:all .2s ease}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 4px #199a8e1a}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{color:#a0aec0}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:#9ca3af}small[_ngcontent-%COMP%]{color:#ef4444;display:block;text-align:left;margin:-12px 0 16px;font-size:12px}small[_ngcontent-%COMP%]{display:block;color:#ef4444;font-size:.875rem;margin-top:-1rem;margin-bottom:1rem;text-align:left;padding-left:.5rem}.register-with-licence-btn[_ngcontent-%COMP%], .register-without-licence-btn[_ngcontent-%COMP%]{background-color:#fff;color:#199a8e;border:2px solid #199A8E;margin-bottom:12px;padding:12px;width:100%;border-radius:8px;cursor:pointer;font-weight:600;font-size:14px;transition:all .3s ease}.register-with-licence-btn[_ngcontent-%COMP%]:hover, .register-without-licence-btn[_ngcontent-%COMP%]:hover{background-color:#199a8e;color:#fff;transform:translateY(-1px)}button[type=submit][_ngcontent-%COMP%]{width:100%;padding:.875rem;background:#199a8e;color:#fff;border:none;border-radius:.5rem;font-size:1rem;font-weight:500;cursor:pointer;transition:all .2s ease}button[type=submit][_ngcontent-%COMP%]:hover{background:#168276}.register-link[_ngcontent-%COMP%]{font-size:14px;margin-top:16px;color:#4a5568;text-align:center}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#199a8e;font-weight:600;text-decoration:none;transition:color .2s}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#147d73}.login-link[_ngcontent-%COMP%]{text-align:center;margin-top:1.5rem;color:#6b7280;font-size:.9375rem}select[_ngcontent-%COMP%]{appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23199A8E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right 10px center;background-size:15px;padding-right:30px!important}select.form-control[_ngcontent-%COMP%]{appearance:none;background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23199a8e' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");background-repeat:no-repeat;background-position:right 1rem center;padding-right:2.75rem}.bi-person[_ngcontent-%COMP%]{font-size:50px;width:70px;height:70px;display:flex;justify-content:center;align-items:center;border-radius:50%;background-color:#e6f7f5;color:#199a8e;padding:15px;margin:0 auto 1.5rem}@media (max-width: 640px){.Registration-card[_ngcontent-%COMP%]{padding:2rem}h2[_ngcontent-%COMP%]{font-size:20px}h3[_ngcontent-%COMP%]{font-size:16px}.input-group[_ngcontent-%COMP%]{padding:10px}svg[_ngcontent-%COMP%]{width:40px;height:40px}}@media (max-width: 480px){.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{font-size:13px}}.success-message[_ngcontent-%COMP%]{background:#ecfdf5;color:#059669;padding:.75rem 1rem;border-radius:8px;font-size:.875rem;margin:1rem 0;display:flex;align-items:center;gap:.5rem}.register-header[_ngcontent-%COMP%]   i.bi-person[_ngcontent-%COMP%]{font-size:2.5rem;color:#199a8e;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]:first-of-type{margin-top:2rem}.register-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6b7280;font-size:1rem}.form-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem;display:flex;flex-direction:column}.input-container[_ngcontent-%COMP%]{position:relative;width:100%}.register-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#f3f4f6,#e5e7eb);padding:2rem}.register-card[_ngcontent-%COMP%]{width:100%;max-width:480px;background:#fff;border-radius:20px;box-shadow:0 10px 25px #0000000d;padding:3rem 2.5rem;position:relative;overflow:hidden;animation:_ngcontent-%COMP%_fadeIn .5s ease-out}.register-header[_ngcontent-%COMP%]{text-align:center;display:flex;flex-direction:column;align-items:center}.register-header[_ngcontent-%COMP%]   i.bi-person[_ngcontent-%COMP%]{font-size:2.5rem;color:#199a8e;margin-bottom:1rem}.register-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#1f2937;font-size:1.75rem;font-weight:600;margin-bottom:2rem}.roles[_ngcontent-%COMP%]{margin-bottom:2rem}.form-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:#199a8e;font-size:1.1rem;z-index:1;pointer-events:none}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{width:100%;padding:.875rem 1rem .875rem 2.5rem;border:1px solid #e5e7eb;border-radius:8px;font-size:.875rem;color:#111827;transition:all .2s;background-color:#f9fafb;height:48px;box-sizing:border-box}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 3px #199a8e1a;background-color:#fff}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:#6b7280}.validation-error[_ngcontent-%COMP%]{display:block;color:#dc2626;font-size:.75rem;margin-top:.25rem;order:1}.error-message[_ngcontent-%COMP%]{background:#fef2f2;color:#dc2626;padding:.75rem 1rem;border-radius:8px;font-size:.875rem;margin:1rem 0;display:flex;align-items:center;gap:.5rem}.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:static;transform:none}.register-btn[_ngcontent-%COMP%]{width:100%;padding:.875rem;background:linear-gradient(90deg,#199a8e,#1fb5a6);color:#fff;border:none;border-radius:12px;font-size:1rem;font-weight:500;cursor:pointer;transition:all .3s ease;height:48px;display:flex;align-items:center;justify-content:center;gap:.5rem}.register-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #199a8e26}.register-btn[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}.login-link[_ngcontent-%COMP%]{text-align:center;margin-top:1.5rem;font-size:.875rem;color:#4b5563}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#199a8e;text-decoration:none;font-weight:500;margin-left:.25rem}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@media screen and (max-width: 1200px){.Registration-card[_ngcontent-%COMP%], .register-card[_ngcontent-%COMP%]{max-width:550px;padding:2.5rem 2rem}.register-card[_ngcontent-%COMP%]   i.bi-person[_ngcontent-%COMP%]{font-size:2.25rem}}@media screen and (max-width: 768px){.registration-container[_ngcontent-%COMP%], .register-container[_ngcontent-%COMP%]{padding:1.5rem}.Registration-card[_ngcontent-%COMP%], .register-card[_ngcontent-%COMP%]{max-width:500px;padding:2rem 1.5rem}h2[_ngcontent-%COMP%]{font-size:22px;margin-bottom:6px}h3[_ngcontent-%COMP%]{font-size:16px;margin:20px 0 14px}p[_ngcontent-%COMP%]{font-size:13px;margin-bottom:20px}.input-group[_ngcontent-%COMP%]{padding:10px;margin-bottom:14px}.input-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{font-size:.9rem}.register-btn[_ngcontent-%COMP%]{height:44px;font-size:.95rem;padding:.75rem}.bi-lock-fill[_ngcontent-%COMP%], .bi-person-fill[_ngcontent-%COMP%], .bi-envelope-fill[_ngcontent-%COMP%], .bi-telephone-fill[_ngcontent-%COMP%], .bi-file-earmark-medical-fill[_ngcontent-%COMP%], .bi-hospital-fill[_ngcontent-%COMP%]{font-size:1rem!important}}@media screen and (max-width: 480px){.registration-container[_ngcontent-%COMP%], .register-container[_ngcontent-%COMP%]{padding:1rem}.Registration-card[_ngcontent-%COMP%], .register-card[_ngcontent-%COMP%]{padding:1.75rem 1.25rem}h2[_ngcontent-%COMP%]{font-size:20px}h3[_ngcontent-%COMP%]{font-size:15px;margin:18px 0 12px}p[_ngcontent-%COMP%]{font-size:12px;margin-bottom:18px}.input-group[_ngcontent-%COMP%]{padding:8px;margin-bottom:12px}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{font-size:.85rem}.register-btn[_ngcontent-%COMP%]{height:42px;font-size:.9rem;padding:.7rem}.error-message[_ngcontent-%COMP%]{font-size:.8rem;padding:.6rem .8rem;margin:.4rem 0}.bi-lock-fill[_ngcontent-%COMP%], .bi-person-fill[_ngcontent-%COMP%], .bi-envelope-fill[_ngcontent-%COMP%], .bi-telephone-fill[_ngcontent-%COMP%], .bi-file-earmark-medical-fill[_ngcontent-%COMP%], .bi-hospital-fill[_ngcontent-%COMP%]{font-size:.95rem!important}}@media screen and (max-width: 320px){.Registration-card[_ngcontent-%COMP%], .register-card[_ngcontent-%COMP%]{padding:1.5rem 1rem}h2[_ngcontent-%COMP%]{font-size:18px}h3[_ngcontent-%COMP%]{font-size:14px;margin:16px 0 10px}p[_ngcontent-%COMP%]{font-size:11px;margin-bottom:16px}.input-group[_ngcontent-%COMP%]{padding:7px;margin-bottom:10px}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{font-size:.8rem}.register-btn[_ngcontent-%COMP%]{height:40px;font-size:.85rem;padding:.65rem}.bi-lock-fill[_ngcontent-%COMP%], .bi-person-fill[_ngcontent-%COMP%], .bi-envelope-fill[_ngcontent-%COMP%], .bi-telephone-fill[_ngcontent-%COMP%], .bi-file-earmark-medical-fill[_ngcontent-%COMP%], .bi-hospital-fill[_ngcontent-%COMP%]{font-size:.9rem!important}}`]})};var rt=class o{profileUpdateSource=new oo;profileUpdated$=this.profileUpdateSource.asObservable();notifyProfileUpdate(e){console.log("Profile update notification sent:",e),this.profileUpdateSource.next(e)}static \u0275fac=function(t){return new(t||o)};static \u0275prov=be({token:o,factory:o.\u0275fac,providedIn:"root"})};var Ns=o=>({label:"Total Patients",value:o,icon:"bi-people-fill",route:"/doctors-patient"}),zs=o=>({label:"Appointments Today",value:o,icon:"bi-calendar2-check-fill",route:"/appointments"}),Rs=o=>({label:"Upcoming Appointments",value:o,icon:"bi-clipboard2-pulse-fill",route:"/appointments"}),Ls=(o,e,t)=>[o,e,t],Vs=(o,e,t)=>({"bi-check-circle-fill":o,"bi-x-circle-fill":e,"bi-clock-fill":t}),Us=(o,e,t)=>({"bi-exclamation-circle-fill":o,"bi-info-circle-fill":e,"bi-exclamation-triangle-fill":t});function js(o,e){if(o&1&&(r(0,"p",49),g(1,"i",50),l(2),a()),o&2){let t=b(2);d(2),M(" ",t.doctorInfo.specialization," ")}}function Bs(o,e){if(o&1&&(r(0,"p",51),g(1,"i",52),l(2),a()),o&2){let t=b(2);d(2),M(" ",t.doctorInfo.email," ")}}function qs(o,e){if(o&1&&(r(0,"p",53),g(1,"i",54),l(2),a()),o&2){let t=b(2);d(2),M(" ",t.doctorInfo.phoneNumber," ")}}function Ws(o,e){if(o&1&&(r(0,"p",55),g(1,"i",56),l(2),a()),o&2){let t=b(2);d(2),M(" ",t.doctorInfo.address," ")}}function $s(o,e){if(o&1&&(r(0,"p",57),g(1,"i",58),l(2),a()),o&2){let t=b(2);d(2),M(" ",t.doctorInfo.bio," ")}}function Ys(o,e){if(o&1&&(r(0,"div",43),_(1,js,3,1,"p",44)(2,Bs,3,1,"p",45)(3,qs,3,1,"p",46)(4,Ws,3,1,"p",47)(5,$s,3,1,"p",48),a()),o&2){let t=b();d(),p("ngIf",t.doctorInfo.specialization),d(),p("ngIf",t.doctorInfo.email),d(),p("ngIf",t.doctorInfo.phoneNumber),d(),p("ngIf",t.doctorInfo.address),d(),p("ngIf",t.doctorInfo.bio)}}function Hs(o,e){if(o&1){let t=V();r(0,"div",59),h("click",function(){let i=v(t).$implicit,s=b();return x(s.navigateTo(i.route))}),r(1,"div",60)(2,"span",61),l(3),a(),r(4,"span",62),l(5),a()(),r(6,"div",63),g(7,"i"),a()()}if(o&2){let t=e.$implicit;d(3),T(t.label),d(2),T(t.value),d(2),co("bi ",t.icon,"")}}function Zs(o,e){if(o&1&&(r(0,"div",64)(1,"span",65),l(2),a(),r(3,"div",66)(4,"span",67),l(5),a(),r(6,"span",68),l(7),a()(),r(8,"div",69),g(9,"i",70),r(10,"span"),l(11),tt(12,"titlecase"),a()()()),o&2){let t=e.$implicit;d(2),T(t.appointmentTime),d(3),T(t.patientName),d(2),T(t.reasonForVisit),d(),Oe(t.status),d(),p("ngClass",gn(9,Vs,t.status==="Completed",t.status==="Cancelled",t.status==="Pending")),d(2),T(pt(12,7,t.status))}}function Ks(o,e){o&1&&(r(0,"div",71),l(1," You don't have any approved appointments scheduled for today "),a())}function Qs(o,e){if(o&1&&(r(0,"div",72)(1,"div",73),g(2,"i",70),a(),r(3,"div",74)(4,"span",75),l(5),a(),r(6,"span",76),l(7),a()()()),o&2){let t=e.$implicit;d(),Oe(t.type),d(),p("ngClass",gn(5,Us,t.type==="warning",t.type==="info",t.type==="error")),d(3),T(t.message),d(2),T(t.time)}}function Gs(o,e){o&1&&(r(0,"div",77),l(1," No recent alerts "),a())}var qn=class o{constructor(e,t,n,i,s,c){this.authService=e;this.appointmentsService=t;this.profileUpdateService=n;this.router=i;this.db=s;this.firestore=c}doctorName="";doctorInfo=null;stats={totalPatients:0,appointmentsToday:0,pendingConsultations:0};appointments=[];alerts=[];appointmentsSub;profileUpdateSub;isLoading=!0;getDoctorFirstName(){return this.db.current_doctor()?.firstname||""}getDoctorFullName(){if(this.doctorInfo&&this.doctorInfo.firstName&&this.doctorInfo.lastName)return`${this.doctorInfo.firstName} ${this.doctorInfo.lastName}`;let e=this.db.current_doctor();return e?`${e.firstname} ${e.lastname}`:""}ngOnInit(){this.isLoading=!0;let e=this.authService.getUserInfo();e&&(this.doctorName=e.firstName||e.name.split(" ")[0],this.doctorInfo={profilePicture:this.ensureFullUrl(e.profilePicture),specialization:e.specialization,phoneNumber:e.phoneNumber,address:e.address,email:e.email,bio:e.bio,hospitalAffiliations:e.hospitalAffiliations,qualifications:e.qualifications,services:e.services,firstName:e.firstName,lastName:e.lastName},this.fetchDoctorFromFirestore(e.id||e.doctorId),this.loadData(),this.appointmentsSub=this.appointmentsService.appointments$.subscribe(()=>{this.loadData()}),this.profileUpdateSub=this.profileUpdateService.profileUpdated$.subscribe(t=>{console.log("Dashboard received profile update:",t),t.profilePicture&&this.doctorInfo&&(this.doctorInfo.profilePicture=this.ensureFullUrl(t.profilePicture)),t.name&&(this.doctorName=t.name.split(" ")[0]),t.specialization&&this.doctorInfo&&(this.doctorInfo.specialization=t.specialization),t.bio&&this.doctorInfo&&(this.doctorInfo.bio=t.bio)}))}fetchDoctorFromFirestore(e){if(!e){console.error("No doctor ID provided for Firestore fetch"),this.isLoading=!1;return}console.log("Fetching doctor profile from Firestore for ID:",e);let t=me(this.firestore,`users/${e}`);qe(t).then(n=>{if(this.isLoading=!1,n.exists()){let i=n.data();console.log("Doctor data fetched from Firestore:",i),this.doctorInfo={profilePicture:this.ensureFullUrl(i.profilePicture),specialization:i.specialization,phoneNumber:i.phoneNumber,email:i.email,bio:i.bio,address:i.address,hospitalAffiliations:i.hospitalAffiliations,qualifications:i.qualifications,services:i.services,firstName:i.firstName||i.firstname,lastName:i.lastName||i.lastname},(i.firstName||i.firstname)&&(this.doctorName=i.firstName||i.firstname);let s=this.authService.getUserInfo();if(s){let c=Ce(B({},s),{profilePicture:i.profilePicture,specialization:i.specialization,phoneNumber:i.phoneNumber,bio:i.bio,email:i.email,address:i.address,hospitalAffiliations:i.hospitalAffiliations,qualifications:i.qualifications,services:i.services,firstName:i.firstName||i.firstname,lastName:i.lastName||i.lastname});this.authService.saveUserInfo(c)}}else console.log("No doctor document found in Firestore")}).catch(n=>{this.isLoading=!1,console.error("Error fetching doctor from Firestore:",n)})}ngOnDestroy(){this.appointmentsSub&&this.appointmentsSub.unsubscribe(),this.profileUpdateSub&&this.profileUpdateSub.unsubscribe()}loadData(){let e=this.authService.getDoctorId();e&&(this.isLoading=!0,this.fetchPatientCountFromFirestore(e),this.fetchAppointmentsFromFirestore(e),this.loadLocalPatientCount(),this.loadLocalAppointments())}fetchPatientCountFromFirestore(e){let t=Q(this.firestore,"doctorPatients"),n=fe(t,se("doctorId","==",e));xe(n).then(i=>{let s=new Set;i.empty||i.forEach(m=>{let u=m.data();u.patientId&&s.add(u.patientId)});let c=s.size;this.stats.totalPatients=c,console.log("Firestore patient count:",c)}).catch(i=>{console.error("Error fetching patient count from Firestore:",i),this.loadLocalPatientCount()})}fetchAppointmentsFromFirestore(e){let t=Q(this.firestore,"appointments"),n=fe(t,se("doctorId","==",e));xe(n).then(i=>{let s=[];i.empty||i.forEach(c=>{let m=c.data();s.push({id:c.id,doctor_id:m.doctorId,patient_id:m.patientId,date:m.date,time:m.time,status:m.status||"Pending",patientName:m.patientName||"Unknown Patient",reasonForVisit:m.reason||"",doctorNotes:m.notes||""})}),s.length>0?this.updateAppointments(s):console.log("No appointments found in Firebase, falling back to local data")}).catch(i=>{console.error("Error fetching appointments from Firestore:",i)})}updateAppointments(e){let t=new Date;t.setHours(0,0,0,0);let n=e.filter(i=>{let s=new Date(i.date);return s.setHours(0,0,0,0),s.getTime()===t.getTime()&&i.status==="Approved"});this.appointments=n.sort((i,s)=>i.time.localeCompare(s.time)),this.stats=Ce(B({},this.stats),{appointmentsToday:n.length,pendingConsultations:e.filter(i=>i.status==="Pending").length}),this.generateAlerts(e)}generateAlerts(e){let t=[],n=new Date;n.setHours(0,0,0,0);let i=e.filter(c=>c.status==="Pending").length;i>0&&t.push({type:"info",message:`You have ${i} pending appointment requests`,time:"Today"});let s=e.filter(c=>{let m=new Date(c.date);return m.setHours(0,0,0,0),m.getTime()===n.getTime()&&c.status==="Approved"}).length;s>0&&t.push({type:"warning",message:`You have ${s} appointments scheduled for today`,time:"Today"}),this.alerts=t}ensureFullUrl(e){return e&&(e.startsWith("data:image")||e.startsWith("http"))?e:"/hospital.svg"}navigateTo(e){this.router.navigate([e])}logout(){this.authService.logout()}loadLocalPatientCount(){let e=this.authService.getDoctorId();if(!e)return;let t=this.db.doctorPatientTable().filter(s=>s.doctor_id===e),n=new Set(t.map(s=>s.patient_id));this.db.appointmentTable().filter(s=>s.doctor_id===e&&s.patient_id).forEach(s=>{s.patient_id&&this.db.userTable().some(m=>m.id===s.patient_id)&&n.add(s.patient_id)}),this.stats.totalPatients===0&&(this.stats.totalPatients=n.size)}loadLocalAppointments(){let e=this.authService.getDoctorId();if(!e)return;let t=this.db.appointmentTable().filter(n=>n.doctor_id===e).map(n=>{let i=this.db.patientTable().find(c=>c.id===n.patient_id),s=i?this.db.userTable().find(c=>c.id===i.user_id):null;return Ce(B({},n),{patientName:s?`${s.firstname} ${s.lastname}`:"Unknown Patient"})});this.updateAppointments(t)}static \u0275fac=function(t){return new(t||o)(P(le),P(sr),P(rt),P(j),P(He),P(Be))};static \u0275cmp=E({type:o,selectors:[["app-dashboard"]],decls:69,vars:19,consts:[[1,"dashboard-container"],[1,"sidebar"],[1,"logo-section"],[1,"logo"],[1,"primary"],[1,"secondary"],[1,"nav-menu"],["routerLink","/dashboard","routerLinkActive","active",1,"nav-item","active"],[1,"bi","bi-grid-1x2-fill","nav-icon"],["routerLink","/doctors-patient","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-people-fill","nav-icon"],["routerLink","/doctors","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-person-badge-fill","nav-icon"],["routerLink","/appointments","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-calendar2-week-fill","nav-icon"],["routerLink","/settings","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-gear-fill","nav-icon"],[1,"logout-section"],[1,"logout-button",3,"click"],[1,"bi","bi-box-arrow-right","nav-icon"],[1,"main-content"],[1,"header"],[1,"welcome-section"],[1,"profile-image"],["alt","Doctor's profile picture",1,"profile-pic",3,"src"],[1,"welcome-text"],[1,"greeting"],["class","doctor-info",4,"ngIf"],[1,"stats-grid"],["class","stat-card",3,"click",4,"ngFor","ngForOf"],[1,"info-grid"],[1,"schedule-card"],[1,"card-header"],[1,"bi","bi-three-dots","more-icon",3,"click"],[1,"appointments-list"],["class","appointment-item",4,"ngFor","ngForOf"],["class","no-appointments",4,"ngIf"],[1,"alerts-card"],[1,"bi","bi-three-dots","more-icon"],[1,"alerts-list"],["class","alert-item",4,"ngFor","ngForOf"],["class","no-alerts",4,"ngIf"],[1,"development-tools-section",2,"margin-top","2rem"],[1,"doctor-info"],["class","specialization",4,"ngIf"],["class","email",4,"ngIf"],["class","phone",4,"ngIf"],["class","address",4,"ngIf"],["class","bio",4,"ngIf"],[1,"specialization"],[1,"bi","bi-award"],[1,"email"],[1,"bi","bi-envelope",2,"color","#199A8E"],[1,"phone"],[1,"bi","bi-telephone"],[1,"address"],[1,"bi","bi-geo-alt"],[1,"bio"],[1,"bi","bi-file-person"],[1,"stat-card",3,"click"],[1,"stat-info"],[1,"stat-label"],[1,"stat-value"],[1,"stat-icon"],[1,"appointment-item"],[1,"time"],[1,"appointment-details"],[1,"patient-name"],[1,"appointment-type"],[1,"appointment-status"],[1,"bi",3,"ngClass"],[1,"no-appointments"],[1,"alert-item"],[1,"alert-indicator"],[1,"alert-details"],[1,"alert-message"],[1,"alert-time"],[1,"no-alerts"]],template:function(t,n){t&1&&(g(0,"app-firebase-reset-button"),r(1,"div",0)(2,"div",1)(3,"div",2)(4,"div",3)(5,"span",4),l(6,"Med"),a(),r(7,"span",5),l(8,"Secura"),a()()(),r(9,"nav",6)(10,"a",7),g(11,"i",8),r(12,"span"),l(13,"Dashboard"),a()(),r(14,"a",9),g(15,"i",10),r(16,"span"),l(17,"Patients"),a()(),r(18,"a",11),g(19,"i",12),r(20,"span"),l(21,"Doctors"),a()(),r(22,"a",13),g(23,"i",14),r(24,"span"),l(25,"Appointments"),a()(),r(26,"a",15),g(27,"i",16),r(28,"span"),l(29,"Settings"),a()()(),r(30,"div",17)(31,"a",18),h("click",function(){return n.logout()}),g(32,"i",19),r(33,"span"),l(34,"Logout"),a()()()(),r(35,"div",20)(36,"header",21)(37,"div",22)(38,"div",23),g(39,"img",24),a(),r(40,"div",25)(41,"h1")(42,"span",26),l(43,"Hello, Dr. "),a(),l(44),a(),r(45,"p"),l(46),a(),_(47,Ys,6,5,"div",27),a()()(),r(48,"div",28),_(49,Hs,8,5,"div",29),a(),r(50,"div",30)(51,"div",31)(52,"div",32)(53,"h2"),l(54,"Today's Schedule"),a(),r(55,"i",33),h("click",function(){return n.navigateTo("/appointments")}),a()(),r(56,"div",34),_(57,Zs,13,13,"div",35)(58,Ks,2,0,"div",36),a()(),r(59,"div",37)(60,"div",32)(61,"h2"),l(62,"Recent Alerts"),a(),g(63,"i",38),a(),r(64,"div",39),_(65,Qs,8,9,"div",40)(66,Gs,2,0,"div",41),a()()(),r(67,"div",42),g(68,"app-firebase-reset"),a()()()),t&2&&(d(39),p("src",(n.doctorInfo==null?null:n.doctorInfo.profilePicture)||"/hospital.svg",Tt),d(5),T(n.getDoctorFullName()),d(2),M("You have ",n.stats.appointmentsToday," appointments scheduled for today"),d(),p("ngIf",n.doctorInfo),d(2),p("ngForOf",gn(15,Ls,mt(9,Ns,n.stats.totalPatients),mt(11,zs,n.stats.appointmentsToday),mt(13,Rs,n.stats.pendingConsultations))),d(8),p("ngForOf",n.appointments),d(),p("ngIf",n.appointments.length===0),d(7),p("ngForOf",n.alerts),d(),p("ngIf",n.alerts.length===0))},dependencies:[A,gt,Se,U,un,_e,ft,rr,ar],styles:["[_nghost-%COMP%]{--primary-color: #199A8E;--primary-color-80: rgba(25, 154, 142, .8);--text-primary: #111827;--text-secondary: #6B7280;--text-success: #166534;--bg-white: #ffffff;--bg-light: #F9FAFB;--bg-success-light: #DCFCE7;--bg-icon-light: #F0F9FF;--border-color: #E5E7EB;--shadow-sm: 0px 1px 2px rgba(0, 0, 0, .05);--border-radius-lg: 12px;--border-radius-md: 8px;--border-radius-full: 9999px}.bi-clock-fill[_ngcontent-%COMP%]{color:#199a8e;margin-right:.5rem}[_nghost-%COMP%]{display:flex;width:100vw;height:100vh;overflow:hidden}.dashboard-container[_ngcontent-%COMP%]{display:flex;width:100%;height:100%}.search-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--text-secondary)}.more-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--primary-color);cursor:pointer}.stat-icon[_ngcontent-%COMP%]{width:40px;height:40px;background:var(--bg-icon-light);border-radius:var(--border-radius-full);display:flex;align-items:center;justify-content:center}.stat-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px;color:var(--primary-color)}.main-content[_ngcontent-%COMP%]{flex:1;padding:16px;display:flex;flex-direction:column;min-width:0}.header[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:16px}.welcome-section[_ngcontent-%COMP%]{background:var(--bg-white);padding:20px;border-radius:var(--border-radius-lg);box-shadow:var(--shadow-sm);display:flex;align-items:center;gap:20px}.profile-image[_ngcontent-%COMP%]{width:70px;height:70px;border-radius:var(--border-radius-full);border:4px solid #E0F2FE;overflow:hidden;background-size:cover;background-position:center;flex-shrink:0}.profile-image[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;overflow:hidden;margin-right:20px;border:3px solid #fff;box-shadow:0 2px 4px #0000001a}.profile-pic[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.doctor-info[_ngcontent-%COMP%]{margin-top:10px;font-size:.9rem;color:#666}.doctor-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:5px 0;display:flex;align-items:center}.doctor-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;color:var(--primary-color)}.specialization[_ngcontent-%COMP%]{color:#2c3e50;font-weight:500}.phone[_ngcontent-%COMP%], .address[_ngcontent-%COMP%], .email[_ngcontent-%COMP%]{font-size:.85rem}.email[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#4a5568}.bio[_ngcontent-%COMP%]{font-size:.85rem;line-height:1.4;max-width:500px;white-space:normal;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.welcome-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;line-height:32px;font-weight:700;margin:0}.greeting[_ngcontent-%COMP%]{color:var(--primary-color)}.welcome-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:14px;margin:4px 0 0}.search-bar[_ngcontent-%COMP%]{display:flex;align-items:center;background:var(--bg-white);border:1px solid var(--border-color);border-radius:var(--border-radius-md);padding:10px}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{flex:1;border:none;outline:none;margin-left:12px;font-size:14px;color:var(--text-secondary);min-width:0}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:16px;margin-bottom:16px}.stat-card[_ngcontent-%COMP%]{background:var(--bg-white);padding:16px;border-radius:var(--border-radius-lg);box-shadow:var(--shadow-sm);display:flex;justify-content:space-between;align-items:flex-start}.stat-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-width:0}.stat-label[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:13px;margin-bottom:6px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.stat-value[_ngcontent-%COMP%]{color:var(--text-primary);font-size:20px;font-weight:700}.info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:16px;flex:1;min-height:0}.schedule-card[_ngcontent-%COMP%], .alerts-card[_ngcontent-%COMP%]{background:var(--bg-white);padding:16px;border-radius:var(--border-radius-lg);box-shadow:var(--shadow-sm);display:flex;flex-direction:column;min-height:0;min-width:0}.card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.card-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--text-primary);font-size:18px;font-weight:600;margin:0}.appointments-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;overflow-y:auto;flex:1}.appointment-item[_ngcontent-%COMP%]{background:var(--bg-light);padding:12px;border-radius:var(--border-radius-md);display:grid;grid-template-columns:auto 1fr auto;gap:12px;align-items:center;min-width:0}.time[_ngcontent-%COMP%]{color:var(--primary-color);font-size:14px;font-weight:500;white-space:nowrap}.appointment-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:2px;min-width:0}.patient-name[_ngcontent-%COMP%]{color:var(--text-primary);font-size:14px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.appointment-type[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:13px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.status[_ngcontent-%COMP%]{padding:4px 10px;background:var(--bg-success-light);color:var(--text-success);border-radius:var(--border-radius-full);font-size:13px;display:flex;align-items:center;gap:4px;white-space:nowrap}.status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.alerts-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;overflow-y:auto;flex:1}.alert-item[_ngcontent-%COMP%]{background:var(--bg-light);padding:12px;border-radius:var(--border-radius-md);display:flex;align-items:center;gap:12px;min-width:0}.alert-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;flex-shrink:0}.alert-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.alert-indicator.warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#eab308}.alert-indicator.info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6}.alert-indicator.error[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ef4444}.alert-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:2px;min-width:0}.alert-message[_ngcontent-%COMP%]{color:var(--text-primary);font-size:14px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.alert-time[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:13px}",".sidebar[_ngcontent-%COMP%]{width:256px;height:100%;background:var(--bg-white);border-right:1px solid var(--border-color);display:flex;flex-direction:column;flex-shrink:0}.logo-section[_ngcontent-%COMP%]{height:70px;border-bottom:1px solid var(--border-color);display:flex;align-items:center;padding:0 24px}.logo[_ngcontent-%COMP%]{font-size:22.88px;line-height:32px}.logo[_ngcontent-%COMP%]   .primary[_ngcontent-%COMP%]{color:var(--primary-color);font-weight:700}.logo[_ngcontent-%COMP%]   .secondary[_ngcontent-%COMP%]{color:var(--primary-color-80);font-weight:400}.nav-menu[_ngcontent-%COMP%]{padding:16px 0;flex:1}.nav-item[_ngcontent-%COMP%]{display:flex;align-items:center;height:44px;padding:0 24px;color:var(--text-primary);font-size:15.38px;cursor:pointer;text-decoration:none}.nav-item[_ngcontent-%COMP%]:hover{background:#f9fafb}.nav-item.active[_ngcontent-%COMP%]{background:var(--bg-icon-light);color:var(--primary-color)}.nav-icon[_ngcontent-%COMP%]{font-size:20px;margin-right:12px;color:var(--text-secondary)}.active[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{color:var(--primary-color)}.logout-section[_ngcontent-%COMP%]{height:44px;border-top:1px solid var(--border-color);display:flex;align-items:center;padding:0 24px}.logout-button[_ngcontent-%COMP%]{display:flex;align-items:center;color:var(--text-primary);font-size:15.25px;cursor:pointer;text-decoration:none}.logout-button[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{margin-right:12px}"]})};var _t=class o{constructor(e){this.http=e}apiUrl=`${Lt.apiUrl}/api/licenses`;generateLicense(e,t,n){let i=Number(e);if(isNaN(i)||i<=0)throw new Error("Invalid user count");if(!t?.trim()||!n?.trim())throw new Error("Email and subscription type are required");return this.http.post(this.apiUrl,{totalUsers:i,email:t.trim(),subscriptionType:n.trim()})}getLicenseDetails(e){return console.log("Getting license details for:",e),this.http.get(`${this.apiUrl}/${e}`)}static \u0275fac=function(t){return new(t||o)(de(ut))};static \u0275prov=be({token:o,factory:o.\u0275fac,providedIn:"root"})};function Xs(o,e){if(o&1&&(r(0,"div",47),g(1,"i",48),r(2,"p"),l(3),a()()),o&2){let t=b(2);d(3),T(t.error)}}function el(o,e){if(o&1){let t=V();r(0,"div")(1,"p",49),l(2),a(),r(3,"div",50)(4,"code",51),l(5),a(),r(6,"button",52),h("click",function(){v(t);let i=b(2);return x(i.copyLicenseKey())}),g(7,"i",53),a()(),r(8,"p",54),l(9," Please keep this key safe and use it to register additional"),g(10,"br"),l(11," doctors on your account. "),a(),r(12,"button",55),h("click",function(){v(t);let i=b(2);return x(i.renewLicense())}),l(13," Renew "),a()()}if(o&2){let t=b(2);d(2),M("Your license key will expire in ",t.daysUntilExpiry," days"),d(3),T(t.licenseKey)}}function tl(o,e){if(o&1){let t=V();r(0,"div",41),h("click",function(){v(t);let i=b();return x(i.closeLicenseModal())}),r(1,"div",42),h("click",function(i){return v(t),x(i.stopPropagation())}),r(2,"div",43)(3,"h2"),l(4,"Your License Key"),a()(),r(5,"div",44),_(6,Xs,4,1,"div",45)(7,el,14,2,"div",46),a()()()}if(o&2){let t=b();d(6),p("ngIf",t.error),d(),p("ngIf",!t.error)}}function nl(o,e){if(o&1){let t=V();r(0,"div",56),h("click",function(){v(t);let i=b();return x(i.closeUnsubscribeModal())}),r(1,"div",57),h("click",function(i){return v(t),x(i.stopPropagation())}),r(2,"div",58)(3,"h2"),l(4,"Confirm Unsubscribe"),a()(),r(5,"div",44)(6,"div",59),g(7,"i",60),a(),r(8,"p",61),l(9,"Are you sure you want to unsubscribe?"),a(),r(10,"div",62),l(11," This action will: "),r(12,"ul")(13,"li"),l(14,"Cancel your current subscription"),a(),r(15,"li"),l(16,"Temporarily suspend access to all features"),a(),r(17,"li"),l(18,"Archive your account data"),a()(),r(19,"p",63),l(20," Don't worry! If you resubscribe you'll be able to restore all your practice data, patient records, and account settings. "),a()(),r(21,"div",64)(22,"button",65),h("click",function(){v(t);let i=b();return x(i.closeUnsubscribeModal())}),l(23," Cancel "),a(),r(24,"button",66),h("click",function(){v(t);let i=b();return x(i.confirmUnsubscribe())}),l(25," Yes, Unsubscribe "),a()()()()()}}var Wn=class o{constructor(e,t){this.router=e;this.licenseService=t;let n=localStorage.getItem("licenseKey");n&&(this.licenseKey=n,this.licenseService.getLicenseDetails(n).subscribe({next:i=>{this.licenseKey=i.licenseKey;let s=new Date(i.createdAt),c=new Date(s);c.setDate(c.getDate()+30);let m=new Date,u=Math.abs(c.getTime()-m.getTime());this.daysUntilExpiry=Math.ceil(u/(1e3*60*60*24))},error:i=>{this.error="Failed to fetch license details"}}))}showLicenseModal=!1;showUnsubscribeModal=!1;licenseKey="";daysUntilExpiry=30;error="";ngOnInit(){}navigateToDoctor(){this.router.navigate(["/doctor-information"])}changePassword(){this.router.navigate(["/change-password"])}viewLicense(){this.showLicenseModal=!0;let e=localStorage.getItem("licenseKey"),t=localStorage.getItem("licenseCreatedAt");if(e&&t){this.licenseKey=e;let n=new Date(t),i=new Date(n);i.setDate(i.getDate()+30);let s=new Date,c=Math.abs(i.getTime()-s.getTime());this.daysUntilExpiry=Math.ceil(c/(1e3*60*60*24))}else this.error="No active license found. Please subscribe to get a license key.",setTimeout(()=>{this.closeLicenseModal(),this.router.navigate(["/subscription"])},2e3)}closeLicenseModal(){this.showLicenseModal=!1,this.error=""}copyLicenseKey(){navigator.clipboard.writeText(this.licenseKey)}renewLicense(){this.router.navigate(["/subscription"])}showUnsubscribeConfirmation(){this.showUnsubscribeModal=!0}closeUnsubscribeModal(){this.showUnsubscribeModal=!1}confirmUnsubscribe(){localStorage.clear(),this.router.navigate(["/"])}logout(){this.router.navigate(["/"])}static \u0275fac=function(t){return new(t||o)(P(j),P(_t))};static \u0275cmp=E({type:o,selectors:[["app-settings"]],decls:99,vars:2,consts:[[1,"page-container"],[1,"sidebar"],[1,"logo-section"],[1,"logo"],[1,"primary"],[1,"secondary"],[1,"nav-menu"],["routerLink","/dashboard","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-grid-1x2-fill","nav-icon"],["routerLink","/doctors-patient","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-people-fill","nav-icon"],["routerLink","/doctors","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-person-badge-fill","nav-icon"],["routerLink","/appointments","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-calendar2-week-fill","nav-icon"],["routerLink","/settings","routerLinkActive","active",1,"nav-item","active"],[1,"bi","bi-gear-fill","nav-icon"],[1,"logout-section"],[1,"logout-button",3,"click"],[1,"bi","bi-box-arrow-right","nav-icon"],[1,"main-content"],[1,"settings-container"],["class","license-modal-overlay",3,"click",4,"ngIf"],["class","unsubscribe-modal-overlay",3,"click",4,"ngIf"],[1,"settings-layout"],[1,"settings-section"],[1,"section-header"],[1,"bi","bi-person-fill","settings-icon"],[1,"settings-content"],[1,"setting-item",3,"click"],[1,"item-icon"],[1,"bi","bi-person-vcard"],[1,"setting-info"],[1,"bi","bi-chevron-right"],[1,"bi","bi-shield-lock","settings-icon"],[1,"bi","bi-key"],[1,"bi","bi-key-fill","settings-icon"],[1,"bi","bi-card-text"],[1,"bi","bi-person-x-fill","settings-icon","danger"],[1,"setting-item","danger",3,"click"],[1,"bi","bi-trash"],[1,"license-modal-overlay",3,"click"],[1,"license-modal",3,"click"],[1,"modal-header"],[1,"modal-content"],["class","error-message",4,"ngIf"],[4,"ngIf"],[1,"error-message"],[1,"bi","bi-exclamation-circle"],[1,"expiry-text"],[1,"license-key-container"],[1,"license-key"],["title","Copy license key",1,"copy-button",3,"click"],[1,"bi","bi-copy"],[1,"info-text"],[1,"renew-button",3,"click"],[1,"unsubscribe-modal-overlay",3,"click"],[1,"unsubscribe-modal",3,"click"],[1,"modal-header","danger"],[1,"warning-icon"],[1,"bi","bi-exclamation-triangle"],[1,"warning-title"],[1,"warning-text"],[1,"recovery-note"],[1,"modal-actions"],[1,"cancel-button",3,"click"],[1,"confirm-button",3,"click"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"span",4),l(5,"Med"),a(),r(6,"span",5),l(7,"Secura"),a()()(),r(8,"nav",6)(9,"a",7),g(10,"i",8),r(11,"span"),l(12,"Dashboard"),a()(),r(13,"a",9),g(14,"i",10),r(15,"span"),l(16,"Patients"),a()(),r(17,"a",11),g(18,"i",12),r(19,"span"),l(20,"Doctors"),a()(),r(21,"a",13),g(22,"i",14),r(23,"span"),l(24,"Appointments"),a()(),r(25,"a",15),g(26,"i",16),r(27,"span"),l(28,"Settings"),a()()(),r(29,"div",17)(30,"a",18),h("click",function(){return n.logout()}),g(31,"i",19),r(32,"span"),l(33,"Logout"),a()()()(),r(34,"div",20)(35,"div",21),_(36,tl,8,2,"div",22)(37,nl,26,0,"div",23),r(38,"div",24)(39,"div",25)(40,"div",26),g(41,"i",27),r(42,"h2"),l(43,"Profile Settings"),a()(),r(44,"div",28)(45,"div",29),h("click",function(){return n.navigateToDoctor()}),r(46,"div",30),g(47,"i",31),a(),r(48,"div",32)(49,"h3"),l(50,"Doctor Information"),a(),r(51,"p"),l(52,"Update your personal and professional details"),a()(),g(53,"i",33),a()()(),r(54,"div",25)(55,"div",26),g(56,"i",34),r(57,"h2"),l(58,"Security Settings"),a()(),r(59,"div",28)(60,"div",29),h("click",function(){return n.changePassword()}),r(61,"div",30),g(62,"i",35),a(),r(63,"div",32)(64,"h3"),l(65,"Change Password"),a(),r(66,"p"),l(67,"Update your account password and security preferences"),a()(),g(68,"i",33),a()()(),r(69,"div",25)(70,"div",26),g(71,"i",36),r(72,"h2"),l(73,"License Management"),a()(),r(74,"div",28)(75,"div",29),h("click",function(){return n.viewLicense()}),r(76,"div",30),g(77,"i",37),a(),r(78,"div",32)(79,"h3"),l(80,"View License"),a(),r(81,"p"),l(82,"Access and manage your MedSecura license details"),a()(),g(83,"i",33),a()()(),r(84,"div",25)(85,"div",26),g(86,"i",38),r(87,"h2"),l(88,"Account Management"),a()(),r(89,"div",28)(90,"div",39),h("click",function(){return n.showUnsubscribeConfirmation()}),r(91,"div",30),g(92,"i",40),a(),r(93,"div",32)(94,"h3"),l(95,"Unsubscribe"),a(),r(96,"p"),l(97,"Cancel your MedSecura subscription and manage account deletion"),a()(),g(98,"i",33),a()()()()()()()),t&2&&(d(36),p("ngIf",n.showLicenseModal),d(),p("ngIf",n.showUnsubscribeModal))},dependencies:[A,U,_e,ft],styles:['[_nghost-%COMP%]{--primary-color: #199A8E;--primary-color-80: rgba(25, 154, 142, .8);--primary-hover: #168176;--primary-bg-hover: rgba(25, 154, 142, .08);--text-primary: #111827;--text-secondary: #6B7280;--text-success: #166534;--bg-white: #ffffff;--bg-light: #F9FAFB;--bg-hover: #F3F4F6;--border-color: #E5E7EB;--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / .05);--shadow-md: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--border-radius-lg: 16px;--border-radius-md: 12px;--danger-color: #DC2626;--danger-hover: #B91C1C;--danger-bg: #FEF2F2;--danger-bg-hover: #FEE2E2;--transition-fast: .15s ease;--transition-normal: .25s cubic-bezier(.4, 0, .2, 1);--transition-bounce: .5s cubic-bezier(.68, -.55, .265, 1.55)}[_nghost-%COMP%]{display:flex;width:100vw;height:100vh;overflow:hidden}.page-container[_ngcontent-%COMP%]{display:flex;width:100%;height:100%}.sidebar[_ngcontent-%COMP%]{width:256px;height:100vh;background:var(--bg-white);border-right:1px solid var(--border-color);display:flex;flex-direction:column;position:fixed;left:0;top:0;z-index:100}.logo-section[_ngcontent-%COMP%]{height:70px;border-bottom:1px solid var(--border-color);display:flex;align-items:center;padding:0 24px}.logo[_ngcontent-%COMP%]{font-size:22.88px;line-height:32px}.logo[_ngcontent-%COMP%]   .primary[_ngcontent-%COMP%]{color:var(--primary-color);font-weight:700}.logo[_ngcontent-%COMP%]   .secondary[_ngcontent-%COMP%]{color:var(--primary-color-80);font-weight:400}.nav-menu[_ngcontent-%COMP%]{padding:16px 0;flex:1}.nav-item[_ngcontent-%COMP%]{display:flex;align-items:center;height:44px;padding:0 24px;color:var(--text-primary);font-size:15.38px;cursor:pointer;text-decoration:none}.nav-item[_ngcontent-%COMP%]:hover{background:var(--bg-light)}.nav-item.active[_ngcontent-%COMP%]{background:var(--bg-icon-light);color:var(--primary-color)}.nav-icon[_ngcontent-%COMP%]{font-size:20px;margin-right:12px;color:var(--text-secondary)}.active[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{color:var(--primary-color)}.logout-section[_ngcontent-%COMP%]{height:44px;border-top:1px solid var(--border-color);display:flex;align-items:center;padding:0 24px}.logout-button[_ngcontent-%COMP%]{display:flex;align-items:center;color:var(--text-primary);font-size:15.25px;cursor:pointer;text-decoration:none}.main-content[_ngcontent-%COMP%]{margin-left:256px;flex:1;height:100vh;overflow-y:auto;background:var(--bg-light);transition:var(--transition-all)}.settings-container[_ngcontent-%COMP%]{padding:32px;min-height:100%;display:flex;flex-direction:column;align-items:center;animation:_ngcontent-%COMP%_fadeIn .5s ease-out;max-width:1600px;margin:0 auto;width:100%}.page-title[_ngcontent-%COMP%]{font-size:28px;font-weight:600;color:var(--text-primary);margin:0 0 40px;text-align:center;padding:16px 0;position:relative;width:100%}.page-title[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:50%;transform:translate(-50%);width:80px;height:3px;background:var(--primary-color);border-radius:3px}.settings-layout[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,minmax(0,600px));gap:32px;width:100%;justify-content:center;align-items:start;padding:0 16px}.settings-section[_ngcontent-%COMP%]{background:var(--bg-white);border-radius:16px;border:1px solid var(--border-color);box-shadow:var(--shadow-sm);transition:transform var(--transition-normal),box-shadow var(--transition-normal);overflow:hidden;display:flex;flex-direction:column;position:relative;height:100%;min-height:320px}.settings-section[_ngcontent-%COMP%]:hover{box-shadow:var(--shadow-md);transform:translateY(-2px)}.settings-section[_ngcontent-%COMP%]:hover   .section-header[_ngcontent-%COMP%]{background:var(--bg-light)}.settings-section[_ngcontent-%COMP%]:hover   .settings-icon[_ngcontent-%COMP%]{color:var(--primary-color);transform:scale(1.1) rotate(-5deg)}.settings-section[_ngcontent-%COMP%]:hover   .settings-icon.danger[_ngcontent-%COMP%]{color:var(--danger-color)}.settings-section[_ngcontent-%COMP%]:hover   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--primary-color)}.settings-section[_ngcontent-%COMP%]:hover   .danger[_ngcontent-%COMP%] ~ h2[_ngcontent-%COMP%]{color:var(--danger-color)}.section-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;padding:28px 24px;background:var(--bg-white);border-bottom:1px solid var(--border-color);transition:background-color var(--transition-normal);text-align:center}.settings-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:16px;color:var(--text-secondary);transition:color var(--transition-normal),transform var(--transition-bounce)}.section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:var(--text-primary);margin:0;transition:color var(--transition-normal)}.settings-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;justify-content:center}.setting-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;padding:28px 24px;cursor:pointer;background:var(--bg-white);position:relative;overflow:hidden;transition:all var(--transition-normal);gap:20px}.setting-item[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background:var(--primary-bg-hover);opacity:0;transition:opacity var(--transition-normal);pointer-events:none;border-radius:8px;margin:4px}.setting-item[_ngcontent-%COMP%]:hover:before{opacity:1}.setting-item.danger[_ngcontent-%COMP%]:hover:before{background:var(--danger-bg-hover)}.item-icon[_ngcontent-%COMP%]{width:52px;height:52px;display:flex;align-items:center;justify-content:center;border-radius:12px;background:var(--bg-light);color:var(--text-secondary);font-size:22px;transition:all var(--transition-normal);position:relative;z-index:1;flex-shrink:0}.setting-item[_ngcontent-%COMP%]:hover   .item-icon[_ngcontent-%COMP%]{background:var(--primary-bg-hover);color:var(--primary-color);transform:scale(1.1) translateY(-2px)}.setting-item.danger[_ngcontent-%COMP%]:hover   .item-icon[_ngcontent-%COMP%]{background:var(--danger-bg);color:var(--danger-color)}.setting-info[_ngcontent-%COMP%]{flex:1;max-width:300px;position:relative;z-index:1;text-align:left}.setting-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--text-primary);margin:0 0 8px;transition:color var(--transition-normal)}.setting-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:var(--text-secondary);margin:0;line-height:1.5;transition:color var(--transition-normal)}.setting-item[_ngcontent-%COMP%]:hover   .setting-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--primary-color);transform:translateY(-1px)}.setting-item.danger[_ngcontent-%COMP%]:hover   .setting-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--danger-color)}.setting-item[_ngcontent-%COMP%]:hover   .setting-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--primary-color-80)}.setting-item.danger[_ngcontent-%COMP%]:hover   .setting-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--danger-hover)}.bi-chevron-right[_ngcontent-%COMP%]{font-size:22px;color:var(--text-secondary);opacity:0;transform:translate(-10px);transition:all var(--transition-normal);position:relative;z-index:1;width:24px;height:24px;display:flex;align-items:center;justify-content:center;flex-shrink:0}.setting-item[_ngcontent-%COMP%]:hover   .bi-chevron-right[_ngcontent-%COMP%]{transform:translate(0);opacity:1;color:var(--primary-color)}.setting-item.danger[_ngcontent-%COMP%]:hover   .bi-chevron-right[_ngcontent-%COMP%]{color:var(--danger-color)}.setting-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .setting-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{transition:all var(--transition-normal)}.setting-item[_ngcontent-%COMP%]:hover{transform:translateY(-1px)}.settings-icon[_ngcontent-%COMP%], .item-icon[_ngcontent-%COMP%]{transition:all var(--transition-normal),transform var(--transition-bounce),background-color var(--transition-normal)}.section-header[_ngcontent-%COMP%], .setting-item[_ngcontent-%COMP%], .bi-chevron-right[_ngcontent-%COMP%]{transition:all var(--transition-normal)}@media screen and (max-width: 1400px){.settings-layout[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,500px));gap:24px}.settings-section[_ngcontent-%COMP%]{min-height:300px}}@media screen and (max-width: 1200px){.settings-layout[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,450px));gap:20px}.settings-section[_ngcontent-%COMP%]{min-height:280px}}@media screen and (max-width: 992px){.settings-layout[_ngcontent-%COMP%]{grid-template-columns:minmax(0,600px);padding:0}.settings-section[_ngcontent-%COMP%]{min-height:240px}}@media screen and (max-width: 768px){.settings-container[_ngcontent-%COMP%]{padding:24px 16px}.page-title[_ngcontent-%COMP%]{font-size:24px;margin-bottom:32px}.settings-layout[_ngcontent-%COMP%]{gap:16px}.section-header[_ngcontent-%COMP%], .setting-item[_ngcontent-%COMP%]{padding:24px 20px}.item-icon[_ngcontent-%COMP%]{width:48px;height:48px;font-size:20px}}@media screen and (max-width: 480px){.settings-container[_ngcontent-%COMP%]{padding:20px 12px}.page-title[_ngcontent-%COMP%]{font-size:22px;margin-bottom:24px}.section-header[_ngcontent-%COMP%], .setting-item[_ngcontent-%COMP%]{padding:20px 16px}.item-icon[_ngcontent-%COMP%]{width:44px;height:44px;font-size:18px}.setting-info[_ngcontent-%COMP%]{max-width:none}}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_slideUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.license-modal-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#00000080;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);display:flex;align-items:center;justify-content:center;z-index:1000}.license-modal[_ngcontent-%COMP%]{width:448px;background:#fff;box-shadow:0 8px 10px -6px #0000001a;border-radius:8px;overflow:hidden;border:1px #E4E4E7 solid;animation:_ngcontent-%COMP%_modalFadeIn .3s ease-out}.modal-header[_ngcontent-%COMP%]{width:100%;height:80px;background:#199a8e;border-top-left-radius:8px;border-top-right-radius:8px;display:flex;align-items:center;justify-content:center}.modal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#fff;font-size:23.81px;font-family:Inter,sans-serif;font-weight:700;line-height:32px;margin:0}.modal-content[_ngcontent-%COMP%]{padding:24px;display:flex;flex-direction:column;align-items:center}.expiry-text[_ngcontent-%COMP%]{color:#09090b;font-size:16px;font-family:Inter,sans-serif;font-weight:600;line-height:24px;margin-bottom:8px}.license-key-container[_ngcontent-%COMP%]{width:398px;height:72px;background:#f3f4f6;border-radius:6px;display:flex;align-items:center;justify-content:space-between;padding:0 16px;margin-bottom:16px}.license-key[_ngcontent-%COMP%]{color:#09090b;font-size:14px;font-family:Roboto Mono,monospace;font-weight:400;line-height:20px}.copy-button[_ngcontent-%COMP%]{width:40px;height:40px;background:transparent;border:none;border-radius:6px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:background-color .2s}.copy-button[_ngcontent-%COMP%]:hover{background:#199a8e1a}.copy-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px;color:#199a8e}.info-text[_ngcontent-%COMP%]{color:#6b7280;font-size:13.67px;font-family:Inter,sans-serif;font-weight:400;line-height:20px;text-align:center;margin-bottom:24px}.renew-button[_ngcontent-%COMP%]{display:block;margin:1.5rem auto;padding:.75rem 2rem;background-color:var(--primary-color);color:#fff;border:none;border-radius:.375rem;font-weight:500;cursor:pointer;transition:background-color .2s}.renew-button[_ngcontent-%COMP%]:hover{background-color:var(--primary-color-80)}@keyframes _ngcontent-%COMP%_modalFadeIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.setting-item.danger[_ngcontent-%COMP%], .setting-item.danger[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .setting-item.danger[_ngcontent-%COMP%]   .setting-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--danger-color)}.unsubscribe-modal-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#00000080;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);display:flex;align-items:center;justify-content:center;z-index:1000}.unsubscribe-modal[_ngcontent-%COMP%]{width:448px;background:#fff;box-shadow:0 8px 10px -6px #0000001a;border-radius:8px;overflow:hidden;border:1px #E4E4E7 solid;animation:_ngcontent-%COMP%_modalFadeIn .3s ease-out}.modal-header.danger[_ngcontent-%COMP%]{background:#dc2626}.warning-icon[_ngcontent-%COMP%]{width:64px;height:64px;background:#fef2f2;border-radius:50%;display:flex;align-items:center;justify-content:center;margin:16px 0}.warning-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:32px;color:#dc2626}.warning-title[_ngcontent-%COMP%]{color:#09090b;font-size:18px;font-family:Inter,sans-serif;font-weight:600;line-height:24px;margin-bottom:16px}.warning-text[_ngcontent-%COMP%]{color:#6b7280;font-size:14px;font-family:Inter,sans-serif;font-weight:400;line-height:20px;text-align:left;margin-bottom:24px}.warning-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:12px 0;padding-left:20px}.warning-text[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px}.recovery-note[_ngcontent-%COMP%]{margin-top:16px;padding:12px;background:#f0fdf4;border-left:4px solid #22C55E;border-radius:4px;color:#166534;font-size:14px;font-family:Inter,sans-serif;line-height:1.5}.modal-actions[_ngcontent-%COMP%]{display:flex;gap:12px;justify-content:center}.cancel-button[_ngcontent-%COMP%]{width:120px;height:40px;background:#f3f4f6;border:none;border-radius:6px;color:#374151;font-size:14px;font-family:Inter,sans-serif;font-weight:500;cursor:pointer;transition:background-color .2s}.cancel-button[_ngcontent-%COMP%]:hover{background:#e5e7eb}.confirm-button[_ngcontent-%COMP%]{width:180px;height:40px;background:#dc2626;border:none;border-radius:6px;color:#fff;font-size:14px;font-family:Inter,sans-serif;font-weight:500;cursor:pointer;transition:background-color .2s}.confirm-button[_ngcontent-%COMP%]:hover{background:#b91c1c}.loading-spinner[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem}.spinner[_ngcontent-%COMP%]{font-size:2rem;animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.error-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#dc3545;padding:1rem;background-color:#f8d7da;border-radius:4px;margin:1rem 0}.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem}']})};var $n=class o{constructor(e,t){this.fb=e;this.router=t;this.practiceForm=this.fb.group({practiceName:["",S.required],doctorName:["",S.required],email:["",[S.required,S.email]],phone:["",[S.required,S.pattern(/^\+?([0-9\s-]){10,}$/)]],address:["",S.required]})}practiceForm;ngOnInit(){}onSubmit(){if(this.practiceForm.valid){let e=this.practiceForm.value;localStorage.setItem("practiceData",JSON.stringify(e)),this.router.navigate(["/payment/payment-info"])}}static \u0275fac=function(t){return new(t||o)(P(Ye),P(j))};static \u0275cmp=E({type:o,selectors:[["app-practice-info"]],decls:44,vars:2,consts:[[1,"payment-container"],[1,"payment-card"],[1,"header"],[1,"progress-dots"],[1,"dot","active"],[1,"dot"],[1,"payment-form",3,"ngSubmit","formGroup"],[1,"form-row"],[1,"form-group"],["for","practiceName"],[1,"input-with-icon"],[1,"practice-icon"],["type","text","id","practiceName","formControlName","practiceName","placeholder","Medical Practice Name"],["for","doctorName"],[1,"doctor-icon"],["type","text","id","doctorName","formControlName","doctorName","placeholder","Dr. John Doe"],["for","email"],[1,"email-icon"],["type","email","id","email","formControlName","email","placeholder","<EMAIL>"],["for","phone"],[1,"phone-icon"],["type","tel","id","phone","formControlName","phone","placeholder","+27 123 456 789"],[1,"form-group","full-width"],["for","address"],[1,"address-icon"],["id","address","formControlName","address","placeholder","Enter your practice address","rows","3"],["type","submit",1,"submit-button",3,"disabled"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2"),l(4,"Complete Your Payment"),a(),r(5,"div",3),g(6,"div",4)(7,"div",5),a()(),r(8,"form",6),h("ngSubmit",function(){return n.onSubmit()}),r(9,"div",7)(10,"div",8)(11,"label",9),l(12,"Practice Name"),a(),r(13,"div",10),g(14,"i",11)(15,"input",12),a()(),r(16,"div",8)(17,"label",13),l(18,"Doctor's Name"),a(),r(19,"div",10),g(20,"i",14)(21,"input",15),a()()(),r(22,"div",7)(23,"div",8)(24,"label",16),l(25,"Email"),a(),r(26,"div",10),g(27,"i",17)(28,"input",18),a()(),r(29,"div",8)(30,"label",19),l(31,"Phone Number"),a(),r(32,"div",10),g(33,"i",20)(34,"input",21),a()()(),r(35,"div",22)(36,"label",23),l(37,"Practice Address"),a(),r(38,"div",10),g(39,"i",24),r(40,"textarea",25),l(41,"          "),a()()(),r(42,"button",26),l(43," Continue to Payment "),a()()()()),t&2&&(d(8),p("formGroup",n.practiceForm),d(34),p("disabled",!n.practiceForm.valid))},dependencies:[A,Fe,Ae,H,Z,De,We,$e],styles:[".payment-container[_ngcontent-%COMP%]{width:100%;min-height:832px;background:#f9fafb;display:flex;justify-content:center;align-items:center;padding:20px}.payment-card[_ngcontent-%COMP%]{width:736px;background:#fff;box-shadow:0 4px 6px -4px #0000001a;border-radius:8px;padding:32px}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:32px}.header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#111827;font-size:22.69px;font-weight:700;line-height:32px;margin:0}.progress-dots[_ngcontent-%COMP%]{display:flex;gap:8px}.dot[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:50%;background:#d1d5db}.dot.active[_ngcontent-%COMP%]{background:#199a8e}.payment-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.form-row[_ngcontent-%COMP%]{display:flex;gap:24px}.form-group[_ngcontent-%COMP%]{flex:1}.form-group.full-width[_ngcontent-%COMP%]{width:100%}label[_ngcontent-%COMP%]{display:block;color:#374151;font-size:13.78px;font-weight:500;line-height:20px;margin-bottom:4px}.input-with-icon[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center}input[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{width:100%;padding:11.5px 12px 11.5px 41px;background:#fff;border:1px solid #E5E7EB;border-radius:8px;color:#111827;font-size:15.25px;line-height:19px}textarea[_ngcontent-%COMP%]{min-height:90px;resize:vertical}input[_ngcontent-%COMP%]::placeholder, textarea[_ngcontent-%COMP%]::placeholder{color:#9ca3af}.input-with-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:12px;width:20px;height:20px;opacity:.6}.submit-button[_ngcontent-%COMP%]{width:100%;height:40px;background:#199a8e;color:#fff;border:none;border-radius:8px;font-size:20px;font-weight:700;line-height:24px;cursor:pointer;transition:background-color .2s}.submit-button[_ngcontent-%COMP%]:hover{background:#158276}.submit-button[_ngcontent-%COMP%]:disabled{background:#a7a7a7;cursor:not-allowed}.practice-icon[_ngcontent-%COMP%], .doctor-icon[_ngcontent-%COMP%], .email-icon[_ngcontent-%COMP%], .phone-icon[_ngcontent-%COMP%], .address-icon[_ngcontent-%COMP%]{display:inline-block;width:20px;height:20px;background-size:contain;background-repeat:no-repeat;background-position:center;opacity:.6}@media (max-width: 768px){.payment-card[_ngcontent-%COMP%]{width:100%;max-width:736px}.form-row[_ngcontent-%COMP%]{flex-direction:column;gap:16px}}"]})};var il=o=>({processing:o}),Ji=(o,e)=>({active:o,completed:e});function ol(o,e){o&1&&(dn(0),l(1,"\u2713"),mn())}function rl(o,e){if(o&1&&(r(0,"div",29)(1,"div",30)(2,"div",31),_(3,ol,2,0,"ng-container",23),a(),r(4,"div",32),l(5),a(),r(6,"div",33)(7,"div",34),g(8,"div",35),r(9,"div",36),l(10,"Processing"),a()(),g(11,"div",37),r(12,"div",34),g(13,"div",35),r(14,"div",36),l(15,"Verifying"),a()(),g(16,"div",37),r(17,"div",34),g(18,"div",35),r(19,"div",36),l(20,"Complete"),a()()()()()),o&2){let t=b();d(2),p("ngClass",t.paymentStep),d(),p("ngIf",t.paymentStep==="success"),d(2),T(t.processingMessage),d(2),p("ngClass",Dt(6,Ji,t.paymentStep==="processing",t.paymentStep==="verifying"||t.paymentStep==="success")),d(5),p("ngClass",Dt(9,Ji,t.paymentStep==="verifying",t.paymentStep==="success")),d(5),p("ngClass",Dt(12,Ji,t.paymentStep==="success",t.paymentStep==="success"))}}function al(o,e){o&1&&(r(0,"div",38),l(1," Please enter a valid 16-digit card number "),a())}function sl(o,e){if(o&1&&(r(0,"div",38),l(1),a()),o&2){let t=b();d(),M(" ",t.expiryDateError," ")}}function ll(o,e){o&1&&(r(0,"div",38),l(1," Please enter a valid expiry date "),a())}function cl(o,e){o&1&&(r(0,"div",38),l(1," Please enter a valid 3-digit CVV "),a())}function dl(o,e){o&1&&(dn(0),l(1," Free Trial (30 days) "),mn())}function ml(o,e){if(o&1&&(dn(0),l(1," Premium Subscription "),r(2,"div",39),l(3),a(),mn()),o&2){let t=b();d(3),M("",t.userCount," users")}}function pl(o,e){if(o&1&&(r(0,"span"),l(1),a()),o&2){let t=b();d(),M("Pay R",t.subscriptionPrice,"")}}function gl(o,e){o&1&&(r(0,"span"),l(1,"Processing..."),a())}var Yn=class o{constructor(e,t){this.fb=e;this.router=t;this.paymentForm=this.fb.group({cardNumber:["",[S.required,S.pattern(/^[\d\s]{19}$/)]],expiryDate:["",[S.required,S.pattern(/^(0[1-9]|1[0-2])\/([0-9]{2})$/),this.expiryDateValidator]],cvc:["",[S.required,S.pattern(/^[0-9]{3}$/)]]})}paymentForm;subscriptionType="trial";subscriptionPrice=0;userCount=5;expiryDateError="";isProcessing=!1;paymentStep="idle";processingMessage="";processingMessages=["Connecting to payment gateway...","Processing your payment...","Securing transaction..."];verifyingMessages=["Verifying card details...","Confirming payment...","Finalizing transaction..."];successMessages=["Payment successful!","Transaction complete!","Payment confirmed!"];ngOnInit(){if(!localStorage.getItem("practiceData")){this.router.navigate(["/payment/practice-info"]);return}this.subscriptionType=localStorage.getItem("subscriptionType")||"trial",this.subscriptionType==="premium"&&(this.subscriptionPrice=Number(localStorage.getItem("subscriptionPrice"))||0,this.userCount=Number(localStorage.getItem("userCount"))||5)}expiryDateValidator(e){let t=e.value;if(!t)return null;if(!/^(0[1-9]|1[0-2])\/([0-9]{2})$/.test(t))return{invalidFormat:!0};let[n,i]=t.split("/"),s=2e3+parseInt(i,10),c=new Date(s,parseInt(n,10)-1,1),m=new Date,u=new Date(s,parseInt(n,10),0).getDate();return c.setDate(u),c.setHours(23,59,59,999),c<m?{expired:!0}:null}formatCardNumber(e){let t=e.target,n=t.value.replace(/\D/g,"");n.length>16&&(n=n.substr(0,16));let i=n.match(/.{1,4}/g)||[];t.value=i.join(" "),this.paymentForm.get("cardNumber")?.setValue(t.value,{emitEvent:!1})}formatExpiryDate(e){let t=e.target,n=t.value.replace(/\D/g,"");n.length>=2&&(n=n.substr(0,2)+"/"+n.substr(2,2)),t.value=n,this.paymentForm.get("expiryDate")?.setValue(t.value,{emitEvent:!1});let i=this.paymentForm.get("expiryDate");i?.errors?.expired?this.expiryDateError="Card has expired. Please use a valid expiry date.":i?.errors?.invalidFormat?this.expiryDateError="Invalid format. Use MM/YY format.":this.expiryDateError=""}getRandomMessage(e){let t=Math.floor(Math.random()*e.length);return e[t]}getRandomTime(e,t){return Math.floor(Math.random()*(t*10-e*10+1)+e*10)*100}updateMessage(e){this.processingMessage="",setTimeout(()=>{this.processingMessage=e},150)}simulatePaymentProcess(){this.isProcessing=!0,this.paymentStep="processing",this.updateMessage(this.getRandomMessage(this.processingMessages));let e=this.getRandomTime(1,1.5),t=this.getRandomTime(1,1.5);setTimeout(()=>{this.updateMessage(this.getRandomMessage(this.processingMessages)),setTimeout(()=>{this.paymentStep="verifying",this.updateMessage(this.getRandomMessage(this.verifyingMessages));let n=this.getRandomTime(.8,1.2),i=this.getRandomTime(.7,1.3);setTimeout(()=>{this.updateMessage(this.getRandomMessage(this.verifyingMessages)),setTimeout(()=>{this.paymentStep="success",this.updateMessage(this.getRandomMessage(this.successMessages));let s=this.getRandomTime(2,3);setTimeout(()=>{this.savePaymentData(),this.router.navigate(["/dashboard"])},s)},i)},n)},t)},e)}savePaymentData(){let e=JSON.parse(localStorage.getItem("practiceData")||"{}"),t=Ce(B({},e),{payment:this.paymentForm.value,subscription:{type:this.subscriptionType,price:this.subscriptionPrice,userCount:this.userCount}});console.log("Payment processed successfully:",t),localStorage.setItem("subscriptionData",JSON.stringify({type:this.subscriptionType,userCount:this.userCount}));let n=localStorage.getItem("userEmail");n&&localStorage.setItem("userEmail",n)}onSubmit(){this.paymentForm.valid&&!this.isProcessing?this.simulatePaymentProcess():this.isProcessing||(Object.keys(this.paymentForm.controls).forEach(t=>{this.paymentForm.get(t)?.markAsTouched()}),this.paymentForm.get("expiryDate")?.errors?.expired&&(this.expiryDateError="Card has expired. Please use a valid expiry date."))}static \u0275fac=function(t){return new(t||o)(P(Ye),P(j))};static \u0275cmp=E({type:o,selectors:[["app-payment-info"]],decls:46,vars:19,consts:[[1,"payment-container"],[1,"payment-card",3,"ngClass"],["class","payment-overlay",4,"ngIf"],[1,"header"],[1,"progress-dots"],[1,"dot"],[1,"dot","active"],[1,"payment-form",3,"ngSubmit","formGroup"],[1,"form-group","full-width"],["for","cardNumber"],[1,"input-with-icon"],[1,"card-icon"],["type","text","id","cardNumber","formControlName","cardNumber","placeholder","1234 5678 9012 3456",3,"input"],["class","error-message",4,"ngIf"],[1,"form-row"],[1,"form-group"],["for","expiryDate"],["type","text","id","expiryDate","formControlName","expiryDate","placeholder","MM/YY",3,"input"],["for","cvc"],["type","text","id","cvc","formControlName","cvc","placeholder","123"],[1,"summary-box"],[1,"summary-row"],[1,"description"],[4,"ngIf"],[1,"amount"],[1,"total-row"],[1,"total-label"],[1,"total-amount"],["type","submit",1,"submit-button",3,"disabled"],[1,"payment-overlay"],[1,"payment-processing"],[1,"loader",3,"ngClass"],[1,"processing-message"],[1,"processing-steps"],[1,"step",3,"ngClass"],[1,"step-indicator"],[1,"step-label"],[1,"step-connector"],[1,"error-message"],[1,"user-details"]],template:function(t,n){if(t&1&&(r(0,"div",0)(1,"div",1),_(2,rl,21,15,"div",2),r(3,"div",3)(4,"h2"),l(5,"Complete Your Payment"),a(),r(6,"div",4),g(7,"div",5)(8,"div",6),a()(),r(9,"form",7),h("ngSubmit",function(){return n.onSubmit()}),r(10,"div",8)(11,"label",9),l(12,"Card Number"),a(),r(13,"div",10),g(14,"i",11),r(15,"input",12),h("input",function(s){return n.formatCardNumber(s)}),a()(),_(16,al,2,0,"div",13),a(),r(17,"div",14)(18,"div",15)(19,"label",16),l(20,"Expiry Date"),a(),r(21,"div",10)(22,"input",17),h("input",function(s){return n.formatExpiryDate(s)}),a()(),_(23,sl,2,1,"div",13)(24,ll,2,0,"div",13),a(),r(25,"div",15)(26,"label",18),l(27,"CVV"),a(),r(28,"div",10),g(29,"input",19),a(),_(30,cl,2,0,"div",13),a()(),r(31,"div",20)(32,"div",21)(33,"span",22),_(34,dl,2,0,"ng-container",23)(35,ml,4,1,"ng-container",23),a(),r(36,"span",24),l(37),a()(),r(38,"div",25)(39,"span",26),l(40,"Total"),a(),r(41,"span",27),l(42),a()()(),r(43,"button",28),_(44,pl,2,1,"span",23)(45,gl,2,0,"span",23),a()()()()),t&2){let i,s,c;d(),p("ngClass",mt(17,il,n.isProcessing)),d(),p("ngIf",n.isProcessing),d(7),p("formGroup",n.paymentForm),d(6),It("maxlength",19),d(),p("ngIf",((i=n.paymentForm.get("cardNumber"))==null?null:i.invalid)&&((i=n.paymentForm.get("cardNumber"))==null?null:i.touched)),d(6),It("maxlength",5),d(),p("ngIf",n.expiryDateError),d(),p("ngIf",((s=n.paymentForm.get("expiryDate"))==null?null:s.invalid)&&((s=n.paymentForm.get("expiryDate"))==null?null:s.touched)&&!n.expiryDateError),d(5),It("maxlength",3),d(),p("ngIf",((c=n.paymentForm.get("cvc"))==null?null:c.invalid)&&((c=n.paymentForm.get("cvc"))==null?null:c.touched)),d(4),p("ngIf",n.subscriptionType==="trial"),d(),p("ngIf",n.subscriptionType==="premium"),d(2),M("R",n.subscriptionPrice,""),d(5),M("R",n.subscriptionPrice,""),d(),p("disabled",!n.paymentForm.valid||n.isProcessing),d(),p("ngIf",!n.isProcessing),d(),p("ngIf",n.isProcessing)}},dependencies:[A,gt,U,Fe,Ae,H,Z,De,We,$e],styles:['.payment-container[_ngcontent-%COMP%]{width:100%;min-height:832px;background:#f3f3f3;display:flex;justify-content:center;align-items:center;padding:20px}.payment-card[_ngcontent-%COMP%]{width:736px;background:#fff;box-shadow:0 8px 20px #0000000f;border-radius:12px;padding:32px;position:relative;overflow:hidden;transition:all .3s ease}.payment-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:#fffffffa;display:flex;justify-content:center;align-items:center;z-index:100;-webkit-backdrop-filter:blur(3px);backdrop-filter:blur(3px);transition:opacity .3s ease;animation:_ngcontent-%COMP%_fadeIn .3s ease}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}.payment-processing[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;width:80%;max-width:400px;padding:20px}.loader[_ngcontent-%COMP%]{position:relative;width:70px;height:70px;margin-bottom:32px;border-radius:50%;background:#199a8e0d;display:flex;justify-content:center;align-items:center;box-shadow:0 0 0 8px #199a8e08}.loader[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:5px;border-radius:50%;border:3px solid transparent;border-top-color:#199a8e;animation:_ngcontent-%COMP%_spin 1.2s cubic-bezier(.68,-.55,.27,1.55) infinite}.loader[_ngcontent-%COMP%]:after{content:"";position:absolute;inset:15px;border-radius:50%;border:3px solid transparent;border-top-color:#199a8e99;animation:_ngcontent-%COMP%_spin 1.8s cubic-bezier(.68,-.55,.27,1.55) infinite reverse}.loader.verifying[_ngcontent-%COMP%]:before{border-top-color:#3b82f6}.loader.verifying[_ngcontent-%COMP%]:after{border-top-color:#3b82f699;animation-duration:1.5s}.loader.success[_ngcontent-%COMP%]{background:#10b98114;box-shadow:0 0 0 8px #10b9810d}.loader.success[_ngcontent-%COMP%]:before, .loader.success[_ngcontent-%COMP%]:after{display:none}.loader.success[_ngcontent-%COMP%]:before{content:"\\2713";display:flex;align-items:center;justify-content:center;color:#fff;font-size:0;animation:_ngcontent-%COMP%_checkmark .8s cubic-bezier(.19,1,.22,1) forwards;border:none}@keyframes _ngcontent-%COMP%_checkmark{0%{font-size:0;background:#10b98100}50%{font-size:40px;background:#10b981;width:70px;height:70px;border-radius:50%}to{font-size:36px;background:#10b981;width:70px;height:70px;border-radius:50%}}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.processing-message[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#111827;margin-bottom:40px;text-align:center;min-height:27px;animation:_ngcontent-%COMP%_fadeInUp .5s ease}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.processing-steps[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%;margin-top:16px;position:relative}.step[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;position:relative;flex:1;z-index:2}.step-indicator[_ngcontent-%COMP%]{width:28px;height:28px;border-radius:50%;background:#f3f4f6;margin-bottom:10px;display:flex;justify-content:center;align-items:center;transition:all .5s ease;position:relative;box-shadow:0 0 0 4px #f3f4f680}.step.active[_ngcontent-%COMP%]   .step-indicator[_ngcontent-%COMP%]{background:#199a8e;box-shadow:0 0 0 4px #199a8e26;transform:scale(1.1)}.step.completed[_ngcontent-%COMP%]   .step-indicator[_ngcontent-%COMP%]{background:#10b981;box-shadow:0 0 0 4px #10b98126}.step.completed[_ngcontent-%COMP%]   .step-indicator[_ngcontent-%COMP%]:after{content:"\\2713";color:#fff;font-size:14px;font-weight:700;opacity:0;animation:_ngcontent-%COMP%_fadeIn .3s ease .2s forwards}.step-connector[_ngcontent-%COMP%]{height:3px;background:#e5e7eb;flex:1;margin:0 4px 38px;position:relative;overflow:hidden;z-index:1}.step-connector[_ngcontent-%COMP%]:after{content:"";position:absolute;top:0;left:0;height:100%;width:0;background:#10b981;transition:width .5s ease}.step[_ngcontent-%COMP%]:nth-child(3) ~ .step-connector[_ngcontent-%COMP%]:after{width:100%}.step[_ngcontent-%COMP%]:nth-child(5) ~ .step-connector[_ngcontent-%COMP%]:after{width:100%}.step-label[_ngcontent-%COMP%]{font-size:14px;color:#6b7280;transition:all .3s ease;font-weight:500;position:relative}.step.active[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%]{color:#111827;font-weight:600;transform:translateY(-2px)}.step.completed[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%]{color:#10b981;font-weight:600}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:32px}.header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#111827;font-size:22.69px;font-weight:700;line-height:32px;margin:0}.progress-dots[_ngcontent-%COMP%]{display:flex;gap:8px}.dot[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:50%;background:#d1d5db;transition:background-color .3s ease}.dot.active[_ngcontent-%COMP%]{background:#199a8e}.payment-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.form-row[_ngcontent-%COMP%]{display:flex;gap:24px}.form-group[_ngcontent-%COMP%]{flex:1}.form-group.full-width[_ngcontent-%COMP%]{width:100%}label[_ngcontent-%COMP%]{display:block;color:#374151;font-size:13.78px;font-weight:500;line-height:20px;margin-bottom:6px}.input-with-icon[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center}input[_ngcontent-%COMP%]{width:100%;padding:12px 14px 12px 42px;background:#fff;border:1px solid #E5E7EB;border-radius:8px;color:#111827;font-size:15.25px;line-height:19px;transition:all .2s ease}input[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 2px #199a8e1a}input.ng-invalid.ng-touched[_ngcontent-%COMP%]{border-color:#ef4444}input[_ngcontent-%COMP%]::placeholder{color:#9ca3af}.card-icon[_ngcontent-%COMP%]{position:absolute;left:12px;width:20px;height:20px;opacity:.6}.error-message[_ngcontent-%COMP%]{color:#ef4444;font-size:12px;margin-top:6px;min-height:16px;transition:all .2s ease}.summary-box[_ngcontent-%COMP%]{background:#f9fafb;border-radius:10px;padding:28px 20px;margin-top:8px;box-shadow:0 1px 2px #0000000d}.summary-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:28px}.description[_ngcontent-%COMP%]{color:#4b5563;font-size:15.38px;line-height:24px}.user-details[_ngcontent-%COMP%]{font-size:14px;color:#6b7280;margin-top:4px}.amount[_ngcontent-%COMP%]{color:#111827;font-size:16px;font-weight:500;line-height:24px}.total-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding-top:16px;border-top:1px solid #E5E7EB}.total-label[_ngcontent-%COMP%]{color:#111827;font-size:16px;font-weight:600;line-height:24px}.total-amount[_ngcontent-%COMP%]{color:#111827;font-size:18px;font-weight:700;line-height:24px}.submit-button[_ngcontent-%COMP%]{width:100%;height:44px;background:#199a8e;color:#fff;border:none;border-radius:8px;font-size:16px;font-weight:700;line-height:24px;cursor:pointer;transition:all .2s ease;position:relative;overflow:hidden;margin-top:8px;display:flex;justify-content:center;align-items:center}.submit-button[_ngcontent-%COMP%]:hover{background:#158276;transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.submit-button[_ngcontent-%COMP%]:active{transform:translateY(0);box-shadow:none}.submit-button[_ngcontent-%COMP%]:disabled{background:#a7a7a7;cursor:not-allowed;transform:none;box-shadow:none}@media (max-width: 768px){.payment-card[_ngcontent-%COMP%]{width:100%;max-width:736px;padding:24px}.form-row[_ngcontent-%COMP%]{flex-direction:column;gap:16px}.processing-steps[_ngcontent-%COMP%]{flex-direction:column;gap:20px;margin-top:0}.step[_ngcontent-%COMP%]{flex-direction:row;justify-content:flex-start;align-items:center;width:100%;gap:16px}.step-indicator[_ngcontent-%COMP%]{margin-bottom:0}.step-label[_ngcontent-%COMP%]{text-align:left}.step-connector[_ngcontent-%COMP%]{width:3px;height:20px;margin:0 0 0 12px}}']})};var ul=["userInput"];function fl(o,e){if(o&1&&(r(0,"span",31),l(1),a()),o&2){let t=b();d(),T(t.userCount)}}function hl(o,e){if(o&1){let t=V();r(0,"input",32,0),L("ngModelChange",function(i){v(t);let s=b();return R(s.tempUserCount,i)||(s.tempUserCount=i),x(i)}),h("blur",function(){v(t);let i=b();return x(i.onUserCountBlur())})("keyup",function(i){v(t);let s=b();return x(s.onUserCountKeyup(i))}),a()}if(o&2){let t=b();uo("min",t.MIN_USERS),z("ngModel",t.tempUserCount),It("placeholder",t.userCount)}}function bl(o,e){if(o&1){let t=V();r(0,"div",33),h("click",function(){v(t);let i=b();return x(i.hideBenefits())}),r(1,"div",34),h("click",function(i){return v(t),x(i.stopPropagation())}),r(2,"div",35)(3,"h3"),l(4,"Key Benefits"),a(),r(5,"button",36),h("click",function(){v(t);let i=b();return x(i.hideBenefits())}),g(6,"i",37),a()(),r(7,"div",38)(8,"div",39),g(9,"i",40),r(10,"div",41)(11,"h4"),l(12,"Digital Transformation"),a(),r(13,"p"),l(14,"Reduce paperwork by 50% and enhance patient care through efficient digital file management."),a()()(),r(15,"div",39),g(16,"i",42),r(17,"div",41)(18,"h4"),l(19,"Secure Management"),a(),r(20,"p"),l(21,"Save 10+ hours weekly with secure digital file handling, prioritizing both efficiency and data protection."),a()()(),r(22,"div",39),g(23,"i",43),r(24,"div",41)(25,"h4"),l(26,"Smart Scheduling"),a(),r(27,"p"),l(28,"Optimize appointments with our intelligent booking system while maintaining full control over your calendar."),a()()(),r(29,"div",39),g(30,"i",44),r(31,"div",41)(32,"h4"),l(33,"Flexible Licensing"),a(),r(34,"p"),l(35,"Start with our base subscription that includes a license key for up to 5 doctors (R500/month). Perfect for growing practices - add more users as your practice expands."),a()()()()()()}}var Hn=class o{constructor(e){this.router=e}userInput;basePrice=500;totalPrice=this.basePrice;userCount=5;isEditing=!1;tempUserCount="";showingBenefits=!1;PRICE_INCREMENT=100;USER_INCREMENT=1;MIN_USERS=5;showBenefits(){this.showingBenefits=!0,document.body.style.overflow="hidden"}hideBenefits(){this.showingBenefits=!1,document.body.style.overflow="auto"}incrementPrice(){this.totalPrice+=this.PRICE_INCREMENT,this.userCount+=this.USER_INCREMENT}decrementPrice(){this.totalPrice>this.basePrice&&(this.totalPrice-=this.PRICE_INCREMENT,this.userCount-=this.USER_INCREMENT)}startEditing(){this.tempUserCount=this.userCount.toString(),this.isEditing=!0,setTimeout(()=>{if(this.userInput){let e=this.userInput.nativeElement;e.focus(),e.style.width=this.getInputWidth(this.tempUserCount)}})}onUserCountBlur(){this.updateUserCount()}onUserCountKeyup(e){e.key==="Enter"?this.onUserCountBlur():e.key==="Escape"&&(this.isEditing=!1);let t=this.userInput.nativeElement;t.style.width=this.getInputWidth(t.value)}getInputWidth(e){return`${Math.max(30,e.length*14+16)}px`}updateUserCount(){let e=parseInt(this.tempUserCount);!isNaN(e)&&e>=this.MIN_USERS?(this.userCount=e,this.totalPrice=this.basePrice+(this.userCount-this.MIN_USERS)*this.PRICE_INCREMENT):this.tempUserCount=this.userCount.toString(),this.isEditing=!1}startFreeTrial(){localStorage.setItem("subscriptionType","trial"),this.router.navigate(["/payment/practice-info"])}proceedToCheckout(){localStorage.setItem("subscriptionType","premium"),localStorage.setItem("userCount",this.userCount.toString()),localStorage.setItem("subscriptionPrice",this.totalPrice.toString()),this.router.navigate(["/payment/practice-info"])}static \u0275fac=function(t){return new(t||o)(P(j))};static \u0275cmp=E({type:o,selectors:[["app-subscription"]],viewQuery:function(t,n){if(t&1&&fo(ul,5),t&2){let i;ho(i=bo())&&(n.userInput=i.first)}},decls:83,vars:7,consts:[["userInput",""],[1,"subscription-container"],[1,"hero-section"],[1,"hero-content"],[1,"benefits-button",3,"click"],[1,"plans-section"],[1,"plans-container"],[1,"plan-card"],[1,"plan-description"],[1,"price-container"],[1,"price"],[1,"price-period"],[1,"features-list"],[1,"feature"],[1,"bi","bi-star-fill","feature-icon"],[1,"bi","bi-shield-lock-fill","feature-icon"],[1,"bi","bi-cloud-arrow-up-fill","feature-icon"],[1,"bi","bi-people-fill","feature-icon"],[1,"trial-button",3,"click"],[1,"plan-card","premium"],[1,"license-info"],[1,"user-count-controls"],[1,"counter-container"],[1,"counter-button",3,"click","disabled"],[1,"user-count-wrapper",3,"click"],["class","user-count",4,"ngIf"],["type","number","class","user-count-input",3,"ngModel","min","ngModelChange","blur","keyup",4,"ngIf"],[1,"counter-button",3,"click"],[1,"pricing-info"],[1,"subscribe-button",3,"click"],["class","benefits-overlay",3,"click",4,"ngIf"],[1,"user-count"],["type","number",1,"user-count-input",3,"ngModelChange","blur","keyup","ngModel","min"],[1,"benefits-overlay",3,"click"],[1,"benefits-modal",3,"click"],[1,"benefits-header"],[1,"close-modal",3,"click"],[1,"bi","bi-x"],[1,"benefits-content"],[1,"benefit-card"],[1,"bi","bi-file-earmark-medical-fill"],[1,"benefit-text"],[1,"bi","bi-shield-lock-fill"],[1,"bi","bi-calendar-check-fill"],[1,"bi","bi-key-fill"]],template:function(t,n){t&1&&(r(0,"div",1)(1,"div",2)(2,"div",3)(3,"h1"),l(4,"Transform Your Practice with Our Healthcare Platform"),a(),r(5,"p"),l(6,"Join thousands of healthcare professionals who trust our platform"),a(),r(7,"button",4),h("click",function(){return n.showBenefits()}),l(8,"View Benefits"),a()()(),r(9,"div",5)(10,"h2"),l(11,"Choose Your Plan"),a(),r(12,"div",6)(13,"div",7)(14,"h3"),l(15,"Free Trial"),a(),r(16,"p",8),l(17,"Experience our platform risk-free"),a(),r(18,"div",9)(19,"span",10),l(20,"R0"),a(),r(21,"span",11),l(22,"/month"),a()(),r(23,"div",12)(24,"div",13),g(25,"i",14),r(26,"span"),l(27,"Try all premium features for 30 days"),a()(),r(28,"div",13),g(29,"i",15),r(30,"span"),l(31,"Advanced security"),a()(),r(32,"div",13),g(33,"i",16),r(34,"span"),l(35,"Cloud storage"),a()(),r(36,"div",13),g(37,"i",17),r(38,"span"),l(39,"Scalable user licenses"),a()()(),r(40,"button",18),h("click",function(){return n.startFreeTrial()}),l(41,"Start Free Trial"),a()(),r(42,"div",19)(43,"h3"),l(44,"Premium"),a(),r(45,"p",8),l(46,"Full features for your practice"),a(),r(47,"div",9)(48,"span",10),l(49),a(),r(50,"span",11),l(51,"/month"),a(),r(52,"div",20),l(53," Base subscription includes license for up to 5 doctors (R500/month) "),a(),r(54,"div",21)(55,"label"),l(56,"Number of Users"),a(),r(57,"div",22)(58,"button",23),h("click",function(){return n.decrementPrice()}),l(59," - "),a(),r(60,"div",24),h("click",function(){return n.startEditing()}),_(61,fl,2,1,"span",25)(62,hl,2,3,"input",26),a(),r(63,"button",27),h("click",function(){return n.incrementPrice()}),l(64," + "),a()(),r(65,"p",28),l(66,"R100 per additional user/month"),a()()(),r(67,"div",12)(68,"div",13),g(69,"i",14),r(70,"span"),l(71,"Premium features"),a()(),r(72,"div",13),g(73,"i",15),r(74,"span"),l(75,"Advanced security"),a()(),r(76,"div",13),g(77,"i",16),r(78,"span"),l(79,"Cloud storage"),a()()(),r(80,"button",29),h("click",function(){return n.proceedToCheckout()}),l(81,"Subscribe Now"),a()()()()(),_(82,bl,36,0,"div",30)),t&2&&(d(49),M("R",n.totalPrice,""),d(9),ae("hidden",n.totalPrice===n.basePrice),p("disabled",n.totalPrice===n.basePrice),d(3),p("ngIf",!n.isEditing),d(),p("ngIf",n.isEditing),d(20),p("ngIf",n.showingBenefits))},dependencies:[A,U,ee,H,Qo,Z,Go,pe],styles:['@import"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css";.subscription-container[_ngcontent-%COMP%]{width:100%;background:#f9fafb;min-height:100vh}.hero-section[_ngcontent-%COMP%]{width:100%;height:340px;background:#199a8e;position:relative}.hero-content[_ngcontent-%COMP%]{width:100%;max-width:1180px;padding:80px 20px;margin:0 auto;text-align:center;color:#fff}.hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2.8rem;font-weight:700;line-height:1.2;margin-bottom:1rem}.hero-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;margin-bottom:2rem}.benefits-button[_ngcontent-%COMP%]{padding:14px 24px;background:#fff;color:#199a8e;border:none;border-radius:8px;font-weight:700;font-size:1.25rem;cursor:pointer;box-shadow:0 4px 4px #00000040;transition:all .2s ease-in-out}.benefits-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 8px #0003}.plans-section[_ngcontent-%COMP%]{padding:64px 20px;max-width:1280px;margin:0 auto}.plans-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{text-align:center;font-size:1.8rem;font-weight:700;margin-bottom:3rem}.plans-container[_ngcontent-%COMP%]{display:flex;gap:32px;justify-content:center;flex-wrap:wrap}.plan-card[_ngcontent-%COMP%]{width:100%;max-width:380px;padding:24px;background:#fff;border-radius:8px;box-shadow:0 4px 6px -4px #0000001a;transition:transform .2s ease-in-out;display:flex;flex-direction:column;min-height:450px}.plan-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px)}.plan-card.premium[_ngcontent-%COMP%]{border:2px solid #199A8E}.plan-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:700;margin-bottom:1rem}.plan-description[_ngcontent-%COMP%]{color:#4b5563;margin-bottom:1.5rem}.price-container[_ngcontent-%COMP%]{margin-bottom:2rem;padding-bottom:1rem;border-bottom:1px solid #E5E7EB}.price[_ngcontent-%COMP%]{font-size:2rem;font-weight:700}.price-period[_ngcontent-%COMP%]{color:#4b5563;margin-left:.5rem}.features-list[_ngcontent-%COMP%]{margin:1.5rem 0 2rem}.feature[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:1rem}.feature-icon[_ngcontent-%COMP%]{color:#199a8e;font-size:1.25rem;margin-right:.75rem;width:20px;height:20px;display:flex;align-items:center;justify-content:center}.user-count-controls[_ngcontent-%COMP%]{margin-top:1.5rem}.user-count-wrapper[_ngcontent-%COMP%]{display:inline-flex;min-width:30px;text-align:center;cursor:pointer}.user-count[_ngcontent-%COMP%]{font-weight:700;font-size:1.25rem;color:#000}.user-count-input[_ngcontent-%COMP%]{min-width:30px;width:auto;padding:4px 8px;border:1px solid #199A8E;border-radius:4px;font-size:1.25rem;font-weight:700;color:#199a8e;text-align:center}.user-count-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#158276;box-shadow:0 0 0 2px #199a8e1a}.counter-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:1.5rem;margin:.5rem 0}.counter-button[_ngcontent-%COMP%]{width:24px;height:24px;border:1px solid #199A8E;border-radius:2px;background:#fff;color:#199a8e;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .2s ease}.counter-button[_ngcontent-%COMP%]:hover:not(:disabled){background:#199a8e1a}.counter-button[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.counter-button.hidden[_ngcontent-%COMP%]{visibility:hidden}.pricing-info[_ngcontent-%COMP%], .license-info[_ngcontent-%COMP%]{color:#4b5563;font-size:.75rem;text-align:center;margin-top:.5rem}.license-info[_ngcontent-%COMP%]{font-size:.85rem;color:#199a8e;background:#199a8e1a;padding:8px 12px;border-radius:4px;margin:1rem 0}.trial-button[_ngcontent-%COMP%], .subscribe-button[_ngcontent-%COMP%]{width:100%;padding:12px;border-radius:8px;font-weight:700;font-size:1.25rem;cursor:pointer;transition:all .2s ease;margin-top:auto}.trial-button[_ngcontent-%COMP%]{background:#fff;color:#199a8e;border:1px solid #199A8E}.trial-button[_ngcontent-%COMP%]:hover{background:#199a8e1a;transform:translateY(-2px)}.subscribe-button[_ngcontent-%COMP%]{background:#199a8e;color:#fff;border:none;box-shadow:0 4px 4px #00000040}.subscribe-button[_ngcontent-%COMP%]:hover{background:#158276;transform:translateY(-2px);box-shadow:0 6px 8px #0003}.benefits-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#00000080;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);display:flex;align-items:center;justify-content:center;z-index:1000}.benefits-modal[_ngcontent-%COMP%]{background:#fff;border-radius:12px;width:90%;max-width:600px;box-shadow:0 20px 25px -5px #0000001a,0 10px 10px -5px #0000000a;overflow:hidden}.benefits-header[_ngcontent-%COMP%]{background:#199a8e;padding:1.5rem;display:flex;justify-content:space-between;align-items:center}.benefits-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#fff;margin:0;font-size:1.5rem;font-weight:600}.close-modal[_ngcontent-%COMP%]{background:none;border:none;color:#fff;font-size:1.5rem;cursor:pointer;padding:.5rem;display:flex;align-items:center;justify-content:center;transition:all .2s ease}.close-modal[_ngcontent-%COMP%]:hover{transform:rotate(90deg)}.benefits-content[_ngcontent-%COMP%]{padding:2rem}.benefit-card[_ngcontent-%COMP%]{display:flex;align-items:flex-start;padding:1.5rem;background:#f9fafb;border:1px solid #E5E7EB;border-radius:8px;margin-bottom:1rem;transition:transform .2s ease}.benefit-card[_ngcontent-%COMP%]:last-child{margin-bottom:0}.benefit-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);border-color:#199a8e;box-shadow:0 4px 6px -1px #0000001a}.benefit-card[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.75rem;color:#199a8e;margin-right:1.25rem}.benefit-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#111827;font-size:1.1rem;font-weight:600;margin:0 0 .5rem}.benefit-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#4b5563;font-size:.95rem;line-height:1.5;margin:0}@media (max-width: 768px){.hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.hero-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem}.plans-container[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.plan-card[_ngcontent-%COMP%]{max-width:100%}.benefits-button[_ngcontent-%COMP%]{font-size:1.1rem;padding:12px 20px}}@media (max-width: 480px){.hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.8rem}.plans-section[_ngcontent-%COMP%]{padding:40px 16px}.counter-container[_ngcontent-%COMP%]{gap:1rem}.user-count-input[_ngcontent-%COMP%]{font-size:1.1rem}.benefits-modal[_ngcontent-%COMP%]{width:95%;margin:1rem}.benefits-content[_ngcontent-%COMP%]{padding:1.5rem}.benefit-card[_ngcontent-%COMP%]{padding:1.25rem}.benefit-card[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;margin-right:1rem}.benefit-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1rem}.benefit-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem}}']})};var Wt=class o{static \u0275fac=function(t){return new(t||o)};static \u0275cmp=E({type:o,selectors:[["app-benefits"]],decls:32,vars:0,consts:[["id","bnft",1,"benefits-container"],[1,"benefits-title"],[1,"highlight"],[1,"cards-container"],[1,"card_1"],[1,"card-header"],[1,"bi","bi-clock"],[1,"card-content"],[1,"card-title"],[1,"card-description"],[1,"card_2"],[1,"bi","bi-shield-lock"],[1,"card_3"],[1,"bi","bi-calendar-check"],[1,"footer-text"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"h2",1),l(2,"Why Healthcare Providers Choose "),r(3,"span",2),l(4,"MedSecura"),a()(),r(5,"div",3)(6,"div",4)(7,"div",5),g(8,"i",6),a(),r(9,"div",7)(10,"h3",8),l(11,"From Physical to Digital"),a(),r(12,"p",9),l(13," Digital file management has halved the hours doctors once spent on paperwork, freeing up more time for patient care! "),a()()(),r(14,"div",10)(15,"div",5),g(16,"i",11),a(),r(17,"div",7)(18,"h3",8),l(19,"Secure File Management"),a(),r(20,"p",9),l(21," Digital file management has halved the 10+ hours doctors once spent on paperwork, freeing up more time for patient care! "),a()()(),r(22,"div",12)(23,"div",5),g(24,"i",13),a(),r(25,"div",7)(26,"h3",8),l(27,"Appointment Booking"),a(),r(28,"p",9),l(29," Streamline your practice with our intelligent booking system. Patients can easily schedule, reschedule, or cancel appointments while you maintain full control over your availability. "),a()()()(),r(30,"p",14),l(31," Join thousands of healthcare providers who trust MedSecura to revolutionize their practice management and enhance patient care. "),a()())},styles:['[_nghost-%COMP%]{display:block;width:100%;min-height:100vh;overflow-x:hidden;box-sizing:border-box}.benefits-section[_ngcontent-%COMP%]{width:100%;max-width:100%;padding:0;box-sizing:border-box;overflow-x:hidden;min-height:100vh}.benefits-container[_ngcontent-%COMP%]{width:100%;max-width:1400px;margin:0 auto;box-sizing:border-box;text-align:center;padding:6rem 5%;min-height:100vh;display:flex;flex-direction:column;justify-content:center}.benefits-title[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin-bottom:4rem;color:#2b2b2b;text-align:center}.benefits-title[_ngcontent-%COMP%]   .highlight[_ngcontent-%COMP%]{color:#199a8e;position:relative}.benefits-title[_ngcontent-%COMP%]   .highlight[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-4px;left:0;width:100%;height:2px;background-color:#199a8e;transform:scaleX(0);transition:transform .3s ease}.benefits-title[_ngcontent-%COMP%]:hover   .highlight[_ngcontent-%COMP%]:after{transform:scaleX(1)}.cards-container[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:3rem;max-width:1400px;margin:0 auto;padding:0 1rem;flex-grow:1;align-items:center}.card_1[_ngcontent-%COMP%], .card_2[_ngcontent-%COMP%], .card_3[_ngcontent-%COMP%]{background:#ffffffb3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:16px;padding:2.5rem;box-shadow:0 4px 6px #0000000d;transition:all .3s ease;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;height:100%;border:1px solid rgba(25,154,142,.1);gap:1.5rem}.card_1[_ngcontent-%COMP%]:hover, .card_2[_ngcontent-%COMP%]:hover, .card_3[_ngcontent-%COMP%]:hover{transform:translateY(-10px);box-shadow:0 8px 15px #0000001a;border:1px solid rgba(25,154,142,.2);background:#fffc}.card-header[_ngcontent-%COMP%]{margin:0;padding:1rem;font-size:3rem;color:#199a8e;transition:transform .3s ease;background:#199a8e1a;border-radius:50%;width:80px;height:80px;display:flex;align-items:center;justify-content:center}.card_1[_ngcontent-%COMP%]:hover   .card-header[_ngcontent-%COMP%], .card_2[_ngcontent-%COMP%]:hover   .card-header[_ngcontent-%COMP%], .card_3[_ngcontent-%COMP%]:hover   .card-header[_ngcontent-%COMP%]{transform:scale(1.1);background:#199a8e26}.card-content[_ngcontent-%COMP%]{text-align:center;flex-grow:1;display:flex;flex-direction:column;justify-content:flex-start;gap:1rem;padding:0 .5rem}.card-title[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:600;color:#2b2b2b;margin:0;line-height:1.3}.card-description[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.6;color:#4a4a4a;margin:0;flex-grow:1;display:flex;align-items:center;justify-content:center}.footer-text[_ngcontent-%COMP%]{margin-top:5rem;font-size:1.1rem;color:#666;max-width:800px;margin-left:auto;margin-right:auto;line-height:1.6}.bi-clock[_ngcontent-%COMP%], .bi-calendar-check[_ngcontent-%COMP%], .bi-shield-lock[_ngcontent-%COMP%]{font-size:2rem;color:#199a8e}@media (max-width: 1200px){.cards-container[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);gap:2.5rem;padding:0 2rem}.benefits-container[_ngcontent-%COMP%]{padding:5rem 4%}.benefits-title[_ngcontent-%COMP%]{font-size:2.2rem;margin-bottom:3.5rem}.card-header[_ngcontent-%COMP%]{font-size:2.75rem;width:75px;height:75px}.card-title[_ngcontent-%COMP%]{font-size:1.6rem}.card-description[_ngcontent-%COMP%]{font-size:1.05rem}}@media (max-width: 768px){.cards-container[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:2rem;padding:0 1.5rem}.benefits-title[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:3rem;padding:0 1rem}.benefits-container[_ngcontent-%COMP%]{padding:4rem 5%}.card_1[_ngcontent-%COMP%], .card_2[_ngcontent-%COMP%], .card_3[_ngcontent-%COMP%]{padding:2rem;max-width:500px;margin:0 auto;width:100%}.card-header[_ngcontent-%COMP%]{font-size:2.5rem;width:70px;height:70px}.card-title[_ngcontent-%COMP%]{font-size:1.5rem}.card-description[_ngcontent-%COMP%]{font-size:1rem}.footer-text[_ngcontent-%COMP%]{margin-top:4rem;font-size:1rem;padding:0 1rem}}@media (max-width: 480px){.benefits-container[_ngcontent-%COMP%]{padding:3rem 4%}.benefits-title[_ngcontent-%COMP%]{font-size:1.8rem;margin-bottom:2.5rem}.cards-container[_ngcontent-%COMP%]{gap:1.5rem;padding:0 1rem}.card_1[_ngcontent-%COMP%], .card_2[_ngcontent-%COMP%], .card_3[_ngcontent-%COMP%]{padding:1.5rem}.card-header[_ngcontent-%COMP%]{font-size:2.25rem;width:65px;height:65px}.card-title[_ngcontent-%COMP%]{font-size:1.3rem}.card-description[_ngcontent-%COMP%]{font-size:.95rem}.footer-text[_ngcontent-%COMP%]{margin-top:3rem;font-size:.95rem}}@media (max-width: 320px){.benefits-container[_ngcontent-%COMP%]{padding:2.5rem 3%}.benefits-title[_ngcontent-%COMP%]{font-size:1.6rem;margin-bottom:2rem}.card_1[_ngcontent-%COMP%], .card_2[_ngcontent-%COMP%], .card_3[_ngcontent-%COMP%]{padding:1.25rem}.card-header[_ngcontent-%COMP%]{font-size:2rem;width:60px;height:60px}.card-title[_ngcontent-%COMP%]{font-size:1.2rem}.card-description[_ngcontent-%COMP%]{font-size:.9rem}.footer-text[_ngcontent-%COMP%]{margin-top:2.5rem;font-size:.9rem}}']})};var $t=class o{static \u0275fac=function(t){return new(t||o)};static \u0275cmp=E({type:o,selectors:[["app-about"]],decls:55,vars:0,consts:[[1,"container"],[1,"mission-section"],[1,"mission-title"],[1,"mission-text"],[1,"features-section"],[1,"features-title"],[1,"features-grid"],[1,"feature-card"],[1,"files"],[1,"bi","bi-file-earmark-text"],[1,"feature-title"],[1,"feature-description"],[1,"wait-time"],[1,"bi","bi-clock"],[1,"clinic-op"],["xmlns","http://www.w3.org/2000/svg","width","30","height","30","fill","#199A8E","viewBox","0 0 16 16",1,"bi","bi-hospital"],["d","M8.5 5.034v1.1l.953-.55.5.867L9 7l.953.55-.5.866-.953-.55v1.1h-1v-1.1l-.953.55-.5-.866L7 7l-.953-.55.5-.866.953.55v-1.1zM13.25 9a.25.25 0 0 0-.25.25v.5c0 .*************.25h.5a.25.25 0 0 0 .25-.25v-.5a.25.25 0 0 0-.25-.25zM13 11.25a.25.25 0 0 1 .25-.25h.5a.25.25 0 0 1 .25.25v.5a.25.25 0 0 1-.25.25h-.5a.25.25 0 0 1-.25-.25zm.25 1.75a.25.25 0 0 0-.25.25v.5c0 .*************.25h.5a.25.25 0 0 0 .25-.25v-.5a.25.25 0 0 0-.25-.25zm-11-4a.25.25 0 0 0-.25.25v.5c0 .*************.25h.5A.25.25 0 0 0 3 9.75v-.5A.25.25 0 0 0 2.75 9zm0 2a.25.25 0 0 0-.25.25v.5c0 .*************.25h.5a.25.25 0 0 0 .25-.25v-.5a.25.25 0 0 0-.25-.25zM2 13.25a.25.25 0 0 1 .25-.25h.5a.25.25 0 0 1 .25.25v.5a.25.25 0 0 1-.25.25h-.5a.25.25 0 0 1-.25-.25z"],["d","M5 1a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v1a1 1 0 0 1 1 1v4h3a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h3V3a1 1 0 0 1 1-1zm2 14h2v-3H7zm3 0h1V3H5v12h1v-3a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1zm0-14H6v1h4zm2 7v7h3V8zm-8 7V8H1v7z"],[1,"people"],[1,"bi","bi-people"],[1,"clip"],[1,"bi","bi-clipboard"],[1,"footer"],[1,"footer-text"],[1,"footer-links"],["href","#"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"div",1)(2,"h2",2),l(3,"Our Mission"),a(),r(4,"p",3),l(5,` At MedSecura, we're on a mission to simplify healthcare for both providers and patients. We're the ultimate solution to healthcare's "paper pileup syndrome," helping clinics escape the burden of excessive paperwork and reducing time spent in endless waiting rooms. `),a()(),r(6,"div",4)(7,"h3",5),l(8,"Key Features"),a(),r(9,"div",6)(10,"div",7)(11,"div",8),g(12,"i",9),r(13,"h4",10),l(14,"Efficient File Management"),a()(),r(15,"p",11),l(16," Streamline your clinic's paperwork and document organization. "),a()(),r(17,"div",7)(18,"div",12),g(19,"i",13),r(20,"h4",10),l(21,"Reduced Waiting Times"),a()(),r(22,"p",11),l(23," Minimize patient time spent in waiting rooms. "),a()(),r(24,"div",7)(25,"div",14),sn(),r(26,"svg",15),g(27,"path",16)(28,"path",17),a(),ln(),r(29,"h4",10),l(30,"Clinic Optimization"),a()(),r(31,"p",11),l(32," Help clinics escape the burden of excessive paperwork. "),a()(),r(33,"div",7)(34,"div",18),g(35,"i",19),r(36,"h4",10),l(37,"Patient-Centric Approach"),a()(),r(38,"p",11),l(39," Improve the healthcare experience for patients. "),a()(),r(40,"div",7)(41,"div",20),g(42,"i",21),r(43,"h4",10),l(44,"Simplified Processes"),a()(),r(45,"p",11),l(46," Streamline healthcare procedures for providers and patients. "),a()()()()(),r(47,"footer",22)(48,"p",23),l(49,"\xA9 2025 MedSecura. All rights reserved."),a(),r(50,"div",24)(51,"a",25),l(52,"Privacy Policy"),a(),r(53,"a",25),l(54,"Terms of Service"),a()()())},styles:['[_nghost-%COMP%]{display:block;width:100%;max-width:100%;overflow-x:hidden;box-sizing:border-box}.container[_ngcontent-%COMP%]{width:100%;height:100%;margin:0 auto;padding:0;font-family:Arial,Helvetica,sans-serif;color:#333}.about-section[_ngcontent-%COMP%]{width:100%;max-width:100%;padding:2rem 1rem;box-sizing:border-box;overflow-x:hidden}.about-container[_ngcontent-%COMP%]{width:100%;max-width:1200px;margin:0 auto;box-sizing:border-box}.mission-section[_ngcontent-%COMP%]{width:100%;text-align:center;padding:4rem 2rem;margin:0 auto}.mission-title[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin-bottom:1rem;color:#199a8e;position:relative;display:inline-block}.mission-title[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-4px;left:0;width:100%;height:2px;background-color:#199a8e;transform:scaleX(0);transition:transform .3s ease}.mission-title[_ngcontent-%COMP%]:hover:after{transform:scaleX(1)}.mission-text[_ngcontent-%COMP%]{font-size:1rem;line-height:1.6}.features-title[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin-bottom:2rem;color:#199a8e;position:relative;display:inline-block}.features-title[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-4px;left:0;width:100%;height:2px;background-color:#199a8e;transform:scaleX(0);transition:transform .3s ease}.features-title[_ngcontent-%COMP%]:hover:after{transform:scaleX(1)}.feature-title[_ngcontent-%COMP%]{font-size:1.5rem;margin:1.2rem 0;color:#2b2b2b}.features-section[_ngcontent-%COMP%]{text-align:center;padding:4rem 0;width:100%}.features-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:2rem;margin:2rem 0;width:100%}.feature-card[_ngcontent-%COMP%]{background:#ffffffb3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:16px;padding:2.5rem;box-shadow:0 4px 6px #0000000d;transition:all .3s ease;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;border:1px solid rgba(25,154,142,.1)}.feature-card[_ngcontent-%COMP%]:hover{transform:translateY(-10px);box-shadow:0 8px 15px #0000001a;border:1px solid rgba(25,154,142,.2);background:#fffc}.feature-description[_ngcontent-%COMP%]{font-size:1.1rem;line-height:1.6;color:#4a4a4a;margin:.8rem 0}.cta-section[_ngcontent-%COMP%]{text-align:center;padding:4rem 2rem;margin:2rem auto;max-width:1200px}.cta-text[_ngcontent-%COMP%]{font-size:1rem;color:#333;margin-bottom:2rem}.cta-button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.btn-lg[_ngcontent-%COMP%]{font-size:1rem}.cta-button[_ngcontent-%COMP%]{background-color:#199a8e;color:#fff;font-size:1rem;padding:.8rem 2rem;border:none;border-radius:5px;cursor:pointer;transition:background-color .3s ease}.cta-button[_ngcontent-%COMP%]:hover{background-color:teal}.btn-outline-dark[_ngcontent-%COMP%]{border:#199A8E;color:#fff;background-color:#199a8e;padding:5px 20px;font-size:15px;font-weight:700;transition:all .3s ease-in-out}.btn-outline-dark[_ngcontent-%COMP%]:hover{background-color:#343a40;color:#fff}.btn-lg[_ngcontent-%COMP%]{font-size:18px;padding:7px 20px}.rounded-pill[_ngcontent-%COMP%]{border-radius:40px}.footer[_ngcontent-%COMP%]{width:100%;background-color:#199a8e;text-align:center;padding:1rem 0;font-size:.9rem}.footer-text[_ngcontent-%COMP%]{color:#fff}.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff;text-decoration:none}.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.b[_ngcontent-%COMP%]{height:200px;fill:#199a8e}.clinic-op[_ngcontent-%COMP%], .wait-time[_ngcontent-%COMP%], .files[_ngcontent-%COMP%], .people[_ngcontent-%COMP%], .clip[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;margin-bottom:1rem}.clinic-op[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wait-time[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .files[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .people[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .clip[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .clinic-op[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{font-size:24px;color:#199a8e}@media screen and (max-width: 1200px){.features-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);padding:0 2rem}.mission-section[_ngcontent-%COMP%]{padding:3rem 1.5rem}.mission-title[_ngcontent-%COMP%], .features-title[_ngcontent-%COMP%]{font-size:2.2rem}}@media screen and (max-width: 768px){.features-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1.5rem;padding:0 1.5rem}.mission-section[_ngcontent-%COMP%]{padding:2.5rem 1.25rem}.mission-title[_ngcontent-%COMP%], .features-title[_ngcontent-%COMP%]{font-size:2rem}.feature-card[_ngcontent-%COMP%]{padding:2rem}.feature-title[_ngcontent-%COMP%]{font-size:1.3rem}.feature-description[_ngcontent-%COMP%]{font-size:1rem}}@media screen and (max-width: 480px){.mission-section[_ngcontent-%COMP%]{padding:2rem 1rem}.mission-title[_ngcontent-%COMP%], .features-title[_ngcontent-%COMP%]{font-size:1.8rem}.mission-text[_ngcontent-%COMP%]{font-size:.95rem}.feature-card[_ngcontent-%COMP%]{padding:1.5rem}.features-grid[_ngcontent-%COMP%]{padding:0 1rem;gap:1.25rem}.feature-title[_ngcontent-%COMP%]{font-size:1.2rem;margin:1rem 0}.feature-description[_ngcontent-%COMP%]{font-size:.95rem}}@media screen and (max-width: 320px){.mission-title[_ngcontent-%COMP%], .features-title[_ngcontent-%COMP%]{font-size:1.6rem}.feature-card[_ngcontent-%COMP%]{padding:1.25rem}.features-grid[_ngcontent-%COMP%]{gap:1rem}.feature-title[_ngcontent-%COMP%]{font-size:1.1rem}.feature-description[_ngcontent-%COMP%]{font-size:.9rem}}']})};function _l(o,e){o&1&&(r(0,"span"),l(1,"\u{1F525} RESET FIREBASE"),a())}function vl(o,e){o&1&&(r(0,"span"),l(1,"\u{1F504} Resetting..."),a())}function xl(o,e){if(o&1&&(r(0,"div",4),l(1),a()),o&2){let t=b();_i("background-color",t.success?"#dcfce7":"#fef2f2")("color",t.success?"#166534":"#dc2626")("border",t.success?"1px solid #bbf7d0":"1px solid #fecaca"),d(),M(" ",t.message," ")}}var Zn=class o{firebaseDbService=Pe(Pn);loading=!1;message="";success=!1;resetFirebase(){if(this.loading||!confirm(`\u{1F6A8} WARNING: This will DELETE ALL data from Firebase!

This includes:
\u2022 All users and profiles
\u2022 All appointments
\u2022 All medical records
\u2022 All doctor/patient data

This action CANNOT be undone!

Are you sure you want to continue?`)||!confirm(`\u26A0\uFE0F FINAL WARNING!

You are about to permanently delete ALL Firebase data.

Type "DELETE" in the next prompt to confirm.`))return;if(prompt('Type "DELETE" to confirm:')!=="DELETE"){alert('Reset cancelled. You must type "DELETE" exactly.');return}this.loading=!0,this.message="",console.log("\u{1F6A8} Starting Firebase reset..."),this.firebaseDbService.resetAllFirebaseData().subscribe({next:i=>{if(this.loading=!1,this.success=i.success,i.success){let s=Object.values(i.deletedCounts||{}).reduce((c,m)=>c+m,0);this.message=`\u2705 Success! Deleted ${s} items.`,console.log("\u2705 Firebase reset successful:",i),setTimeout(()=>{confirm("Reset complete! Refresh the page now?")&&window.location.reload()},3e3)}else this.message=`\u274C Failed: ${i.message}`,console.error("\u274C Firebase reset failed:",i);setTimeout(()=>{this.message=""},1e4)},error:i=>{this.loading=!1,this.success=!1,this.message=`\u274C Error: ${i.message}`,console.error("\u274C Firebase reset error:",i),setTimeout(()=>{this.message=""},1e4)}})}static \u0275fac=function(t){return new(t||o)};static \u0275cmp=E({type:o,selectors:[["app-simple-reset-button"]],decls:5,vars:8,consts:[[2,"position","fixed","top","20px","right","20px","z-index","10000"],["onmouseover","this.style.backgroundColor='#b91c1c'","onmouseout","this.style.backgroundColor='#dc2626'",2,"background-color","#dc2626","color","white","border","none","padding","12px 20px","border-radius","8px","font-weight","bold","font-size","14px","cursor","pointer","box-shadow","0 4px 6px rgba(0, 0, 0, 0.1)","transition","all 0.2s",3,"click","disabled"],[4,"ngIf"],["style",`
             margin-top: 8px; 
             padding: 8px 12px; 
             border-radius: 6px; 
             font-size: 12px; 
             font-weight: 500;
             max-width: 200px;
             word-wrap: break-word;
           `,3,"background-color","color","border",4,"ngIf"],[2,"margin-top","8px","padding","8px 12px","border-radius","6px","font-size","12px","font-weight","500","max-width","200px","word-wrap","break-word"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"button",1),h("click",function(){return n.resetFirebase()}),_(2,_l,2,0,"span",2)(3,vl,2,0,"span",2),a(),_(4,xl,2,7,"div",3),a()),t&2&&(d(),_i("background-color",n.loading?"#9ca3af":"#dc2626")("cursor",n.loading?"not-allowed":"pointer"),p("disabled",n.loading),d(),p("ngIf",!n.loading),d(),p("ngIf",n.loading),d(),p("ngIf",n.message))},dependencies:[A,U],encapsulation:2})};var yl=o=>({"show-scroll":o}),on=class o{constructor(e){this.router=e}showScroll=!1;ngOnInit(){}onWindowScroll(){let e=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;this.showScroll=e>400}scrollToTop(){window.scrollTo({top:0,behavior:"smooth"})}static \u0275fac=function(t){return new(t||o)(P(j))};static \u0275cmp=E({type:o,selectors:[["app-landing"]],hostBindings:function(t,n){t&1&&h("scroll",function(){return n.onWindowScroll()},!1,cn)},decls:42,vars:3,consts:[[1,"landing-page"],[1,"navbar","navbar-expand-lg","navbar-light","bg-transparent"],[1,"container-fluid"],["href","/",1,"navbar-brand"],[2,"font-weight","bold"],["type","button","data-bs-toggle","collapse","data-bs-target","#navbarNav","aria-controls","navbarNav","aria-expanded","false","aria-label","Toggle navigation",1,"navbar-toggler"],[1,"navbar-toggler-icon"],["id","navbarNav",1,"collapse","navbar-collapse"],[1,"navbar-nav","ms-auto",2,"display","flex","gap","2rem"],[1,"nav-item"],["href","#bnft",1,"nav-link","text-hover","text-white","fw-bold"],["href","#abt",1,"nav-link","text-white","fw-bold"],["routerLink","/firebase-test",1,"nav-link","text-white","fw-bold"],[1,"hero-section","d-flex","align-items-center","text-center"],[1,"container"],[1,"display-4","fw-bold","text-white"],[1,"lead","text-white"],["routerLink","login",1,"get_started"],[1,"scroll-down","text-center"],["href","#bnft"],[1,"bi","bi-chevron-down","text-white"],["aria-label","Scroll to top",1,"scroll-to-top",3,"click","ngClass"],[1,"bi","bi-arrow-up"],["id","bnft"],["id","abt"]],template:function(t,n){t&1&&(g(0,"app-simple-reset-button"),r(1,"div",0)(2,"nav",1)(3,"div",2)(4,"a",3)(5,"span",4),l(6,"Med"),a(),l(7,"Secura"),a(),r(8,"button",5),g(9,"span",6),a(),r(10,"div",7)(11,"ul",8)(12,"li",9)(13,"a",10),l(14,"Benefits"),a()(),r(15,"li",9)(16,"a",11),l(17,"About"),a()(),r(18,"li",9)(19,"a",12),l(20,"Test Firebase"),a()()()()()(),r(21,"div",13)(22,"div",14)(23,"h1",15),l(24," The Future of "),g(25,"br"),l(26," Healthcare "),g(27,"br"),l(28," Management "),a(),r(29,"p",16),l(30," Transform your medical practice with our comprehensive digital healthcare platform. "),g(31,"br"),l(32," Secure, efficient, and designed for modern healthcare professionals. "),a(),r(33,"button",17),l(34,"Get Started"),a()()(),r(35,"div",18)(36,"a",19),g(37,"i",20),a()()(),r(38,"button",21),h("click",function(){return n.scrollToTop()}),g(39,"i",22),a(),g(40,"app-benefits",23)(41,"app-about",24)),t&2&&(d(38),p("ngClass",mt(1,yl,n.showScroll)))},dependencies:[A,gt,ve,_e,Wt,$t,ee,Fe,Zn],styles:['[_nghost-%COMP%]{display:block;width:100%;max-width:100%;overflow-x:hidden;box-sizing:border-box}.landing-page[_ngcontent-%COMP%]{min-height:100vh;background:linear-gradient(135deg,#199a8ee6,#0b4d47e6),url(/images/landing.png);background-size:cover;background-position:center;background-attachment:fixed;position:relative;overflow:hidden}.navbar[_ngcontent-%COMP%]{padding:1rem 2rem;position:absolute;width:100%;z-index:10}.navbar-brand[_ngcontent-%COMP%]{color:#fff;font-size:1.5rem;text-decoration:none}.hero-section[_ngcontent-%COMP%]{min-height:100vh;padding:2rem;display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;position:relative}.get_started[_ngcontent-%COMP%]{background:#fff;color:#199a8e;border:none;padding:1rem 2rem;border-radius:30px;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all .3s ease;margin-top:2rem}.get_started[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #0003}.scroll-down[_ngcontent-%COMP%]{position:absolute;bottom:5vh;left:50%;transform:translate(-50%);display:flex;align-items:center;justify-content:center;z-index:100;animation:_ngcontent-%COMP%_fadeIn .8s ease-out 1s forwards}.scroll-down[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2.5rem;color:#fff;animation:_ngcontent-%COMP%_bounce 2s infinite;cursor:pointer;text-shadow:0 2px 4px rgba(0,0,0,.2);display:inline-block}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_bounce{0%,20%,50%,80%,to{transform:translateY(0)}40%{transform:translateY(-15px)}60%{transform:translateY(-7px)}}.scroll-to-top[_ngcontent-%COMP%]{position:fixed;bottom:2rem;right:2rem;background:#199a8e;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;opacity:0;visibility:hidden;z-index:1000}.scroll-to-top.show-scroll[_ngcontent-%COMP%]{opacity:1;visibility:visible}.features-list[_ngcontent-%COMP%]{margin:1.5rem 0}.feature[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin:1rem 0;color:#495057}.feature[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#199a8e}.modal-actions[_ngcontent-%COMP%]{text-align:center;margin-top:2rem}.btn-primary[_ngcontent-%COMP%]{background:#199a8e;color:#fff;border:none;padding:.75rem 1.5rem;border-radius:5px;cursor:pointer;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover{background:#147d73}.container[_ngcontent-%COMP%]{width:100%;height:100%;margin:0 auto;padding:0;font-family:Arial,Helvetica,sans-serif;color:#333}.about-section[_ngcontent-%COMP%]{width:100%;max-width:100%;padding:2rem 1rem;box-sizing:border-box;overflow-x:hidden}.about-container[_ngcontent-%COMP%]{width:100%;max-width:1200px;margin:0 auto;box-sizing:border-box}.mission-section[_ngcontent-%COMP%]{width:100%;text-align:center;padding:4rem 2rem;margin:0 auto}.mission-title[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin-bottom:1rem;color:#199a8e;position:relative}.mission-title[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-4px;left:0;width:100%;height:2px;background-color:#199a8e;transform:scaleX(0);transition:transform .3s ease}.mission-title[_ngcontent-%COMP%]:hover:after{transform:scaleX(1)}.mission-text[_ngcontent-%COMP%]{font-size:1rem;line-height:1.6}.features-title[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin-bottom:2rem;color:#199a8e;position:relative}.features-title[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-4px;left:0;width:100%;height:2px;background-color:#199a8e;transform:scaleX(0);transition:transform .3s ease}.features-title[_ngcontent-%COMP%]:hover:after{transform:scaleX(1)}.feature-title[_ngcontent-%COMP%]{font-size:1.5rem;margin:1.2rem 0;color:#2b2b2b}.features-section[_ngcontent-%COMP%]{text-align:center;padding:4rem 0;width:100%}.features-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:2rem;margin:2rem 0;width:100%}.feature-card[_ngcontent-%COMP%]{background:#ffffffb3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:16px;padding:2.5rem;box-shadow:0 4px 6px #0000000d;transition:all .3s ease;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;border:1px solid rgba(25,154,142,.1)}.feature-card[_ngcontent-%COMP%]:hover{transform:translateY(-10px);box-shadow:0 8px 15px #0000001a;border:1px solid rgba(25,154,142,.2);background:#fffc}.feature-description[_ngcontent-%COMP%]{font-size:1.1rem;line-height:1.6;color:#4a4a4a;margin:.8rem 0}.cta-section[_ngcontent-%COMP%]{text-align:center;padding:4rem 2rem;margin:2rem auto;max-width:1200px}.cta-text[_ngcontent-%COMP%]{font-size:1rem;color:#333;margin-bottom:2rem}.cta-button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.btn-lg[_ngcontent-%COMP%]{font-size:1rem}.cta-button[_ngcontent-%COMP%]{background-color:#199a8e;color:#fff;font-size:1rem;padding:.8rem 2rem;border:none;border-radius:5px;cursor:pointer;transition:background-color .3s ease}.cta-button[_ngcontent-%COMP%]:hover{background-color:teal}.btn-outline-dark[_ngcontent-%COMP%]{border:#199A8E;color:#fff;background-color:#199a8e;padding:5px 20px;font-size:15px;font-weight:700;transition:all .3s ease-in-out}.btn-outline-dark[_ngcontent-%COMP%]:hover{background-color:#343a40;color:#fff}.btn-lg[_ngcontent-%COMP%]{font-size:18px;padding:7px 20px}.rounded-pill[_ngcontent-%COMP%]{border-radius:40px}.footer[_ngcontent-%COMP%]{width:100%;background-color:#199a8e;text-align:center;padding:1rem 0;font-size:.9rem}.footer-text[_ngcontent-%COMP%]{color:#fff}.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff;text-decoration:none}.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.b[_ngcontent-%COMP%]{height:200px;fill:#199a8e}.clinic-op[_ngcontent-%COMP%], .wait-time[_ngcontent-%COMP%], .files[_ngcontent-%COMP%], .people[_ngcontent-%COMP%], .clip[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;margin-bottom:1rem}.clinic-op[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wait-time[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .files[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .people[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .clip[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .clinic-op[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{font-size:24px;color:#199a8e}@media screen and (max-width: 1200px){.features-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);padding:0 2rem}.mission-section[_ngcontent-%COMP%]{padding:3rem 1.5rem}.mission-title[_ngcontent-%COMP%], .features-title[_ngcontent-%COMP%]{font-size:2.2rem}}@media screen and (max-width: 768px){.features-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1.5rem;padding:0 1.5rem}.mission-section[_ngcontent-%COMP%]{padding:2.5rem 1.25rem}.mission-title[_ngcontent-%COMP%], .features-title[_ngcontent-%COMP%]{font-size:2rem}.feature-card[_ngcontent-%COMP%]{padding:2rem}.feature-title[_ngcontent-%COMP%]{font-size:1.3rem}.feature-description[_ngcontent-%COMP%]{font-size:1rem}}@media screen and (max-width: 480px){.mission-section[_ngcontent-%COMP%]{padding:2rem 1rem}.mission-title[_ngcontent-%COMP%], .features-title[_ngcontent-%COMP%]{font-size:1.8rem}.mission-text[_ngcontent-%COMP%]{font-size:.95rem}.feature-card[_ngcontent-%COMP%]{padding:1.5rem}.features-grid[_ngcontent-%COMP%]{padding:0 1rem;gap:1.25rem}.feature-title[_ngcontent-%COMP%]{font-size:1.2rem;margin:1rem 0}.feature-description[_ngcontent-%COMP%]{font-size:.95rem}}@media screen and (max-width: 320px){.mission-title[_ngcontent-%COMP%], .features-title[_ngcontent-%COMP%]{font-size:1.6rem}.feature-card[_ngcontent-%COMP%]{padding:1.25rem}.features-grid[_ngcontent-%COMP%]{gap:1rem}.feature-title[_ngcontent-%COMP%]{font-size:1.1rem}.feature-description[_ngcontent-%COMP%]{font-size:.9rem}}']})};function Cl(o,e){if(o&1&&g(0,"img",39),o&2){let t=b();p("src",t.imagePreview,Tt)}}function Pl(o,e){o&1&&(r(0,"div",40),sn(),r(1,"svg",41),g(2,"path",42),a(),ln(),r(3,"span"),l(4,"Upload Photo"),a()())}function Ml(o,e){o&1&&(r(0,"div",43),l(1,"Full name is required"),a())}function wl(o,e){o&1&&(r(0,"div",43),l(1,"Phone number is required"),a())}function Ol(o,e){o&1&&(r(0,"div",43),l(1,"Specialization is required"),a())}function Sl(o,e){if(o&1){let t=V();r(0,"div",44),l(1),r(2,"i",45),h("click",function(){let i=v(t).index,s=b();return x(s.removeQualification(i))}),a()()}if(o&2){let t=e.$implicit;d(),M(" ",t," ")}}function El(o,e){if(o&1){let t=V();r(0,"div",46),l(1),r(2,"i",45),h("click",function(){let i=v(t).index,s=b();return x(s.removeHospitalAffiliation(i))}),a()()}if(o&2){let t=e.$implicit;d(),M(" ",t," ")}}function kl(o,e){if(o&1){let t=V();r(0,"div",47)(1,"div",48)(2,"div",49),g(3,"i",50),a(),r(4,"h2"),l(5,"Profile Updated Successfully!"),a(),r(6,"p"),l(7,"Your doctor profile has been updated with all the information you provided."),a(),r(8,"button",51),h("click",function(){v(t);let i=b();return x(i.closeModalAndProceed())}),l(9," Proceed to Subscription "),a()()()}}var Kn=class o{constructor(e,t,n,i,s,c,m){this.authService=e;this.router=t;this.profileUpdateService=n;this.http=i;this.db=s;this.doctorService=c;this.ngZone=m}fullName="";email="";phoneNumber="";specialization="";hospitalAffiliations=[];bio="";selectedImage=null;imagePreview=null;qualifications=[];newQualification="";newHospitalAffiliation="";services=[];newService="";showSuccessModal=!1;isSubmitting=!1;baseUrl=Lt.apiUrl+"/api";ngOnInit(){let e=this.authService.getUserInfo();if(console.log("User info retrieved in doctor profile:",e),e){this.initializeFromUserInfo(e);let t=this.authService.getDoctorId()||e?.doctorId||e?.id;console.log("Doctor ID from user info:",t),t&&this.ngZone.runOutsideAngular(()=>{this.fetchDoctorDetailsInBackground(t)})}else console.warn("No user info available - allowing user to create profile"),this.initializeEmptyProfile()}fetchDoctorDetailsInBackground(e){this.ngZone.run(()=>{console.log("Attempting to fetch doctor details in background for ID:",e)}),this.doctorService.getDoctorById(e).subscribe({next:t=>{this.ngZone.run(()=>{console.log("Doctor details fetched successfully from Firestore:",t)})},error:t=>{this.ngZone.run(()=>{console.error("Error fetching doctor details from Firestore - this is expected until security rules are fixed:",t)})}})}initializeFromUserInfo(e){this.fullName=e.name||"",!this.fullName&&e.firstName&&e.lastName&&(this.fullName=`${e.firstName} ${e.lastName}`.trim()),this.email=e.email||"",this.phoneNumber=e.phoneNumber||"",this.specialization=e.specialization||"",Array.isArray(e.hospitalAffiliations)?this.hospitalAffiliations=e.hospitalAffiliations:typeof e.hospitalAffiliations=="string"?this.hospitalAffiliations=e.hospitalAffiliations.split(",").map(t=>t.trim()):this.hospitalAffiliations=[],Array.isArray(e.qualifications)?this.qualifications=e.qualifications:typeof e.qualifications=="string"?this.qualifications=e.qualifications.split(",").map(t=>t.trim()):this.qualifications=[],Array.isArray(e.services)?this.services=e.services:typeof e.services=="string"?this.services=e.services.split(",").map(t=>t.trim()):this.services=[],this.bio=e.bio||"",e.profilePicture&&(this.imagePreview=e.profilePicture),console.log("Doctor profile initialized with user info data:",{fullName:this.fullName,email:this.email,phoneNumber:this.phoneNumber,specialization:this.specialization,hospitalAffiliations:this.hospitalAffiliations,qualifications:this.qualifications,services:this.services,bio:this.bio,hasProfilePicture:!!this.imagePreview})}initializeEmptyProfile(){this.fullName="",this.email="",this.phoneNumber="",this.specialization="",this.hospitalAffiliations=[],this.qualifications=[],this.services=[],this.bio="",this.imagePreview=null,console.log("Doctor profile initialized with empty values for new profile creation")}onImageSelected(e){let t=e.target.files[0];if(t){if(t.size>2*1024*1024){alert("Image is too large. Maximum size is 2MB.");return}if(!t.type.match(/image\/(jpeg|jpg|png|gif)/)){alert("Only image files (JPEG, PNG, GIF) are allowed.");return}let n=new FileReader;n.onload=i=>{this.imagePreview=i.target.result,console.log("Image loaded as base64"),this.updateProfilePicture()},n.readAsDataURL(t)}}convertToBase64(e){return new Promise((t,n)=>{let i=new FileReader;i.onload=()=>{let s=i.result;t(s)},i.onerror=()=>{n(new Error("Failed to read file"))},i.readAsDataURL(e)})}addQualification(){this.newQualification.trim()&&(this.qualifications.push(this.newQualification.trim()),this.newQualification="",this.updateUserInfo())}removeQualification(e){this.qualifications.splice(e,1),this.updateUserInfo()}addHospitalAffiliation(){this.newHospitalAffiliation.trim()&&(this.hospitalAffiliations.push(this.newHospitalAffiliation.trim()),this.newHospitalAffiliation="",this.updateUserInfo())}removeHospitalAffiliation(e){this.hospitalAffiliations.splice(e,1),this.updateUserInfo()}addService(){this.newService.trim()&&(this.services.push(this.newService.trim()),this.newService="",this.updateUserInfo())}removeService(e){this.services.splice(e,1),this.updateUserInfo()}updateUserInfo(){let e=this.authService.getUserInfo(),t=e?Ce(B({},e),{name:this.fullName,email:this.email,phoneNumber:this.phoneNumber,specialization:this.specialization,profilePicture:this.imagePreview,hospitalAffiliations:this.hospitalAffiliations,qualifications:this.qualifications,bio:this.bio,services:this.services}):{name:this.fullName,email:this.email,phoneNumber:this.phoneNumber,specialization:this.specialization,profilePicture:this.imagePreview,hospitalAffiliations:this.hospitalAffiliations,qualifications:this.qualifications,bio:this.bio,services:this.services,role:"doctor",id:"temp_"+Date.now(),firstName:this.fullName.split(" ")[0]||"",lastName:this.fullName.split(" ").slice(1).join(" ")||""};console.log("Updating local user info:",t),this.authService.saveUserInfo(t)}updateProfilePicture(){if(!this.imagePreview){console.error("No image selected");return}console.log("Updating profile picture with base64 data"),this.authService.updateProfilePicture(this.imagePreview).subscribe({next:e=>{console.log("Profile picture updated successfully:",e);let t=this.authService.getUserInfo(),n=t?Ce(B({},t),{profilePicture:this.imagePreview}):{name:this.fullName,email:this.email,phoneNumber:this.phoneNumber,specialization:this.specialization,profilePicture:this.imagePreview,hospitalAffiliations:this.hospitalAffiliations,qualifications:this.qualifications,bio:this.bio,services:this.services,role:"doctor",id:"temp_"+Date.now(),firstName:this.fullName.split(" ")[0]||"",lastName:this.fullName.split(" ").slice(1).join(" ")||""};this.authService.saveUserInfo(n),this.profileUpdateService.notifyProfileUpdate({profilePicture:this.imagePreview,name:this.fullName,specialization:this.specialization,bio:this.bio})},error:e=>{console.error("Error updating profile picture:",e),alert(`Error updating profile picture: ${e.message}`)}})}proceedToSubscription(){this.showSuccessModal=!0}navigateToAvailability(){this.router.navigate(["/doctor-availability"])}closeModalAndProceed(){this.showSuccessModal=!1,this.router.navigate(["/subscription"])}onInputChange(){this.isSubmitting&&(this.isSubmitting=!1)}static \u0275fac=function(t){return new(t||o)(P(le),P(j),P(rt),P(ut),P(He),P(Ut),P(je))};static \u0275cmp=E({type:o,selectors:[["app-doctor-profile"]],decls:75,vars:24,consts:[["fileInput",""],[1,"doctor-profile-container"],[1,"profile-header"],[1,"required-fields-note"],[1,"required-star"],[1,"profile-content"],[1,"left-column"],[1,"personal-info"],[1,"photo-upload"],[1,"image-preview"],["alt","Profile preview",3,"src",4,"ngIf"],["class","upload-placeholder",4,"ngIf"],["type","file","accept","image/*",2,"display","none",3,"change"],[1,"upload-button",3,"click"],[1,"basic-info"],[1,"input-container"],["type","text",3,"ngModelChange","input","ngModel"],["class","error-message",4,"ngIf"],["type","email","readonly","",3,"ngModelChange","ngModel"],["type","tel",3,"ngModelChange","input","ngModel"],[1,"right-column"],[1,"bio"],[1,"bi","bi-heart-pulse"],["placeholder","Please write your bio here...",3,"ngModelChange","ngModel"],[1,"qualifications"],[1,"bi","bi-mortarboard"],[1,"qualification-container"],[1,"qualification-tags"],["class","qualification-pill",4,"ngFor","ngForOf"],["type","text","placeholder","Add another qualification...",3,"ngModelChange","keyup.enter","ngModel"],[1,"hospital-affiliations"],[1,"bi","bi-hospital"],[1,"hospital-affiliation-container"],[1,"hospital-affiliation-tags"],["class","hospital-affiliation-pill",4,"ngFor","ngForOf"],["type","text","placeholder","Add another affiliated institution...",3,"ngModelChange","keyup.enter","ngModel"],[1,"action-buttons"],[1,"save-profile",3,"click"],["class","success-modal-overlay",4,"ngIf"],["alt","Profile preview",3,"src"],[1,"upload-placeholder"],["width","24","height","24","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2",1,"upload-icon"],["d","M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M17 8l-5-5-5 5M12 3v12"],[1,"error-message"],[1,"qualification-pill"],[1,"bi","bi-x-circle",3,"click"],[1,"hospital-affiliation-pill"],[1,"success-modal-overlay"],[1,"success-modal"],[1,"success-icon"],[1,"bi","bi-check-circle-fill"],[1,"proceed-button",3,"click"]],template:function(t,n){if(t&1){let i=V();r(0,"div",1)(1,"header",2)(2,"h1"),l(3,"Doctor Profile"),a(),r(4,"p"),l(5,"Manage your professional information"),a(),r(6,"p",3)(7,"span",4),l(8,"*"),a(),l(9," Required fields must be filled"),a()(),r(10,"main",5)(11,"div",6)(12,"section",7)(13,"div",8)(14,"div",9),_(15,Cl,1,1,"img",10)(16,Pl,5,0,"div",11),a(),r(17,"input",12,0),h("change",function(c){return v(i),x(n.onImageSelected(c))}),a(),r(19,"button",13),h("click",function(){v(i);let c=dt(18);return x(c.click())}),l(20),a()(),r(21,"div",14)(22,"div",15)(23,"label"),l(24,"Full Name "),r(25,"span",4),l(26,"*"),a()(),r(27,"input",16),L("ngModelChange",function(c){return v(i),R(n.fullName,c)||(n.fullName=c),x(c)}),h("input",function(){return v(i),x(n.onInputChange())}),a(),_(28,Ml,2,0,"div",17),a(),r(29,"div",15)(30,"label"),l(31,"Email "),r(32,"span",4),l(33,"*"),a()(),r(34,"input",18),L("ngModelChange",function(c){return v(i),R(n.email,c)||(n.email=c),x(c)}),a()(),r(35,"div",15)(36,"label"),l(37,"Phone Number "),r(38,"span",4),l(39,"*"),a()(),r(40,"input",19),L("ngModelChange",function(c){return v(i),R(n.phoneNumber,c)||(n.phoneNumber=c),x(c)}),h("input",function(){return v(i),x(n.onInputChange())}),a(),_(41,wl,2,0,"div",17),a(),r(42,"div",15)(43,"label"),l(44,"Specialization "),r(45,"span",4),l(46,"*"),a()(),r(47,"input",16),L("ngModelChange",function(c){return v(i),R(n.specialization,c)||(n.specialization=c),x(c)}),h("input",function(){return v(i),x(n.onInputChange())}),a(),_(48,Ol,2,0,"div",17),a()()()(),r(49,"div",20)(50,"section",21)(51,"h2"),g(52,"i",22),l(53,"Bio"),a(),r(54,"textarea",23),L("ngModelChange",function(c){return v(i),R(n.bio,c)||(n.bio=c),x(c)}),a()(),r(55,"section",24)(56,"h2"),g(57,"i",25),l(58,"Qualifications & Certifications"),a(),r(59,"div",26)(60,"div",27),_(61,Sl,3,1,"div",28),a(),r(62,"input",29),L("ngModelChange",function(c){return v(i),R(n.newQualification,c)||(n.newQualification=c),x(c)}),h("keyup.enter",function(){return v(i),x(n.addQualification())}),a()()(),r(63,"section",30)(64,"h2"),g(65,"i",31),l(66,"Hospital Affiliations"),a(),r(67,"div",32)(68,"div",33),_(69,El,3,1,"div",34),a(),r(70,"input",35),L("ngModelChange",function(c){return v(i),R(n.newHospitalAffiliation,c)||(n.newHospitalAffiliation=c),x(c)}),h("keyup.enter",function(){return v(i),x(n.addHospitalAffiliation())}),a()()()()(),r(71,"div",36)(72,"button",37),h("click",function(){return v(i),x(n.proceedToSubscription())}),l(73," Save Profile "),a()()(),_(74,kl,10,0,"div",38)}t&2&&(d(14),ae("has-image",n.imagePreview),d(),p("ngIf",n.imagePreview),d(),p("ngIf",!n.imagePreview),d(4),M(" ",n.imagePreview?"Change Photo":"Upload Photo"," "),d(7),ae("invalid-input",n.isSubmitting&&!n.fullName),z("ngModel",n.fullName),d(),p("ngIf",n.isSubmitting&&!n.fullName),d(6),z("ngModel",n.email),d(6),ae("invalid-input",n.isSubmitting&&!n.phoneNumber),z("ngModel",n.phoneNumber),d(),p("ngIf",n.isSubmitting&&!n.phoneNumber),d(6),ae("invalid-input",n.isSubmitting&&!n.specialization),z("ngModel",n.specialization),d(),p("ngIf",n.isSubmitting&&!n.specialization),d(6),z("ngModel",n.bio),d(7),p("ngForOf",n.qualifications),d(),z("ngModel",n.newQualification),d(7),p("ngForOf",n.hospitalAffiliations),d(),z("ngModel",n.newHospitalAffiliation),d(4),p("ngIf",n.showSuccessModal))},dependencies:[A,Se,U,ee,H,Z,pe],styles:[".doctor-profile-container[_ngcontent-%COMP%]{width:100%;min-height:100vh;background:#f3f4f6;font-family:Inter,sans-serif;padding-bottom:2rem}.profile-header[_ngcontent-%COMP%]{background-color:#199a8e;padding:24px;border-radius:8px;color:#fff;margin-bottom:24px;box-shadow:0 1px 3px #0000001a;text-align:center}.profile-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:600;margin-bottom:8px;letter-spacing:-.5px;color:#fff}.profile-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{opacity:.9;font-size:16px;line-height:1.5;color:#fff;margin-bottom:.5rem}.profile-header[_ngcontent-%COMP%]   .required-fields-note[_ngcontent-%COMP%]{font-size:.85rem;color:#2c3e50;margin-top:1rem;background-color:#ffffffe6;padding:.5rem 1rem;border-radius:4px;display:inline-block;box-shadow:0 1px 3px #0000001a}.required-fields-note[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#ff3860;margin-right:.25rem}.profile-content[_ngcontent-%COMP%]{margin:0 auto;width:98%;max-width:1600px;display:grid;grid-template-columns:400px 1fr;gap:24px}.left-column[_ngcontent-%COMP%], .right-column[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.left-column[_ngcontent-%COMP%]{position:sticky;top:24px}.personal-info[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column}.basic-info[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;justify-content:space-between}section[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:24px;box-shadow:0 1px 3px #0000001a;height:100%;margin:0;display:flex;flex-direction:column}.photo-upload[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1rem;margin-bottom:1.5rem}.image-preview[_ngcontent-%COMP%]{width:150px;height:150px;border-radius:50%;border:2px dashed #ccc;display:flex;align-items:center;justify-content:center;overflow:hidden;background-color:#f8f9fa;cursor:pointer;transition:all .3s ease}.image-preview.has-image[_ngcontent-%COMP%]{border:2px solid #199A8E}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.upload-placeholder[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.5rem;color:#6c757d}.upload-icon[_ngcontent-%COMP%]{width:32px;height:32px;color:#6c757d}.upload-button[_ngcontent-%COMP%]{padding:8px 16px;background-color:#199a8e;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:14px;transition:background-color .3s ease}.upload-button[_ngcontent-%COMP%]:hover{background-color:#158276}input[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{width:100%;padding:.875rem 1.25rem;border:1px solid #e5e7eb;border-radius:.75rem;font-size:.9375rem;color:#374151;background:#fff;transition:all .2s ease;margin-bottom:1rem;box-shadow:0 1px 2px #0000000d}input[_ngcontent-%COMP%]::placeholder, textarea[_ngcontent-%COMP%]::placeholder{color:#afb1b5}input[_ngcontent-%COMP%]:focus, textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 4px #199a8e1a}input[readonly][_ngcontent-%COMP%]{background-color:#f3f4f6;border-color:#e5e7eb;color:#6b7280;cursor:not-allowed}input[readonly][_ngcontent-%COMP%]:focus{border-color:#e5e7eb;box-shadow:none}textarea[_ngcontent-%COMP%]{min-height:120px;resize:vertical}h2[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:18px;font-weight:600;margin-bottom:16px;color:#0a0a0a}h2[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;color:#199a8e}.qualification-container[_ngcontent-%COMP%], .hospital-affiliation-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1rem;flex:1}.qualification-tags[_ngcontent-%COMP%], .hospital-affiliation-tags[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem;min-height:40px}.qualification-pill[_ngcontent-%COMP%], .hospital-affiliation-pill[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;background:#f3f4f6;padding:6px 12px;border-radius:16px;font-size:14px;color:#374151;height:fit-content}.qualification-pill[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .hospital-affiliation-pill[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{cursor:pointer;color:#6b7280;transition:color .2s ease}.qualification-pill[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover, .hospital-affiliation-pill[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover{color:#ef4444}.action-buttons[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:15px;margin-top:30px}.save-profile[_ngcontent-%COMP%], .set-availability[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff;border:none;padding:12px 30px;border-radius:5px;font-size:16px;font-weight:500;cursor:pointer;transition:background-color .3s}.save-profile[_ngcontent-%COMP%]:hover, .set-availability[_ngcontent-%COMP%]:hover{background-color:#45a049}.set-availability[_ngcontent-%COMP%]{background-color:#2196f3}.set-availability[_ngcontent-%COMP%]:hover{background-color:#0b7dda}.required-star[_ngcontent-%COMP%]{color:#ff3860;margin-left:2px}.required-fields-note[_ngcontent-%COMP%]{font-size:.85rem;color:#666;margin-bottom:1rem;text-align:center}@media (max-width: 1200px){.profile-content[_ngcontent-%COMP%]{grid-template-columns:350px 1fr}}@media (max-width: 1024px){.profile-content[_ngcontent-%COMP%]{grid-template-columns:1fr;width:95%}.left-column[_ngcontent-%COMP%]{position:static}}@media (max-width: 640px){.profile-header[_ngcontent-%COMP%]{padding:20px 24px}.profile-content[_ngcontent-%COMP%]{width:98%}section[_ngcontent-%COMP%]{padding:20px}}.success-modal-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.success-modal[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;padding:32px;width:90%;max-width:450px;text-align:center;box-shadow:0 10px 25px #0000001a;animation:_ngcontent-%COMP%_modalFadeIn .3s ease-out}@keyframes _ngcontent-%COMP%_modalFadeIn{0%{opacity:0;transform:translateY(-20px)}to{opacity:1;transform:translateY(0)}}.success-icon[_ngcontent-%COMP%]{font-size:64px;color:#199a8e;margin-bottom:16px}.success-modal[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px;font-weight:600;margin-bottom:16px;color:#111827;justify-content:center}.success-modal[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6b7280;margin-bottom:24px;line-height:1.5}.proceed-button[_ngcontent-%COMP%]{padding:12px 24px;background-color:#199a8e;color:#fff;border:none;border-radius:8px;font-size:16px;font-weight:500;cursor:pointer;transition:all .3s ease}.proceed-button[_ngcontent-%COMP%]:hover{background-color:#158276;transform:translateY(-1px);box-shadow:0 4px 6px #0000001a}.invalid-input[_ngcontent-%COMP%]{border:1px solid #ff3860!important;background-color:#ff38600d}.error-message[_ngcontent-%COMP%]{color:#ff3860;font-size:.8rem;margin-top:.25rem}.field-note[_ngcontent-%COMP%]{color:#6b7280;font-size:.8rem;margin-top:-.5rem;margin-bottom:1rem;display:flex;align-items:center}.field-note[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6b7280;margin-right:.25rem;font-size:.8rem}input[readonly][_ngcontent-%COMP%]{background-color:#f5f5f5;cursor:not-allowed;opacity:.7}"]})};var Qn=class o{constructor(e){this.firestore=e;console.log("DoctorAvailabilityService initialized"),console.log("Firestore instance:",this.firestore?"Available":"Not available")}collectionName="doctorAvailability";addAvailability(e){console.log("Adding availability to Firebase collection:",this.collectionName),console.log("Availability data:",e);try{let t=Q(this.firestore,this.collectionName);return console.log("Collection reference created"),ie(it(t,e)).pipe(Kt(n=>console.log("Document added with ID:",n.id)),we(n=>B({id:n.id},e)),re(n=>(console.error("Error adding availability:",n),console.error("Error code:",n.code),console.error("Error message:",n.message),ce(()=>n))))}catch(t){return console.error("Exception when trying to access Firestore:",t),ce(()=>t)}}getAvailabilitiesByDoctorId(e){console.log("Getting availabilities for doctor ID:",e);try{let t=Q(this.firestore,this.collectionName),n=fe(t,se("doctor_id","==",e));return Rt(n,{idField:"id"}).pipe(Kt(i=>console.log("Retrieved availabilities:",i.length)),we(i=>i),re(i=>(console.error("Error fetching availabilities:",i),ce(()=>i))))}catch(t){return console.error("Exception when trying to access Firestore:",t),ce(()=>t)}}removeAvailability(e){console.log("Removing availability with ID:",e);try{let t=me(this.firestore,`${this.collectionName}/${e}`);return ie(Ct(t)).pipe(Kt(()=>console.log("Document successfully deleted")),we(()=>{}),re(n=>(console.error("Error removing availability:",n),ce(()=>n))))}catch(t){return console.error("Exception when trying to access Firestore:",t),ce(()=>t)}}getAvailabilitiesByDate(e,t){console.log("Getting availabilities for doctor ID:",e,"and date:",t);try{let n=Q(this.firestore,this.collectionName),i=fe(n,se("doctor_id","==",e),se("date","==",t));return Rt(i,{idField:"id"}).pipe(Kt(s=>console.log("Retrieved availabilities for date:",s.length)),we(s=>s),re(s=>(console.error("Error fetching availabilities by date:",s),ce(()=>s))))}catch(n){return console.error("Exception when trying to access Firestore:",n),ce(()=>n)}}static \u0275fac=function(t){return new(t||o)(de(Be))};static \u0275prov=be({token:o,factory:o.\u0275fac,providedIn:"root"})};function Il(o,e){o&1&&(r(0,"p"),l(1,"No availability slots set yet."),a())}function Dl(o,e){if(o&1){let t=V();r(0,"div",15)(1,"div",16)(2,"div",17),l(3),a(),r(4,"div",18),l(5),a()(),r(6,"button",19),h("click",function(){let i=v(t).$implicit,s=b();return x(s.removeAvailability(i.id))}),g(7,"i",20),a()()}if(o&2){let t=e.$implicit,n=b();d(3),T(n.formatDate(t.date)),d(2),T(n.formatTimeRange(t.start_time,t.end_time))}}var Gn=class o{constructor(e,t,n){this.availabilityService=e;this.authService=t;this.router=n}availabilities=[];newAvailability={doctor_id:"",date:"",start_time:"",end_time:""};userId="";ngOnInit(){let e=this.authService.getCurrentUser();if(e&&e.uid)this.userId=e.uid,this.newAvailability.doctor_id=this.userId,this.loadAvailabilities();else{let t=localStorage.getItem("user");if(t)try{let n=JSON.parse(t);n&&n.uid?(this.userId=n.uid,this.newAvailability.doctor_id=this.userId,this.loadAvailabilities()):(console.error("User found in localStorage but no uid"),this.redirectToLogin())}catch(n){console.error("Error parsing user from localStorage:",n),this.redirectToLogin()}else console.error("No user found in localStorage"),this.redirectToLogin()}}redirectToLogin(){alert("Please log in to access this page"),this.router.navigate(["/login"])}loadAvailabilities(){console.log("Loading availabilities for doctor ID:",this.userId),this.availabilityService.getAvailabilitiesByDoctorId(this.userId).subscribe({next:e=>{console.log("Successfully loaded availabilities from Firebase:",e),this.availabilities=e},error:e=>{console.error("Error loading availabilities from Firebase:",e),alert("Failed to load availabilities. Error: "+e.message)}})}addAvailability(){if(!this.isValidAvailability()){alert("Please fill in all fields correctly");return}console.log("Adding availability with data:",this.newAvailability),console.log("Using doctor ID:",this.userId),this.newAvailability.doctor_id||(console.warn("doctor_id was not set, setting it now to",this.userId),this.newAvailability.doctor_id=this.userId),this.availabilityService.addAvailability(this.newAvailability).subscribe({next:e=>{console.log("Successfully added availability to Firebase:",e),this.availabilities.push(e),this.resetForm(),alert("Availability added successfully!")},error:e=>{console.error("Error adding availability to Firebase:",e),alert("Failed to add availability. Please try again. Error: "+e.message)}})}removeAvailability(e){console.log("Removing availability with ID:",e),this.availabilityService.removeAvailability(e).subscribe({next:()=>{console.log("Successfully removed availability from Firebase"),this.availabilities=this.availabilities.filter(t=>t.id!==e)},error:t=>{console.error("Error removing availability from Firebase:",t),alert("Failed to remove availability. Error: "+t.message)}})}resetForm(){this.newAvailability={doctor_id:this.userId,date:"",start_time:"",end_time:""}}isValidAvailability(){return!!(this.newAvailability.date&&this.newAvailability.start_time&&this.newAvailability.end_time&&this.newAvailability.start_time<this.newAvailability.end_time)}formatDate(e){return new Date(e).toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric",year:"numeric"})}formatTimeRange(e,t){return`${this.formatTime(e)} - ${this.formatTime(t)}`}formatTime(e){let[t,n]=e.split(":"),i=parseInt(t,10),s=i>=12?"PM":"AM";return`${i%12||12}:${n} ${s}`}static \u0275fac=function(t){return new(t||o)(P(Qn),P(Vt),P(j))};static \u0275cmp=E({type:o,selectors:[["app-doctor-availability"]],decls:24,vars:5,consts:[[1,"availability-container"],[1,"section-title"],[1,"availability-form"],[1,"form-group"],["for","date"],["type","date","id","date",1,"form-control",3,"ngModelChange","ngModel"],["for","startTime"],["type","time","id","startTime",1,"form-control",3,"ngModelChange","ngModel"],["for","endTime"],["type","time","id","endTime",1,"form-control",3,"ngModelChange","ngModel"],[1,"action-buttons"],[1,"add-availability",3,"click"],[1,"availability-list"],[4,"ngIf"],["class","availability-item",4,"ngFor","ngForOf"],[1,"availability-item"],[1,"availability-details"],[1,"availability-date"],[1,"availability-time"],[1,"remove-button",3,"click"],[1,"bi","bi-trash"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"h2",1),l(2,"Set Your Availability"),a(),r(3,"div",2)(4,"div",3)(5,"label",4),l(6,"Date"),a(),r(7,"input",5),L("ngModelChange",function(s){return R(n.newAvailability.date,s)||(n.newAvailability.date=s),s}),a()(),r(8,"div",3)(9,"label",6),l(10,"Start Time"),a(),r(11,"input",7),L("ngModelChange",function(s){return R(n.newAvailability.start_time,s)||(n.newAvailability.start_time=s),s}),a()(),r(12,"div",3)(13,"label",8),l(14,"End Time"),a(),r(15,"input",9),L("ngModelChange",function(s){return R(n.newAvailability.end_time,s)||(n.newAvailability.end_time=s),s}),a()(),r(16,"div",10)(17,"button",11),h("click",function(){return n.addAvailability()}),l(18,"Add Availability"),a()()(),r(19,"div",12)(20,"h3"),l(21,"Your Availabilities"),a(),_(22,Il,2,0,"p",13)(23,Dl,8,2,"div",14),a()()),t&2&&(d(7),z("ngModel",n.newAvailability.date),d(4),z("ngModel",n.newAvailability.start_time),d(4),z("ngModel",n.newAvailability.end_time),d(7),p("ngIf",n.availabilities.length===0),d(),p("ngForOf",n.availabilities))},dependencies:[A,Se,U,ee,H,Z,pe],styles:[".availability-container[_ngcontent-%COMP%]{background:#fff;border-radius:10px;padding:20px;box-shadow:0 2px 10px #0000000d;margin-bottom:20px}.section-title[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:20px;color:#333;border-bottom:1px solid #eee;padding-bottom:10px}.availability-form[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:15px;margin-bottom:20px}.form-group[_ngcontent-%COMP%]{margin-bottom:15px}label[_ngcontent-%COMP%]{display:block;margin-bottom:5px;font-weight:500;color:#555}.form-control[_ngcontent-%COMP%]{width:100%;padding:10px;border:1px solid #ddd;border-radius:5px;font-size:14px}.action-buttons[_ngcontent-%COMP%]{grid-column:1 / -1;display:flex;justify-content:flex-end}.add-availability[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;font-weight:500;transition:background-color .3s}.add-availability[_ngcontent-%COMP%]:hover{background-color:#45a049}.availability-list[_ngcontent-%COMP%]{margin-top:30px}.availability-list[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem;margin-bottom:15px;color:#444}.availability-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:12px 15px;background-color:#f9f9f9;border-radius:5px;margin-bottom:10px;border-left:3px solid #4caf50}.availability-details[_ngcontent-%COMP%]{display:flex;gap:15px}.availability-date[_ngcontent-%COMP%]{font-weight:500}.availability-time[_ngcontent-%COMP%]{color:#666}.remove-button[_ngcontent-%COMP%]{background:none;border:none;color:#ff5252;cursor:pointer;font-size:1.2rem;transition:color .3s}.remove-button[_ngcontent-%COMP%]:hover{color:red}"]})};function Al(o,e){if(o&1&&(r(0,"div",30),l(1),a()),o&2){let t=b();p("@fadeSlideInOut",void 0),d(),M(" ",t.currentPasswordError," ")}}function Fl(o,e){if(o&1&&(r(0,"div",31)(1,"div",32),g(2,"div"),a(),r(3,"span",33),l(4),tt(5,"titlecase"),a()()),o&2){let t=b();d(2),Oe("strength-level "+t.passwordStrength),d(2),M("Password Strength: ",pt(5,3,t.passwordStrength),"")}}function Nl(o,e){o&1&&(r(0,"div",30),l(1," Password must be at least 8 characters long "),a()),o&2&&p("@fadeSlideInOut",void 0)}function zl(o,e){o&1&&(r(0,"div",30),l(1," Passwords do not match "),a()),o&2&&p("@fadeSlideInOut",void 0)}function Rl(o,e){o&1&&(r(0,"span"),l(1,"Change Password"),a())}function Ll(o,e){o&1&&g(0,"span",34)}var Jn=class o{constructor(e,t){this.router=e;this.authService=t}currentPassword="";newPassword="";confirmPassword="";currentPasswordError="";isLoading=!1;showCurrentPassword=!1;showNewPassword=!1;showConfirmPassword=!1;passwordStrength="weak";get passwordMismatch(){return this.newPassword!==this.confirmPassword&&this.confirmPassword!==""}toggleCurrentPassword(){this.showCurrentPassword=!this.showCurrentPassword}toggleNewPassword(){this.showNewPassword=!this.showNewPassword}toggleConfirmPassword(){this.showConfirmPassword=!this.showConfirmPassword}clearErrors(){this.currentPasswordError=""}validatePassword(){if(!this.newPassword){this.passwordStrength="weak";return}let e=/[A-Z]/.test(this.newPassword),t=/[a-z]/.test(this.newPassword),n=/\d/.test(this.newPassword),i=/[!@#$%^&*(),.?":{}|<>]/.test(this.newPassword),s=[e,t,n,i].filter(Boolean).length;s<=2?this.passwordStrength="weak":s===3?this.passwordStrength="medium":this.passwordStrength="strong"}goBack(){this.router.navigate(["/settings"])}onSubmit(){this.isLoading||this.passwordMismatch||(this.isLoading=!0,this.clearErrors(),this.authService.verifyCurrentPassword(this.currentPassword).subscribe({next:()=>{this.authService.changePassword(this.currentPassword,this.newPassword).subscribe({next:()=>{alert("Password changed successfully!"),this.router.navigate(["/settings"])},error:e=>{this.currentPasswordError=e.message||"Failed to change password. Please try again."},complete:()=>{this.isLoading=!1}})},error:e=>{this.currentPasswordError="Current password is incorrect",this.isLoading=!1}}))}static \u0275fac=function(t){return new(t||o)(P(j),P(le))};static \u0275cmp=E({type:o,selectors:[["app-change-password"]],features:[et([le])],decls:49,vars:32,consts:[["passwordForm","ngForm"],["currentPasswordInput","ngModel"],["newPasswordInput","ngModel"],["confirmPasswordInput","ngModel"],[1,"page-container"],[1,"change-password-container"],[1,"header"],[1,"back-button",3,"click"],[1,"bi","bi-arrow-left"],[1,"subtitle"],[1,"form-container"],[3,"ngSubmit"],[1,"form-group"],["for","currentPassword"],[1,"bi","bi-lock"],[1,"password-input-container"],["id","currentPassword","name","currentPassword","required","",1,"form-control",3,"ngModelChange","input","type","ngModel"],["type","button",1,"toggle-password",3,"click"],["class","error-message",4,"ngIf"],["for","newPassword"],[1,"bi","bi-shield-lock"],["id","newPassword","name","newPassword","required","","minlength","8",1,"form-control",3,"ngModelChange","input","type","ngModel"],["class","password-strength",4,"ngIf"],["for","confirmPassword"],[1,"bi","bi-check-circle"],["id","confirmPassword","name","confirmPassword","required","",1,"form-control",3,"ngModelChange","input","type","ngModel"],[1,"button-container"],["type","submit",1,"submit-button",3,"disabled"],[4,"ngIf"],["class","spinner",4,"ngIf"],[1,"error-message"],[1,"password-strength"],[1,"strength-meter"],[1,"strength-text"],[1,"spinner"]],template:function(t,n){if(t&1){let i=V();r(0,"div",4)(1,"div",5)(2,"div",6)(3,"button",7),h("click",function(){return v(i),x(n.goBack())}),g(4,"i",8),r(5,"span"),l(6,"Back to Settings"),a()(),r(7,"h1"),l(8,"Change Password"),a(),r(9,"p",9),l(10,"Update your password to keep your account secure"),a()(),r(11,"div",10)(12,"form",11,0),h("ngSubmit",function(){return v(i),x(n.onSubmit())}),r(14,"div",12)(15,"label",13),g(16,"i",14),l(17," Current Password "),a(),r(18,"div",15)(19,"input",16,1),L("ngModelChange",function(c){return v(i),R(n.currentPassword,c)||(n.currentPassword=c),x(c)}),h("input",function(){return v(i),x(n.clearErrors())}),a(),r(21,"button",17),h("click",function(){return v(i),x(n.toggleCurrentPassword())}),g(22,"i"),a()(),_(23,Al,2,2,"div",18),a(),r(24,"div",12)(25,"label",19),g(26,"i",20),l(27," New Password "),a(),r(28,"div",15)(29,"input",21,2),L("ngModelChange",function(c){return v(i),R(n.newPassword,c)||(n.newPassword=c),x(c)}),h("input",function(){return v(i),x(n.validatePassword())}),a(),r(31,"button",17),h("click",function(){return v(i),x(n.toggleNewPassword())}),g(32,"i"),a()(),_(33,Fl,6,5,"div",22)(34,Nl,2,1,"div",18),a(),r(35,"div",12)(36,"label",23),g(37,"i",24),l(38," Confirm New Password "),a(),r(39,"div",15)(40,"input",25,3),L("ngModelChange",function(c){return v(i),R(n.confirmPassword,c)||(n.confirmPassword=c),x(c)}),h("input",function(){return v(i),x(n.validatePassword())}),a(),r(42,"button",17),h("click",function(){return v(i),x(n.toggleConfirmPassword())}),g(43,"i"),a()(),_(44,zl,2,1,"div",18),a(),r(45,"div",26)(46,"button",27),_(47,Rl,2,0,"span",28)(48,Ll,1,0,"span",29),a()()()()()()}if(t&2){let i=dt(13),s=dt(30),c=dt(41);p("@fadeIn",void 0),d(),p("@fadeSlideInOut",void 0),d(11),p("@fadeSlideInOut",void 0),d(2),ae("error",n.currentPasswordError),d(5),ae("is-invalid",n.currentPasswordError),p("type",n.showCurrentPassword?"text":"password"),z("ngModel",n.currentPassword),d(3),Oe(n.showCurrentPassword?"bi bi-eye-slash":"bi bi-eye"),d(),p("ngIf",n.currentPasswordError),d(6),ae("is-invalid",s.invalid&&s.touched),p("type",n.showNewPassword?"text":"password"),z("ngModel",n.newPassword),d(3),Oe(n.showNewPassword?"bi bi-eye-slash":"bi bi-eye"),d(),p("ngIf",n.newPassword),d(),p("ngIf",s.invalid&&s.touched),d(6),ae("is-invalid",n.passwordMismatch&&c.touched),p("type",n.showConfirmPassword?"text":"password"),z("ngModel",n.confirmPassword),d(3),Oe(n.showConfirmPassword?"bi bi-eye-slash":"bi bi-eye"),d(),p("ngIf",n.passwordMismatch&&c.touched),d(2),ae("loading",n.isLoading),p("disabled",!i.form.valid||n.passwordMismatch||n.isLoading),d(),p("ngIf",!n.isLoading),d(),p("ngIf",n.isLoading)}},dependencies:[A,U,un,ee,Ae,H,Z,De,Jo,Xo,pe,Ko],styles:[".page-container[_ngcontent-%COMP%]{min-height:100vh;padding:2rem;background:#f8f9fa;display:flex;align-items:center;justify-content:center}.change-password-container[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 8px 24px #0000001a;padding:2.5rem;width:100%;max-width:500px;position:relative}.header[_ngcontent-%COMP%]{margin-bottom:2.5rem;text-align:center}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:1rem 0 .5rem;color:#2c3e50;font-size:2rem;font-weight:600}.subtitle[_ngcontent-%COMP%]{color:#6c757d;margin-bottom:0}.back-button[_ngcontent-%COMP%]{position:absolute;top:1.5rem;left:1.5rem;background:none;border:none;color:#199a8e;cursor:pointer;padding:.5rem;display:flex;align-items:center;gap:.5rem;font-size:.9rem;transition:color .2s}.back-button[_ngcontent-%COMP%]:hover{color:#147f75}.form-group[_ngcontent-%COMP%]{margin-bottom:1.5rem;position:relative}label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.5rem;color:#495057;font-weight:500}.password-input-container[_ngcontent-%COMP%]{position:relative}.form-control[_ngcontent-%COMP%]{width:100%;padding:.75rem 1rem;border:2px solid #e9ecef;border-radius:8px;font-size:1rem;transition:all .2s}.form-control[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 3px #199a8e1a}.form-control.is-invalid[_ngcontent-%COMP%]{border-color:#dc3545}.toggle-password[_ngcontent-%COMP%]{position:absolute;right:1rem;top:50%;transform:translateY(-50%);background:none;border:none;color:#6c757d;cursor:pointer;padding:.25rem}.toggle-password[_ngcontent-%COMP%]:hover{color:#495057}.error-message[_ngcontent-%COMP%]{color:#dc3545;font-size:.875rem;margin-top:.5rem;display:flex;align-items:center;gap:.25rem}.password-strength[_ngcontent-%COMP%]{margin-top:.75rem}.strength-meter[_ngcontent-%COMP%]{height:4px;background:#e9ecef;border-radius:2px;margin-bottom:.5rem}.strength-level[_ngcontent-%COMP%]{height:100%;border-radius:2px;transition:width .3s ease}.strength-level.weak[_ngcontent-%COMP%]{width:33.33%;background:#dc3545}.strength-level.medium[_ngcontent-%COMP%]{width:66.66%;background:#ffc107}.strength-level.strong[_ngcontent-%COMP%]{width:100%;background:#28a745}.strength-text[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d}.button-container[_ngcontent-%COMP%]{margin-top:2.5rem}.submit-button[_ngcontent-%COMP%]{width:100%;padding:1rem;background-color:#199a8e;color:#fff;border:none;border-radius:8px;font-size:1rem;font-weight:500;cursor:pointer;transition:all .2s;position:relative}.submit-button[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#147f75;transform:translateY(-1px);box-shadow:0 4px 12px #199a8e33}.submit-button[_ngcontent-%COMP%]:disabled{background-color:#e9ecef;cursor:not-allowed}.submit-button.loading[_ngcontent-%COMP%]{padding-right:2.5rem}.spinner[_ngcontent-%COMP%]{width:20px;height:20px;border:2px solid #ffffff;border-top-color:transparent;border-radius:50%;animation:_ngcontent-%COMP%_spinner .8s linear infinite;position:absolute;right:1rem;top:50%;transform:translateY(-50%)}@keyframes _ngcontent-%COMP%_spinner{to{transform:translateY(-50%) rotate(360deg)}}@media (max-width: 576px){.page-container[_ngcontent-%COMP%]{padding:1rem}.change-password-container[_ngcontent-%COMP%]{padding:1.5rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.back-button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:none}}"],data:{animation:[Qe("fadeSlideInOut",[Re(":enter",[ne({opacity:0,transform:"translateY(-10px)"}),ze("300ms ease-out",ne({opacity:1,transform:"translateY(0)"}))]),Re(":leave",[ze("300ms ease-in",ne({opacity:0,transform:"translateY(-10px)"}))])]),Qe("fadeIn",[Re(":enter",[ne({opacity:0}),ze("300ms ease-out",ne({opacity:1}))])])]}})};var Yt=class o{title="Success!";message="Operation completed successfully.";closed=new so;onClose(){this.closed.emit()}static \u0275fac=function(t){return new(t||o)};static \u0275cmp=E({type:o,selectors:[["app-success-dialog"]],inputs:{title:"title",message:"message"},outputs:{closed:"closed"},decls:10,vars:3,consts:[[1,"success-dialog"],[1,"dialog-content"],[1,"success-icon"],[3,"click"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"div",1)(2,"div",2),l(3,"\u2713"),a(),r(4,"h2"),l(5),a(),r(6,"p"),l(7),a(),r(8,"button",3),h("click",function(){return n.onClose()}),l(9,"Close"),a()()()),t&2&&(p("@fadeIn",void 0),d(5),T(n.title),d(2),T(n.message))},dependencies:[A],styles:[".success-dialog[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.dialog-content[_ngcontent-%COMP%]{background-color:#fff;padding:2rem;border-radius:8px;text-align:center;max-width:400px;width:90%}.success-icon[_ngcontent-%COMP%]{font-size:3rem;color:#4caf50;margin-bottom:1rem}h2[_ngcontent-%COMP%]{margin:0 0 1rem;color:#333}p[_ngcontent-%COMP%]{margin:0 0 1.5rem;color:#666}button[_ngcontent-%COMP%]{padding:.5rem 1.5rem;background-color:#4caf50;color:#fff;border:none;border-radius:4px;cursor:pointer;transition:background-color .2s}button[_ngcontent-%COMP%]:hover{background-color:#45a049}"],data:{animation:[Qe("fadeIn",[Re(":enter",[ne({opacity:0,transform:"translateY(10px)"}),ze("300ms ease-out",ne({opacity:1,transform:"translateY(0)"}))])])]}})};function Vl(o,e){if(o&1){let t=V();r(0,"app-success-dialog",40),h("closed",function(){v(t);let i=b();return x(i.onSuccessDialogClosed())}),a()}o&2&&p("title","Success!")("message","Your profile has been updated successfully.")}function Ul(o,e){if(o&1&&g(0,"img",41),o&2){let t=b();p("src",t.imagePreview,Tt)}}function jl(o,e){o&1&&(r(0,"div",42),g(1,"i",43),a())}function Bl(o,e){if(o&1){let t=V();r(0,"div",44),l(1),r(2,"i",45),h("click",function(){let i=v(t).index,s=b();return x(s.removeHospitalAffiliation(i))}),a()()}if(o&2){let t=e.$implicit;p("@tagAnimation",void 0),d(),M(" ",t," ")}}var Xn=class o{constructor(e,t,n){this.authService=e;this.router=t;this.profileUpdateService=n}fullName="";email="";phoneNumber="";specialization="";bio="";address="";hospitalAffiliations=[];newHospitalAffiliation="";selectedImage=null;imagePreview=null;showSuccessDialog=!1;ngOnInit(){let e=this.authService.getUserInfo();e&&(this.fullName=e.name||"",this.email=e.email||"",this.phoneNumber=e.phoneNumber||"",this.specialization=e.specialization||"",this.imagePreview=e.profilePicture||null,this.bio=e.bio||"",this.address=e.address||"",e.hospitalAffiliations?Array.isArray(e.hospitalAffiliations)?this.hospitalAffiliations=e.hospitalAffiliations:typeof e.hospitalAffiliations=="string"?this.hospitalAffiliations=e.hospitalAffiliations.split(",").map(t=>t.trim()):this.hospitalAffiliations=[]:this.hospitalAffiliations=[])}onImageSelected(e){let t=e.target;if(t.files&&t.files[0]){this.selectedImage=t.files[0];let n=new FileReader;n.onload=()=>{this.imagePreview=n.result},n.readAsDataURL(this.selectedImage)}}addHospitalAffiliation(){this.newHospitalAffiliation.trim()&&(this.hospitalAffiliations.push(this.newHospitalAffiliation.trim()),this.newHospitalAffiliation="")}removeHospitalAffiliation(e){this.hospitalAffiliations.splice(e,1)}saveChanges(){return ke(this,null,function*(){try{if(!this.authService.getDoctorId()){console.error("Doctor ID not found");return}let t={firstName:this.fullName.split(" ")[0],lastName:this.fullName.split(" ").slice(1).join(" "),specialization:this.specialization,contactNumber:this.phoneNumber,bio:this.bio||"",officeAddress:this.address||"",officeHours:"",education:"",experience:this.hospitalAffiliations.join(", "),awards:""};console.log("Sending profile data:",t),this.authService.updateProfile(t).subscribe({next:n=>{console.log("Profile updated successfully:",n),this.selectedImage&&this.imagePreview?this.updateProfilePicture(n):this.completeProfileUpdate(n)},error:n=>{console.error("Error updating profile:",n),alert("Failed to update profile: "+n.message)}})}catch(e){console.error("Error in saveChanges:",e),alert("An unexpected error occurred")}})}updateProfilePicture(e){this.authService.updateProfilePicture(this.imagePreview).subscribe({next:t=>{console.log("Profile picture updated successfully:",t),this.completeProfileUpdate(e)},error:t=>{console.error("Error updating profile picture:",t),this.completeProfileUpdate(e)}})}completeProfileUpdate(e){this.profileUpdateService.notifyProfileUpdate({name:this.fullName,profilePicture:this.imagePreview}),this.showSuccessDialog=!0}onSuccessDialogClosed(){this.showSuccessDialog=!1,this.router.navigate(["/settings"])}logout(){this.authService.clearUserInfo(),this.router.navigate(["/login"])}goBack(){this.router.navigate(["/settings"])}static \u0275fac=function(t){return new(t||o)(P(le),P(j),P(rt))};static \u0275cmp=E({type:o,selectors:[["app-doctor-information"]],features:[et([le,rt])],decls:60,vars:17,consts:[["fileInput",""],[1,"page-container"],[3,"title","message","closed",4,"ngIf"],[1,"main-content"],[1,"doctor-info-container"],[1,"header"],[1,"back-button",3,"click"],[1,"bi","bi-arrow-left"],[1,"form-content"],[1,"photo-section"],[1,"photo-upload"],[1,"image-preview"],["alt","Profile preview",3,"src",4,"ngIf"],["class","upload-placeholder",4,"ngIf"],["type","file","accept","image/*",2,"display","none",3,"change"],[1,"upload-button",3,"click"],[1,"form-section"],[1,"grid-container"],[1,"input-group"],["for","fullName"],["type","text","id","fullName","placeholder","Full Name","autocomplete","name",3,"ngModelChange","ngModel"],["for","email"],["type","email","id","email","placeholder","Email Address","autocomplete","email",3,"ngModelChange","ngModel"],["for","phoneNumber"],["type","tel","id","phoneNumber","placeholder","Phone Number","autocomplete","tel",3,"ngModelChange","ngModel"],["for","specialization"],["type","text","id","specialization","placeholder","Specialization",3,"ngModelChange","ngModel"],[1,"input-group","full-width"],["for","bio"],["id","bio","placeholder","Write a short bio about yourself, your experience, and expertise...","rows","4",3,"ngModelChange","ngModel"],["for","hospitalAffiliation"],[1,"tags-container"],["class","tag",4,"ngFor","ngForOf"],[1,"add-tag"],["type","text","id","hospitalAffiliation","placeholder","Add hospital affiliation",3,"ngModelChange","keyup.enter","ngModel"],[3,"click"],["for","address"],["type","text","id","address","placeholder","Practice Address","autocomplete","address-line1",3,"ngModelChange","ngModel"],[1,"action-buttons"],[1,"save-button",3,"click"],[3,"closed","title","message"],["alt","Profile preview",3,"src"],[1,"upload-placeholder"],[1,"bi","bi-person-fill"],[1,"tag"],[1,"bi","bi-x",3,"click"]],template:function(t,n){if(t&1){let i=V();r(0,"div",1),_(1,Vl,1,2,"app-success-dialog",2),r(2,"div",3)(3,"div",4)(4,"header",5)(5,"button",6),h("click",function(){return v(i),x(n.goBack())}),g(6,"i",7),l(7," Back to Settings "),a(),r(8,"h1"),l(9,"Doctor Information"),a(),r(10,"p"),l(11,"Update your personal and professional details"),a()(),r(12,"div",8)(13,"div",9)(14,"div",10)(15,"div",11),_(16,Ul,1,1,"img",12)(17,jl,2,0,"div",13),a(),r(18,"input",14,0),h("change",function(c){return v(i),x(n.onImageSelected(c))}),a(),r(20,"button",15),h("click",function(){v(i);let c=dt(19);return x(c.click())}),l(21),a()()(),r(22,"div",16)(23,"div",17)(24,"div",18)(25,"label",19),l(26,"Full Name"),a(),r(27,"input",20),L("ngModelChange",function(c){return v(i),R(n.fullName,c)||(n.fullName=c),x(c)}),a()(),r(28,"div",18)(29,"label",21),l(30,"Email"),a(),r(31,"input",22),L("ngModelChange",function(c){return v(i),R(n.email,c)||(n.email=c),x(c)}),a()(),r(32,"div",18)(33,"label",23),l(34,"Phone Number"),a(),r(35,"input",24),L("ngModelChange",function(c){return v(i),R(n.phoneNumber,c)||(n.phoneNumber=c),x(c)}),a()(),r(36,"div",18)(37,"label",25),l(38,"Specialization"),a(),r(39,"input",26),L("ngModelChange",function(c){return v(i),R(n.specialization,c)||(n.specialization=c),x(c)}),a()(),r(40,"div",27)(41,"label",28),l(42,"Bio"),a(),r(43,"textarea",29),L("ngModelChange",function(c){return v(i),R(n.bio,c)||(n.bio=c),x(c)}),a()(),r(44,"div",27)(45,"label",30),l(46,"Hospital Affiliations"),a(),r(47,"div",31),_(48,Bl,3,2,"div",32),a(),r(49,"div",33)(50,"input",34),L("ngModelChange",function(c){return v(i),R(n.newHospitalAffiliation,c)||(n.newHospitalAffiliation=c),x(c)}),h("keyup.enter",function(){return v(i),x(n.addHospitalAffiliation())}),a(),r(51,"button",35),h("click",function(){return v(i),x(n.addHospitalAffiliation())}),l(52,"Add"),a()()(),r(53,"div",27)(54,"label",36),l(55,"Practice Address"),a(),r(56,"input",37),L("ngModelChange",function(c){return v(i),R(n.address,c)||(n.address=c),x(c)}),a()()()(),r(57,"div",38)(58,"button",39),h("click",function(){return v(i),x(n.saveChanges())}),l(59,"Save Changes"),a()()()()()()}t&2&&(d(),p("ngIf",n.showSuccessDialog),d(2),p("@fadeIn",void 0),d(),p("@slideIn",void 0),d(11),ae("has-image",n.imagePreview),d(),p("ngIf",n.imagePreview),d(),p("ngIf",!n.imagePreview),d(4),M(" ",n.imagePreview?"Change Photo":"Upload Photo"," "),d(),p("@slideIn",void 0),d(5),z("ngModel",n.fullName),d(4),z("ngModel",n.email),d(4),z("ngModel",n.phoneNumber),d(4),z("ngModel",n.specialization),d(4),z("ngModel",n.bio),d(5),p("ngForOf",n.hospitalAffiliations),d(2),z("ngModel",n.newHospitalAffiliation),d(6),z("ngModel",n.address))},dependencies:[A,Se,U,ee,H,Z,pe,Yt],styles:[".grid-container[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:1.5rem;width:100%}.full-width[_ngcontent-%COMP%]{grid-column:1 / -1}.form-content[_ngcontent-%COMP%]{background:#fff;border-radius:.75rem;padding:2rem;max-width:900px;margin:0 auto}.input-group[_ngcontent-%COMP%]{margin-bottom:1.5rem;display:flex;flex-direction:column}.input-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;font-weight:500;color:#2d3748;margin-bottom:.5rem;font-size:.95rem}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:.85rem 1rem;border:2px solid #e2e8f0;border-radius:.5rem;font-size:1rem;transition:all .25s ease;background-color:#fff}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .input-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{border-color:#199a8e;box-shadow:0 0 0 3px #199a8e26;outline:none}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .input-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder{color:#cbd5e1}textarea[_ngcontent-%COMP%]{min-height:120px;resize:vertical;line-height:1.5}.photo-section[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:2.5rem}.photo-upload[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.image-preview[_ngcontent-%COMP%]{width:130px;height:130px;border-radius:50%;margin:0 auto 1.5rem;overflow:hidden;display:flex;align-items:center;justify-content:center;background:#f8fafc;border:3px solid #e2e8f0;transition:all .3s ease;position:relative}.image-preview[_ngcontent-%COMP%]:hover{border-color:#199a8e}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.upload-placeholder[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#f1f5f9}.upload-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;color:#94a3b8}.upload-button[_ngcontent-%COMP%], .save-button[_ngcontent-%COMP%]{background:#199a8e;color:#fff;border:none;padding:.85rem 1.5rem;border-radius:.5rem;font-weight:500;cursor:pointer;transition:all .3s ease;font-size:.95rem}.upload-button[_ngcontent-%COMP%]:hover, .save-button[_ngcontent-%COMP%]:hover{background:#158276;transform:translateY(-2px);box-shadow:0 4px 12px #199a8e33}.upload-button[_ngcontent-%COMP%]:active, .save-button[_ngcontent-%COMP%]:active{transform:translateY(0)}.tags-container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.75rem;margin-bottom:.75rem;min-height:36px;padding:.25rem 0}.tag[_ngcontent-%COMP%]{background:#f8fafc;padding:.6rem 1rem;border-radius:25px;display:flex;align-items:center;gap:.5rem;font-size:.875rem;color:#2d3748;border:1px solid #e2e8f0;transition:all .2s ease}.tag[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{cursor:pointer;color:#64748b;transition:color .2s ease}.tag[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover{color:#e53e3e}.add-tag[_ngcontent-%COMP%]{display:flex;gap:.5rem;width:100%}.add-tag[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{flex:1;min-width:0}.add-tag[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:#199a8e;color:#fff;border:none;padding:.75rem 1.25rem;border-radius:.5rem;cursor:pointer;transition:all .3s ease;white-space:nowrap}.add-tag[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:#158276;transform:translateY(-1px);box-shadow:0 4px 8px #199a8e33}.action-buttons[_ngcontent-%COMP%]{margin-top:2.5rem;text-align:right;padding-right:1rem}.save-button[_ngcontent-%COMP%]{padding:1rem 2.5rem;font-size:1rem;font-weight:600;letter-spacing:.5px}.back-button[_ngcontent-%COMP%]{background:none;border:none;color:#199a8e;display:flex;align-items:center;gap:.5rem;padding:.75rem;margin-bottom:1.5rem;cursor:pointer;font-size:.875rem;border-radius:.5rem;transition:all .3s ease;font-weight:500}.back-button[_ngcontent-%COMP%]:hover{color:#158276;background:#199a8e0d;transform:translate(-2px)}.header[_ngcontent-%COMP%]{margin-bottom:2.5rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#199a8e;font-size:1.75rem;margin:0;font-weight:600}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#64748b;margin-top:.5rem;font-size:.95rem}.page-container[_ngcontent-%COMP%]{display:flex;min-height:100vh;background:#f8fafc;width:100%}.sidebar[_ngcontent-%COMP%]{width:260px;background:#fff;box-shadow:2px 0 5px #0000000d;display:flex;flex-direction:column;position:fixed;height:100vh}.main-content[_ngcontent-%COMP%]{flex:1;padding:2rem;margin:0 auto;max-width:1200px;width:100%}.doctor-info-container[_ngcontent-%COMP%]{background:#fff;border-radius:1rem;padding:2.5rem;box-shadow:0 8px 16px #0000000d;overflow:hidden}@media (max-width: 768px){.grid-container[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1.25rem}.full-width[_ngcontent-%COMP%]{grid-column:auto}.form-content[_ngcontent-%COMP%]{padding:1.5rem}.sidebar[_ngcontent-%COMP%]{width:100%;position:relative;height:auto}.main-content[_ngcontent-%COMP%]{margin-left:0}.input-group[_ngcontent-%COMP%]{margin-bottom:1.25rem}.photo-section[_ngcontent-%COMP%]{margin-bottom:2rem}.action-buttons[_ngcontent-%COMP%]{text-align:center;padding-right:0}.save-button[_ngcontent-%COMP%]{width:100%;max-width:300px}}@media (max-width: 480px){.main-content[_ngcontent-%COMP%]{padding:1rem}.doctor-info-container[_ngcontent-%COMP%]{padding:1.5rem 1rem;border-radius:.75rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.form-content[_ngcontent-%COMP%]{padding:1rem}.add-tag[_ngcontent-%COMP%]{flex-direction:column}.add-tag[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}@media (max-width: 992px){.doctor-info-container[_ngcontent-%COMP%]{padding:2rem 1.5rem}.form-content[_ngcontent-%COMP%]{padding:1.5rem}}"],data:{animation:[Qe("fadeIn",[Re(":enter",[ne({opacity:0,transform:"translateY(10px)"}),ze("300ms ease-out",ne({opacity:1,transform:"translateY(0)"}))])]),Qe("slideIn",[Re(":enter",[ne({transform:"translateX(-20px)",opacity:0}),ze("300ms ease-out",ne({transform:"translateX(0)",opacity:1}))])]),Qe("tagAnimation",[Re(":enter",[ne({transform:"scale(0.8)",opacity:0}),ze("200ms ease-out",ne({transform:"scale(1)",opacity:1}))]),Re(":leave",[ze("200ms ease-in",ne({transform:"scale(0.8)",opacity:0}))])])]}})};function ql(o,e){o&1&&(r(0,"div",6),g(1,"div",7),r(2,"p"),l(3,"Generating your license key..."),a()())}function Wl(o,e){if(o&1){let t=V();r(0,"div",8)(1,"p"),l(2),a(),r(3,"button",9),h("click",function(){v(t);let i=b();return x(i.retryGeneration())}),g(4,"i",10),l(5," Retry "),a()()}if(o&2){let t=b();d(2),T(t.error)}}function $l(o,e){if(o&1){let t=V();r(0,"div",11)(1,"h1"),l(2,"Thank you for your subscription!"),a(),r(3,"div",12)(4,"h2"),l(5,"Your License Details"),a(),r(6,"div",13)(7,"p")(8,"strong"),l(9,"Total Users:"),a(),l(10),a(),r(11,"p")(12,"strong"),l(13,"Remaining Users:"),a(),l(14),a(),r(15,"div",14)(16,"label"),l(17,"License Key:"),a(),r(18,"div",15)(19,"code"),l(20),a(),r(21,"button",16),h("click",function(){v(t);let i=b();return x(i.copyLicenseKey())}),g(22,"i",17),l(23),a()()()()(),r(24,"div",18)(25,"p"),l(26,"Please save your license key in a secure location. You'll need it to activate the software."),a(),r(27,"button",19),h("click",function(){v(t);let i=b();return x(i.goToDashboard())}),l(28,"Go to Dashboard"),a()()()}if(o&2){let t=b();d(10),M(" ",t.totalUsers,""),d(4),M(" ",t.remainingUsers,""),d(6),T(t.licenseKey),d(),ae("copied",t.copied),d(2),M(" ",t.copied?"Copied!":"Copy"," ")}}var ei=class o{licenseKey="";totalUsers=0;remainingUsers=0;copied=!1;loading=!0;error="";router=Pe(j);route=Pe(Ft);licenseService=Pe(_t);constructor(){}ngOnInit(){this.route.queryParams.subscribe(e=>{let t=e.userCount;t?this.generateLicense(parseInt(t)):(this.error="User count not provided",this.loading=!1)})}generateLicense(e){this.loading=!0,this.error="";let t=localStorage.getItem("userEmail"),n=localStorage.getItem("subscriptionType")||"standard";if(!t){this.error="User email not found. Please try logging in again.",this.loading=!1;return}if(isNaN(e)||e<=0){this.error="Invalid user count. Please select a valid number of users.",this.loading=!1;return}this.licenseService.generateLicense(e,t,n).subscribe({next:i=>{if(!i.licenseKey){this.error="Invalid license response from server",this.loading=!1;return}this.licenseKey=i.licenseKey,this.totalUsers=i.totalUsers,this.remainingUsers=i.totalUsers-i.usedUsers,localStorage.setItem("licenseKey",i.licenseKey),localStorage.setItem("licenseCreatedAt",i.createdAt.toString()),this.loading=!1},error:i=>{console.error("License generation error:",i),i.status===400?this.error="Invalid request. Please check your input and try again.":i.status===401?this.error="Authentication required. Please log in again.":i.status===403?this.error="You do not have permission to generate a license.":this.error="Failed to generate license key. Please try again.",this.loading=!1}})}copyLicenseKey(){this.licenseKey&&(navigator.clipboard.writeText(this.licenseKey),this.copied=!0,setTimeout(()=>{this.copied=!1},2e3))}retryGeneration(){this.totalUsers>0&&this.generateLicense(this.totalUsers)}goToDashboard(){this.router.navigate(["/dashboard"])}static \u0275fac=function(t){return new(t||o)};static \u0275cmp=E({type:o,selectors:[["app-subscription-confirmation"]],features:[et([{provide:_t,useFactory:()=>new _t(Pe(ut))}])],decls:6,vars:4,consts:[[1,"subscription-confirmation"],[1,"confirmation-container"],[1,"confirmation-content"],["class","loading-spinner",4,"ngIf"],["class","error-message",4,"ngIf"],["class","success-content",4,"ngIf"],[1,"loading-spinner"],[1,"spinner"],[1,"error-message"],[1,"retry-button",3,"click"],[1,"fas","fa-sync-alt"],[1,"success-content"],[1,"license-details"],[1,"license-info"],[1,"license-key-container"],[1,"key-display"],[3,"click"],[1,"fas","fa-copy"],[1,"next-steps"],[1,"primary-button",3,"click"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"div",1)(2,"div",2),_(3,ql,4,0,"div",3)(4,Wl,6,1,"div",4)(5,$l,29,6,"div",5),a()()()),t&2&&(d(),p("@fadeInUp",void 0),d(2),p("ngIf",n.loading),d(),p("ngIf",!n.loading&&n.error),d(),p("ngIf",!n.loading&&!n.error))},dependencies:[A,U,At],styles:['.subscription-confirmation[_ngcontent-%COMP%]{min-height:100vh;display:flex;justify-content:center;align-items:center;background-color:var(--bg-light);padding:2rem;transition:all .3s ease}.confirmation-container[_ngcontent-%COMP%]{width:100%;max-width:600px;background:var(--bg-white);border-radius:var(--border-radius-lg);box-shadow:0 8px 16px #00000014;overflow:hidden;transform:translateY(0);opacity:1;animation:_ngcontent-%COMP%_slideIn .5s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}.confirmation-content[_ngcontent-%COMP%]{padding:2.5rem;text-align:center}.confirmation-icon[_ngcontent-%COMP%]{width:80px;height:80px;margin:0 auto 1.5rem;background-color:var(--bg-icon-light);border-radius:var(--border-radius-full);display:flex;align-items:center;justify-content:center;animation:_ngcontent-%COMP%_scaleIn .5s ease-out .3s both}@keyframes _ngcontent-%COMP%_scaleIn{0%{transform:scale(.8);opacity:0}to{transform:scale(1);opacity:1}}.confirmation-title[_ngcontent-%COMP%]{color:var(--text-primary);font-size:1.75rem;font-weight:600;margin-bottom:1rem;animation:_ngcontent-%COMP%_fadeIn .5s ease-out .4s both}.confirmation-message[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:1.1rem;line-height:1.6;margin-bottom:2rem;animation:_ngcontent-%COMP%_fadeIn .5s ease-out .5s both}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:1rem;justify-content:center;animation:_ngcontent-%COMP%_fadeIn .5s ease-out .6s both}.primary-button[_ngcontent-%COMP%]{background-color:var(--primary-color);color:#fff;border:none;padding:.875rem 1.5rem;border-radius:var(--border-radius-md);font-weight:500;cursor:pointer;transition:all .2s ease}.primary-button[_ngcontent-%COMP%]:hover{background-color:var(--primary-color-80);transform:translateY(-1px)}.primary-button[_ngcontent-%COMP%]:active{transform:translateY(0)}.secondary-button[_ngcontent-%COMP%]{background-color:transparent;color:var(--text-secondary);border:1px solid var(--border-color);padding:.875rem 1.5rem;border-radius:var(--border-radius-md);font-weight:500;cursor:pointer;transition:all .2s ease}.secondary-button[_ngcontent-%COMP%]:hover{background-color:var(--bg-light);color:var(--text-primary)}.loading-spinner[_ngcontent-%COMP%]{text-align:center;padding:2.5rem}.spinner[_ngcontent-%COMP%]{display:inline-block;width:50px;height:50px;border:3px solid rgba(25,154,142,.1);border-radius:50%;border-top-color:var(--primary-color);animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}.error-message[_ngcontent-%COMP%]{text-align:center;color:#dc3545;padding:1rem;animation:_ngcontent-%COMP%_shake .5s ease-in-out}@keyframes _ngcontent-%COMP%_shake{0%,to{transform:translate(0)}20%,60%{transform:translate(-5px)}40%,80%{transform:translate(5px)}}.copy-button[_ngcontent-%COMP%]{background-color:var(--primary-color);color:#fff;border:none;padding:.5rem 1rem;border-radius:var(--border-radius-md);cursor:pointer;transition:all .2s ease;display:inline-flex;align-items:center;gap:.5rem;font-weight:500}.copy-button[_ngcontent-%COMP%]:hover{background-color:var(--primary-color-80);transform:translateY(-1px)}.copy-button[_ngcontent-%COMP%]:active{transform:translateY(0)}.copy-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.copy-button.copied[_ngcontent-%COMP%]{background-color:var(--primary-color-80);animation:_ngcontent-%COMP%_pulse .3s ease-in-out}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}.key-display[_ngcontent-%COMP%]{background:linear-gradient(145deg,var(--bg-white) 0%,rgba(249,250,251,.8) 100%);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:var(--border-radius-lg);padding:2rem;margin:2rem 0;box-shadow:0 10px 25px -5px #0000000d,0 8px 10px -6px #00000005;border:1px solid rgba(255,255,255,.8);transition:all .4s cubic-bezier(.4,0,.2,1);animation:_ngcontent-%COMP%_cardFloat .6s cubic-bezier(.4,0,.2,1) forwards;position:relative;overflow:hidden}.key-display[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;width:100%;height:4px;background:linear-gradient(90deg,var(--primary-color) 0%,var(--primary-color-80) 50%,var(--primary-color) 100%);animation:_ngcontent-%COMP%_shimmer 2s infinite linear;background-size:200% 100%}.key-display[_ngcontent-%COMP%]:after{content:"";position:absolute;inset:0;z-index:-1;background:radial-gradient(circle at top right,rgba(25,154,142,.1) 0%,transparent 70%);opacity:0;transition:opacity .4s ease}.key-display[_ngcontent-%COMP%]:hover{transform:translateY(-4px) scale(1.01);box-shadow:0 20px 35px -10px #00000014,0 10px 20px -8px #00000008;border-color:#199a8e33}.key-display[_ngcontent-%COMP%]:hover:after{opacity:1}@keyframes _ngcontent-%COMP%_cardFloat{0%{opacity:0;transform:translateY(40px) scale(.98)}60%{transform:translateY(-10px) scale(1.01)}to{opacity:1;transform:translateY(0) scale(1)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:200% 0}to{background-position:-200% 0}}.key-display[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background:#f9fafb99;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);padding:1.5rem;border-radius:var(--border-radius-md);margin:1rem 0;overflow-x:auto;position:relative;font-family:Fira Code,monospace;font-size:.95rem;line-height:1.6;border:1px solid rgba(25,154,142,.1);box-shadow:inset 0 1px 2px #0000000d;animation:_ngcontent-%COMP%_fadeScale .5s cubic-bezier(.4,0,.2,1) .2s both}.key-display[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(45deg,transparent 0%,rgba(255,255,255,.4) 50%,transparent 100%);transform:translate(-100%);animation:_ngcontent-%COMP%_shine 3s infinite}@keyframes _ngcontent-%COMP%_shine{0%{transform:translate(-100%)}20%{transform:translate(100%)}to{transform:translate(100%)}}.key-display[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--text-primary);margin:0 0 1.5rem;font-size:1.5rem;font-weight:700;display:flex;align-items:center;gap:.75rem;animation:_ngcontent-%COMP%_slideUp .5s cubic-bezier(.4,0,.2,1) both;letter-spacing:-.02em}.key-display[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--primary-color);font-size:1.5rem;animation:_ngcontent-%COMP%_iconPop .5s cubic-bezier(.4,0,.2,1) .2s both}@keyframes _ngcontent-%COMP%_iconPop{0%{transform:scale(0);opacity:0}60%{transform:scale(1.2)}to{transform:scale(1);opacity:1}}.key-display[_ngcontent-%COMP%]   .copy-wrapper[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-top:1.5rem;animation:_ngcontent-%COMP%_slideUp .5s cubic-bezier(.4,0,.2,1) .3s both}.key-display[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-size:.95rem;color:var(--text-secondary);margin-bottom:.75rem;font-weight:500;animation:_ngcontent-%COMP%_slideUp .5s cubic-bezier(.4,0,.2,1) .1s both;letter-spacing:-.01em}.next-steps[_ngcontent-%COMP%]{margin-top:2.5rem;padding:2rem;background:#f9fafbcc;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:var(--border-radius-lg);border:1px solid rgba(25,154,142,.1);animation:_ngcontent-%COMP%_fadeScale .5s cubic-bezier(.4,0,.2,1) .4s both;position:relative;overflow:hidden}.next-steps[_ngcontent-%COMP%]:before{content:"";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle at center,rgba(25,154,142,.05) 0%,transparent 50%);animation:_ngcontent-%COMP%_rotate 15s linear infinite}@keyframes _ngcontent-%COMP%_rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.next-steps[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--text-primary);margin:0 0 1.5rem;font-size:1.4rem;font-weight:700;position:relative;letter-spacing:-.02em}.next-steps[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0;position:relative}.next-steps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:1rem 0;padding-left:2rem;position:relative;color:var(--text-secondary);font-size:1.05rem;line-height:1.6;animation:_ngcontent-%COMP%_slideInRight .5s cubic-bezier(.4,0,.2,1) calc(.5s + var(--li-index, 0) * .1s) both}.next-steps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:"";position:absolute;left:0;top:50%;transform:translateY(-50%);width:24px;height:24px;background:var(--primary-color);border-radius:50%;opacity:.1;transition:all .3s ease}.next-steps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:after{content:"\\2192";position:absolute;left:6px;top:50%;transform:translateY(-50%);color:var(--primary-color);font-weight:700;font-size:.9rem}.next-steps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover:before{transform:translateY(-50%) scale(1.2);opacity:.2}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_fadeScale{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}@keyframes _ngcontent-%COMP%_slideUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@media (max-width: 768px){.key-display[_ngcontent-%COMP%]{padding:1.5rem;margin:1.5rem 0}.key-display[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{font-size:.85rem;padding:1.25rem}.key-display[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.3rem}.next-steps[_ngcontent-%COMP%]{padding:1.5rem;margin-top:2rem}.next-steps[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.25rem}.next-steps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{font-size:1rem;padding-left:1.75rem}}@media (max-width: 480px){.key-display[_ngcontent-%COMP%]{padding:1.25rem;margin:1.25rem 0}.key-display[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{font-size:.8rem;padding:1rem}.key-display[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem}.next-steps[_ngcontent-%COMP%]{padding:1.25rem}.next-steps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{font-size:.95rem;padding-left:1.5rem}}@media (prefers-color-scheme: dark){.key-display[_ngcontent-%COMP%]{background:linear-gradient(145deg,#111827cc,#11182799);border-color:#ffffff1a}.key-display[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%], .next-steps[_ngcontent-%COMP%]{background:#11182780;border-color:#ffffff1a}.key-display[_ngcontent-%COMP%]:after{background:radial-gradient(circle at top right,rgba(25,154,142,.2) 0%,transparent 70%)}}.license-details[_ngcontent-%COMP%]{margin:2rem 0;padding:1.5rem;background:#f8f9fa;border-radius:8px}.license-info[_ngcontent-%COMP%]{margin-top:1rem}.license-key-container[_ngcontent-%COMP%]{margin-top:1.5rem}code[_ngcontent-%COMP%]{background:#e9ecef;padding:.5rem 1rem;border-radius:4px;font-family:monospace;font-size:1.1rem;letter-spacing:1px}.key-display[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background-color:#199a8e;color:#fff;border:none;padding:.5rem 1rem;border-radius:4px;cursor:pointer;transition:background-color .3s}.key-display[_ngcontent-%COMP%]   button.copied[_ngcontent-%COMP%]{background-color:#199a8e}.key-display[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:var(--primary-color-80)}h1[_ngcontent-%COMP%]{color:#343a40;margin-bottom:1.5rem}h2[_ngcontent-%COMP%]{color:#495057;margin-bottom:1rem}p[_ngcontent-%COMP%]{color:#6c757d;line-height:1.5}strong[_ngcontent-%COMP%]{color:#495057}'],data:{animation:[Qe("fadeInUp",[Re(":enter",[ne({opacity:0,transform:"translateY(20px)"}),ze("400ms ease-out",ne({opacity:1,transform:"translateY(0)"}))])])]}})};var Ke=class o{constructor(e,t,n,i,s,c){this.http=e;this.firestore=t;this.storage=n;this.authService=i;this.ngZone=s;this.db=c}medicalRecordsCollection="medicalRecords";doctorPatientCollection="doctorPatient";getAllPatients(){return this.ngZone.runOutsideAngular(()=>{let e=Q(this.firestore,"users"),t=fe(e,se("role","==","patient"),xi("lastName"));return Rt(t,{idField:"uid"}).pipe(we(n=>this.ngZone.run(()=>n)),re(n=>this.ngZone.run(()=>(console.error("Error fetching patients:",n),ce(()=>n)))))})}getPatient(e){return this.ngZone.runOutsideAngular(()=>{let t=me(this.firestore,`users/${e}`);return ie(qe(t)).pipe(we(n=>this.ngZone.run(()=>n.exists()&&n.data().role==="patient"?B({uid:e},n.data()):null)),re(n=>this.ngZone.run(()=>(console.error("Error fetching patient:",n),lt(null)))))})}removePatient(e,t){return this.ngZone.runOutsideAngular(()=>{console.log(`Removing doctor-patient relationship: Doctor ID ${t}, Patient ID ${e}`);let n=`${t}_${e}`,i=me(this.firestore,`${this.doctorPatientCollection}/${n}`);return ie(qe(i)).pipe(ct(s=>this.ngZone.run(()=>{if(s.exists())return console.log(`Found doctor-patient relationship document with ID: ${n}`),this.ngZone.runOutsideAngular(()=>ie(Ct(i)).pipe(we(()=>{console.log(`Successfully removed doctor-patient relationship with ID: ${n}`)})));{console.log(`Relationship document not found with ID: ${n}, trying with query`);let c=fe(Q(this.firestore,this.doctorPatientCollection),se("doctorId","==",t),se("patientId","==",e));return this.ngZone.runOutsideAngular(()=>ie(xe(c)).pipe(ct(m=>this.ngZone.run(()=>{if(m.empty)return console.log("No relationship found to remove"),ce(()=>new Error("No relationship found"));console.log(`Found ${m.size} relationships to delete`);let u=m.docs.map(f=>this.ngZone.runOutsideAngular(()=>Ct(f.ref)));return ie(Promise.all(u)).pipe(we(()=>{console.log("Successfully removed all doctor-patient relationships")}))}))))}})),re(s=>this.ngZone.run(()=>(console.error("Error removing patient from doctor:",s),ce(()=>s)))))})}findPatientByEmail(e){return console.log("Finding patient by email in Firebase:",e),new Zt(t=>{let n=e.toLowerCase();this.searchPatientWithRole(n,"patient").then(i=>{if(i){console.log("Patient found with role=patient:",i),t.next(i),t.complete();return}return this.searchPatientWithRole(n,"PATIENT")}).then(i=>{if(i){console.log("Patient found with role=PATIENT:",i),t.next(i),t.complete();return}return this.searchPatientWithoutRoleFilter(n)}).then(i=>{if(i){console.log("Patient found without role filter:",i),t.next(i),t.complete();return}console.log("No patient found with email after all attempts:",e),t.next(null),t.complete()}).catch(i=>{console.error("Error searching for patient:",i),t.error(i)})})}searchPatientWithRole(e,t){return this.ngZone.runOutsideAngular(()=>{console.log(`Searching for patient with email=${e} and role=${t}`);let n=Q(this.firestore,"users"),i=fe(n,se("email","==",e),se("role","==",t));return xe(i).then(s=>this.ngZone.run(()=>{if(s.empty)return console.log(`No patient found with email=${e} and role=${t}`),null;let c=s.docs[0];return console.log(`Found patient with id=${c.id} for email=${e} and role=${t}`),B({uid:c.id},c.data())}))})}searchPatientWithoutRoleFilter(e){return this.ngZone.runOutsideAngular(()=>{console.log(`Searching for patient with email=${e} without role filter`);let t=Q(this.firestore,"users"),n=fe(t,se("email","==",e));return xe(n).then(i=>this.ngZone.run(()=>{if(i.empty)return console.log(`No user found with email=${e}, trying with original case email`),this.searchWithOriginalCaseEmail(e);let s=i.docs.filter(m=>{let u=m.data();return u.role==="patient"||u.role==="PATIENT"||u.patientId||u.hasOwnProperty("medicalRecords")});if(s.length===0){let m=i.docs[0];return console.log(`No obvious patient found, using first user with id=${m.id}`),B({uid:m.id},m.data())}let c=s[0];return console.log(`Found potential patient with id=${c.id} for email=${e}`),B({uid:c.id},c.data())}))})}searchWithOriginalCaseEmail(e){return this.ngZone.runOutsideAngular(()=>{let t=Q(this.firestore,"users");return xe(t).then(n=>this.ngZone.run(()=>{if(n.empty)return console.log("No users found in database"),null;for(let i of n.docs){let s=i.data(),c=s.email||s.emailAddress||s.userEmail||"";if(c.toLowerCase()===e)return console.log(`Found user with case-insensitive email match: ${c}`),B({uid:i.id},s)}return console.log("No users found with matching email after case-insensitive search"),null}))})}addPatientToDoctor(e,t){return console.log(`Creating doctor-patient relationship: Doctor ID ${t}, Patient ID ${e}`),this.ngZone.run(()=>{let n=fe(Q(this.firestore,this.doctorPatientCollection),se("doctorId","==",t),se("patientId","==",e));return ie(xe(n)).pipe(ct(i=>{if(!i.empty)return console.log("Relationship already exists in Firebase"),lt(void 0);let s=`${t}_${e}`,c=me(this.firestore,this.doctorPatientCollection,s);return ie(ht(c,{doctorId:t,patientId:e,assignedDate:new Date().toISOString()})).pipe(we(()=>{console.log("Successfully created doctor-patient relationship in Firebase")}))}),re(i=>(console.error("Error creating doctor-patient relationship:",i),ce(()=>i))))})}getPatientCount(e){return this.ngZone.runOutsideAngular(()=>{let t=fe(Q(this.firestore,this.doctorPatientCollection),se("doctorId","==",e));return ie(xe(t)).pipe(we(n=>this.ngZone.run(()=>n.size)),re(n=>this.ngZone.run(()=>(console.error("Error getting patient count:",n),lt(0)))))})}getDoctorPatients(e){return console.log("Fetching patients for doctor:",e),this.ngZone.run(()=>this.checkAllDoctorPatientFormats(e))}checkAllDoctorPatientFormats(e){let t=fe(Q(this.firestore,this.doctorPatientCollection),se("doctorId","==",e)),n=fe(Q(this.firestore,this.doctorPatientCollection),se("doctor_id","==",e));return ie(xe(t)).pipe(ct(i=>{let s=this.extractPatientIds(i);return s.length>0?(console.log("Found patients with doctorId field format:",s),this.fetchPatientsByIds(s)):ie(xe(n)).pipe(ct(c=>{let m=this.extractPatientIds(c,!0);if(m.length>0)return console.log("Found patients with doctor_id field format:",m),this.fetchPatientsByIds(m);console.log("No patients found with any field format. Trying alternate collection names...");let u=fe(Q(this.firestore,"doctorPatients"),se("doctorId","==",e));return ie(xe(u)).pipe(ct(f=>{let C=this.extractPatientIds(f);return C.length>0?(console.log("Found patients in doctorPatients collection:",C),this.fetchPatientsByIds(C)):(console.log("No patients found in any collection format. Checking both ID formats together..."),this.checkAllDoctorPatientRelationships(e))}))}))}),re(i=>(console.error("Error fetching doctor patients:",i),lt([]))))}extractPatientIds(e,t=!1){if(e.empty)return[];let n=e.docs.map(i=>{let s=i.data();return t?s.patient_id||s.patientId:s.patientId||s.patient_id}).filter(i=>i!=null);return console.log("Extracted patient IDs:",n),n}checkAllDoctorPatientRelationships(e){return ie(xe(Q(this.firestore,this.doctorPatientCollection))).pipe(ct(t=>{if(t.empty)return console.log("No doctor-patient relationships found at all"),lt([]);let n=t.docs.map(s=>s.data());console.log("All doctor-patient relationships:",n);let i=n.filter(s=>s.doctorId===e||s.doctor_id===e||s.doctor&&s.doctor.id===e||s.doctor===e).map(s=>s.patientId||s.patient_id||s.patient&&s.patient.id||s.patient).filter(s=>s!=null);return i.length>0?(console.log("Found patient IDs through relationship inspection:",i),this.fetchPatientsByIds(i)):(console.log("Could not find any patients linked to this doctor after checking all formats"),lt([]))}),re(t=>(console.error("Error checking all doctor-patient relationships:",t),lt([]))))}fetchPatientsByIds(e){return this.ngZone.run(()=>{let t=e.map(n=>{let i=me(this.firestore,"users",n);return qe(i)});return ie(Promise.all(t)).pipe(we(n=>{let i=n.filter(s=>s.exists()).map(s=>B({uid:s.id},s.data()));return console.log("Fetched patients from Firebase:",i),i}))})}getMedicalRecords(e){return this.ngZone.runOutsideAngular(()=>{let t=Q(this.firestore,this.medicalRecordsCollection),n=fe(t,se("patientId","==",e),xi("date","desc"));return Rt(n,{idField:"id"}).pipe(we(i=>this.ngZone.run(()=>i.map(s=>Ce(B({},s),{date:s.date.toDate(),nextVisit:s.nextVisit?s.nextVisit.toDate():void 0})))),re(i=>this.ngZone.run(()=>(console.error("Error fetching medical records:",i),ce(()=>i)))))})}getMedicalRecord(e){return this.ngZone.runOutsideAngular(()=>{let t=me(this.firestore,`${this.medicalRecordsCollection}/${e}`);return ie(qe(t)).pipe(we(n=>this.ngZone.run(()=>{if(n.exists()){let i=n.data();return Ce(B({id:n.id},i),{date:i.date.toDate(),nextVisit:i.nextVisit?i.nextVisit.toDate():void 0})}else throw new Error("Medical record not found")})),re(n=>this.ngZone.run(()=>(console.error("Error fetching medical record:",n),ce(()=>n)))))})}addMedicalRecord(e){return this.ngZone.runOutsideAngular(()=>{let t=Q(this.firestore,this.medicalRecordsCollection);return ie(it(t,e)).pipe(we(n=>this.ngZone.run(()=>n.id)),re(n=>this.ngZone.run(()=>(console.error("Error adding medical record:",n),ce(()=>n)))))})}updateMedicalRecord(e,t){return this.ngZone.runOutsideAngular(()=>{let n=me(this.firestore,`${this.medicalRecordsCollection}/${e}`);return ie(qo(n,t)).pipe(re(i=>this.ngZone.run(()=>(console.error("Error updating medical record:",i),ce(()=>i)))))})}static \u0275fac=function(t){return new(t||o)(de(ut),de(Be),de(_n),de(Vt),de(je),de(He))};static \u0275prov=be({token:o,factory:o.\u0275fac,providedIn:"root"})};var Ht=class o{constructor(e,t){this.patientService=e;this.authService=t;this.initializePatientCount()}patientCount=new ro(0);getPatientCount(){return this.patientCount.asObservable()}initializePatientCount(){let e=this.authService.getUserInfo();e&&e.patientCount!==void 0?this.patientCount.next(e.patientCount):this.refreshPatientCount()}refreshPatientCount(){}updatePatientCount(e){this.patientCount.next(e);let t=this.authService.getUserInfo();t&&(t.patientCount=e,this.authService.saveUserInfo(t))}static \u0275fac=function(t){return new(t||o)(de(Ke),de(le))};static \u0275prov=be({token:o,factory:o.\u0275fac,providedIn:"root"})};function Hl(o,e){if(o&1){let t=V();r(0,"div",38)(1,"div",39),l(2),a(),r(3,"div",40),l(4),a(),r(5,"div",40),l(6),a(),r(7,"div",40),l(8),a(),r(9,"div",40)(10,"div",41)(11,"a",42),h("click",function(){let i=v(t).$implicit,s=b(2);return x(s.showDeleteConfirmation(i))}),g(12,"i",43),l(13," Remove "),a()()()()}if(o&2){let t=e.$implicit,n=e.$index;d(2),T(n+1),d(2),T(t.firstname),d(2),T(t.lastname),d(2),T(t.email||"No email available")}}function Zl(o,e){if(o&1&&(r(0,"div",33)(1,"div",34)(2,"div",35),l(3,"#"),a(),r(4,"div",36),l(5,"First Name"),a(),r(6,"div",36),l(7,"Last Name"),a(),r(8,"div",36),l(9,"Email"),a(),r(10,"div",36),l(11,"Actions"),a()(),r(12,"div",37),po(13,Hl,14,4,"div",38,mo),a()()),o&2){let t=b();d(13),go(t.filteredPatients())}}function Kl(o,e){if(o&1){let t=V();r(0,"div",44)(1,"div",45),g(2,"i",46),a(),r(3,"h3"),l(4,"No patients yet"),a(),r(5,"p"),l(6,"You haven't added any patients to your list yet. As a new doctor, you start with an empty patient list."),a(),r(7,"button",23),h("click",function(){v(t);let i=b();return x(i.openAddPatientModal())}),g(8,"i",24),l(9," Add your first patient "),a()()}}function Ql(o,e){o&1&&(r(0,"div",44)(1,"div",45),g(2,"i",47),a(),r(3,"h3"),l(4,"Loading..."),a(),r(5,"p"),l(6,"Getting your patients list"),a()())}function Gl(o,e){if(o&1){let t=V();r(0,"div",44)(1,"div",45),g(2,"i",48),a(),r(3,"h3"),l(4,"No matches found"),a(),r(5,"p"),l(6),a(),r(7,"button",49),h("click",function(){v(t);let i=b();return x(i.clearSearch())}),l(8," Clear search "),a()()}if(o&2){let t=b();d(6),M('No patients match your search term "',t.searchQuery,'"')}}function Jl(o,e){if(o&1&&(r(0,"div",63),g(1,"i",64),l(2),a()),o&2){let t=b(2);d(2),M(" ",t.successMessage," ")}}function Xl(o,e){if(o&1&&(r(0,"div",65),g(1,"i",66),l(2),a()),o&2){let t=b(2);d(2),M(" ",t.errorMessage," ")}}function ec(o,e){o&1&&(r(0,"span"),g(1,"i",47),l(2," Adding... "),a())}function tc(o,e){o&1&&(r(0,"span"),l(1,"Add Patient"),a())}function nc(o,e){if(o&1){let t=V();r(0,"div",50),h("click",function(){v(t);let i=b();return x(i.closeAddPatientModal())}),r(1,"div",51),h("click",function(i){return v(t),x(i.stopPropagation())}),r(2,"div",52)(3,"h2"),l(4,"Add New Patient"),a()(),r(5,"div",53)(6,"div",54)(7,"label",55),l(8,"Patient Email"),a(),r(9,"input",56),L("ngModelChange",function(i){v(t);let s=b();return R(s.newPatientEmail,i)||(s.newPatientEmail=i),x(i)}),a()(),_(10,Jl,3,1,"div",57)(11,Xl,3,1,"div",58),a(),r(12,"div",59)(13,"button",60),h("click",function(){v(t);let i=b();return x(i.closeAddPatientModal())}),l(14,"Cancel"),a(),r(15,"button",61),h("click",function(){v(t);let i=b();return x(i.addPatient())}),_(16,ec,3,0,"span",62)(17,tc,2,0,"span",62),a()()()()}if(o&2){let t=b();ae("show",t.showAddPatientModal),d(9),z("ngModel",t.newPatientEmail),p("disabled",t.addingPatient),d(),p("ngIf",t.successMessage),d(),p("ngIf",t.errorMessage),d(2),p("disabled",t.addingPatient),d(2),p("disabled",t.addingPatient||!t.newPatientEmail),d(),p("ngIf",t.addingPatient),d(),p("ngIf",!t.addingPatient)}}function ic(o,e){if(o&1){let t=V();r(0,"div",50),h("click",function(){v(t);let i=b();return x(i.cancelDelete())}),r(1,"div",51),h("click",function(i){return v(t),x(i.stopPropagation())}),r(2,"div",52)(3,"h2"),l(4,"Confirm Patient Removal"),a(),g(5,"i",67),a(),r(6,"div",53)(7,"p"),l(8,"Are you sure you want to remove this patient from your list?"),a(),r(9,"p",68),l(10,"This action cannot be undone."),a()(),r(11,"div",59)(12,"button",69),h("click",function(){v(t);let i=b();return x(i.cancelDelete())}),l(13,"Cancel"),a(),r(14,"button",70),h("click",function(){v(t);let i=b();return x(i.confirmDelete())}),l(15,"Remove Patient"),a()()()()}if(o&2){let t=b();ae("show",t.showDeleteModal)}}var ni=class o{constructor(e,t,n,i){this.router=e;this.patientService=t;this.patientCountService=n;this.authService=i;this.checkScreenSize()}searchQuery="";isLoading=!0;showAddPatientModal=!1;showDeleteModal=!1;selectedPatient=null;newPatientEmail="";addingPatient=!1;successMessage="";errorMessage="";sidebarCollapsed=!1;isMobileView=!1;doctorName="";patientSubscription;db=Pe(He);myPatients=hi([]);filteredPatients=hi([]);onResize(){this.checkScreenSize()}checkScreenSize(){this.isMobileView=window.innerWidth<=768,this.sidebarCollapsed=this.isMobileView}ngOnInit(){this.loadDoctorPatient()}ngOnDestroy(){this.patientSubscription&&this.patientSubscription.unsubscribe()}toggleSidebar(){this.sidebarCollapsed=!this.sidebarCollapsed}filterPatients(){if(!this.searchQuery){this.filteredPatients.set(this.myPatients());return}let e=this.searchQuery.toLowerCase(),t=this.myPatients().filter(n=>{let i=`${n.firstname} ${n.lastname}`.toLowerCase(),s=(n.email||"").toLowerCase();return i.includes(e)||s.includes(e)});this.filteredPatients.set(t)}clearSearch(){this.searchQuery="",this.filterPatients()}openAddPatientModal(){this.showAddPatientModal=!0,this.newPatientEmail="",this.successMessage="",this.errorMessage=""}closeAddPatientModal(){this.showAddPatientModal=!1,this.newPatientEmail="",this.successMessage="",this.errorMessage=""}showDeleteConfirmation(e){this.selectedPatient=e,this.showDeleteModal=!0}cancelDelete(){this.selectedPatient=null,this.showDeleteModal=!1}confirmDelete(){if(this.selectedPatient&&this.selectedPatient.id){let e=this.selectedPatient.id,t=this.db.current_doctor()?.id||"",n=this.db.doctorPatientTable().filter(i=>i.doctor_id===t&&i.patient_id===e);n.length>0&&(this.db.removeDoctorPatient(n[0].id),this.patientService.removePatient(e,t).subscribe({next:()=>{console.log("Patient successfully removed from Firebase")},error:i=>{console.error("Error removing patient from Firebase:",i)}}),this.cleanupPatientAppointments(e),this.loadDoctorPatient()),this.showDeleteModal=!1,this.selectedPatient=null}}cleanupPatientAppointments(e){this.db.appointmentTable().filter(n=>n.patient_id===e).forEach(n=>{this.db.deleteAppointment(n.id)})}logout(){this.router.navigate(["/login"])}loadDoctorPatient(){let e=this.authService.getCurrentUser();if(e&&e.uid){let t=e.uid;console.log("Loading patients for authenticated doctor ID:",t),this.patientService.getDoctorPatients(t).subscribe({next:n=>{if(console.log("Patients loaded from Firebase:",n),n.length===0){this.myPatients.set([]),this.filteredPatients.set([]),this.patientCountService.updatePatientCount(0),this.isLoading=!1;return}let i=n.map(s=>({id:s.uid||"",email:s.email||"",firstname:this.extractFirstName(s),lastname:this.extractLastName(s),password:"",role:"PATIENT"}));this.myPatients.set(i),this.filteredPatients.set(i),this.patientCountService.updatePatientCount(i.length),this.isLoading=!1},error:n=>{console.error("Error loading patients from Firebase:",n),this.isLoading=!1,this.loadFromLocalStorage(t)}})}else console.error("No current doctor found"),this.myPatients.set([]),this.filteredPatients.set([]),this.patientCountService.updatePatientCount(0),this.isLoading=!1,this.loadFromLocalStorage()}loadFromLocalStorage(e){let t=this.db.current_doctor(),n=e||t?.id;if(!n){console.error("No current doctor found"),this.myPatients.set([]),this.filteredPatients.set([]),this.patientCountService.updatePatientCount(0);return}let i=this.db.doctorPatientTable().filter(c=>c.doctor_id===n);if(i.length===0){this.myPatients.set([]),this.filteredPatients.set([]),this.patientCountService.updatePatientCount(0);return}let s=[];for(let c=0;c<i.length;c++)for(let m=0;m<this.db.userTable().length;m++)i[c].patient_id===this.db.userTable()[m].id&&s.push(this.db.userTable()[m]);this.myPatients.set(s),this.filteredPatients.set(s),this.patientCountService.updatePatientCount(s.length)}addPatient(){this.addingPatient=!0,this.errorMessage="",this.successMessage="";let e=this.authService.getCurrentUser();if(!e||!e.uid){this.errorMessage="Doctor ID not found. Please log in again.",this.addingPatient=!1;return}let t=e.uid;console.log(`Doctor ${t} is adding patient with email: ${this.newPatientEmail}`);let n=this.db.userTable().find(i=>i.email==this.newPatientEmail)||null;if(n&&n.id){console.log(`Patient found in local database with ID: ${n.id}`);let i={id:this.db.generateId(),doctor_id:t,patient_id:n.id};this.db.addDoctorPatient(i),this.patientService.addPatientToDoctor(n.id,t).subscribe({next:()=>{console.log("Doctor-patient relationship successfully added to Firebase"),this.successMessage="Patient added successfully to your patient list",this.updateLocalPatientsList(n)},error:s=>{console.error("Error adding doctor-patient relationship to Firebase:",s),this.successMessage="Patient added locally, but could not update Firebase",this.updateLocalPatientsList(n)},complete:()=>{setTimeout(()=>{this.loadDoctorPatient(),setTimeout(()=>{this.closeAddPatientModal()},1e3)},500)}}),this.addingPatient=!1;return}this.continueAddPatientWithFirebase(t)}continueAddPatientWithFirebase(e){console.log("Patient not found in local database, checking Firebase..."),this.patientService.findPatientByEmail(this.newPatientEmail).pipe(ao(()=>{this.addingPatient=!1})).subscribe({next:t=>{if(t&&t.uid){console.log("Patient found in Firebase:",t);let n={id:t.uid,email:t.email||this.newPatientEmail,firstname:this.extractFirstName(t),lastname:this.extractLastName(t),password:"",role:"PATIENT"};this.db.userTable.update(s=>[...s,n]),this.db.storage.setItem("USER_TABLE",this.db.userTable());let i={id:this.db.generateId(),doctor_id:e,patient_id:n.id};this.db.addDoctorPatient(i),this.updateLocalPatientsList(n),this.patientService.addPatientToDoctor(n.id,e).subscribe({next:()=>{console.log("Doctor-patient relationship successfully added to Firebase"),this.successMessage="Patient imported from Firebase and added successfully"},error:s=>{console.error("Error adding doctor-patient relationship to Firebase:",s),this.successMessage="Patient imported and added locally, but Firebase update failed"},complete:()=>{setTimeout(()=>{this.loadDoctorPatient(),setTimeout(()=>{this.closeAddPatientModal()},1e3)},500)}})}else console.log("Patient not found in Firebase"),this.errorMessage="Patient with this email does not exist in the system",setTimeout(()=>{this.errorMessage=""},5e3)},error:t=>{console.error("Error searching for patient in Firebase:",t),this.errorMessage="Error searching for patient. Please try again.",setTimeout(()=>{this.errorMessage=""},5e3)}})}updateLocalPatientsList(e){this.myPatients().find(n=>n.id===e.id)||(this.myPatients.update(n=>[...n,e]),this.filteredPatients.update(n=>[...n,e]),this.patientCountService.updatePatientCount(this.myPatients().length))}extractFirstName(e){return e.firstName||e.first_name||e.firstname||(e.displayName?e.displayName.split(" ")[0]:"")||(e.fullName?e.fullName.split(" ")[0]:"")||(e.name?e.name.split(" ")[0]:"")||""}extractLastName(e){return e.lastName||e.last_name||e.lastname||(e.displayName&&e.displayName.split(" ").length>1?e.displayName.split(" ").slice(1).join(" "):"")||(e.fullName&&e.fullName.split(" ").length>1?e.fullName.split(" ").slice(1).join(" "):"")||(e.name&&e.name.split(" ").length>1?e.name.split(" ").slice(1).join(" "):"")||""}static \u0275fac=function(t){return new(t||o)(P(j),P(Ke),P(Ht),P(Vt))};static \u0275cmp=E({type:o,selectors:[["app-view-patients"]],hostBindings:function(t,n){t&1&&h("resize",function(s){return n.onResize(s)},!1,cn)},features:[et([Ke])],decls:53,vars:9,consts:[[1,"dashboard-container"],[1,"sidebar"],[1,"logo-section"],[1,"logo"],[1,"primary"],[1,"secondary"],[1,"nav-menu"],["routerLink","/dashboard","routerLinkActive","active",1,"nav-item","active"],[1,"bi","bi-grid-1x2-fill","nav-icon"],["routerLink","/doctors-patient","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-people-fill","nav-icon"],["routerLink","/doctors","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-person-badge-fill","nav-icon"],["routerLink","/appointments","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-calendar2-week-fill","nav-icon"],["routerLink","/settings","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-gear-fill","nav-icon"],[1,"logout-section"],[1,"logout-button",3,"click"],[1,"bi","bi-box-arrow-right","nav-icon"],[1,"main-content"],[1,"content-card"],[1,"card-header"],[1,"btn-primary",3,"click"],[1,"bi","bi-person-plus"],[1,"search-section"],[1,"search-bar"],[1,"bi","bi-search","search-icon"],["type","text","placeholder","Search patients by name, email...",3,"ngModelChange","input","ngModel"],[1,"patients-table-container"],["class","patients-table",4,"ngIf"],["class","empty-state",4,"ngIf"],["class","modal-overlay",3,"show","click",4,"ngIf"],[1,"patients-table"],[1,"table-header"],[1,"header-cell","number-cell"],[1,"header-cell"],[1,"table-body"],[1,"table-row"],[1,"table-cell","number-cell"],[1,"table-cell"],[1,"action-links"],[1,"action-link","danger",3,"click"],[1,"bi","bi-trash"],[1,"empty-state"],[1,"empty-icon"],[1,"bi","bi-people"],[1,"bi","bi-arrow-repeat","spinning"],[1,"bi","bi-search"],[1,"btn-outline",3,"click"],[1,"modal-overlay",3,"click"],[1,"modal-content",3,"click"],[1,"modal-header"],[1,"modal-body"],[1,"form-group"],["for","patientEmail"],["type","email","id","patientEmail","placeholder","Enter patient's email address","autocomplete","off","spellcheck","false",1,"form-control",3,"ngModelChange","ngModel","disabled"],["class","success-message",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"modal-footer"],[1,"btn-secondary",3,"click","disabled"],[1,"btn-primary",3,"click","disabled"],[4,"ngIf"],[1,"success-message"],[1,"bi","bi-check-circle-fill"],[1,"error-message"],[1,"bi","bi-exclamation-circle-fill"],[1,"bi","bi-x-lg"],[1,"warning-text"],[1,"btn-secondary",3,"click"],[1,"btn-danger",3,"click"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"span",4),l(5,"Med"),a(),r(6,"span",5),l(7,"Secura"),a()()(),r(8,"nav",6)(9,"a",7),g(10,"i",8),r(11,"span"),l(12,"Dashboard"),a()(),r(13,"a",9),g(14,"i",10),r(15,"span"),l(16,"Patients"),a()(),r(17,"a",11),g(18,"i",12),r(19,"span"),l(20,"Doctors"),a()(),r(21,"a",13),g(22,"i",14),r(23,"span"),l(24,"Appointments"),a()(),r(25,"a",15),g(26,"i",16),r(27,"span"),l(28,"Settings"),a()()(),r(29,"div",17)(30,"a",18),h("click",function(){return n.logout()}),g(31,"i",19),r(32,"span"),l(33,"Logout"),a()()()(),r(34,"div",20)(35,"div",21)(36,"div",22)(37,"h1"),l(38,"My Patients"),a(),r(39,"button",23),h("click",function(){return n.openAddPatientModal()}),g(40,"i",24),l(41," Add Patient "),a()(),r(42,"div",25)(43,"div",26),g(44,"i",27),r(45,"input",28),L("ngModelChange",function(s){return R(n.searchQuery,s)||(n.searchQuery=s),s}),h("input",function(){return n.filterPatients()}),a()()(),r(46,"div",29),_(47,Zl,15,0,"div",30)(48,Kl,10,0,"div",31)(49,Ql,7,0,"div",31)(50,Gl,9,1,"div",31),a()()(),_(51,nc,18,10,"div",32)(52,ic,16,2,"div",32),a()),t&2&&(d(),ae("collapsed",n.sidebarCollapsed),d(44),z("ngModel",n.searchQuery),d(2),p("ngIf",n.filteredPatients().length>0),d(),p("ngIf",n.myPatients().length===0&&!n.isLoading),d(),p("ngIf",n.isLoading),d(),p("ngIf",n.myPatients().length>0&&n.filteredPatients().length===0),d(),p("ngIf",n.showAddPatientModal),d(),p("ngIf",n.showDeleteModal))},dependencies:[A,U,ve,_e,ft,ee,H,Z,pe],styles:["[_nghost-%COMP%]{display:flex;width:100vw;height:100vh;overflow:hidden}.dashboard-container[_ngcontent-%COMP%]{display:flex;width:100%;height:100%;flex-direction:row}.sidebar[_ngcontent-%COMP%]{width:256px;height:100%;background:var(--bg-white);border-right:1px solid var(--border-color);display:flex;flex-direction:column;flex-shrink:0;transition:all .3s ease}.main-content[_ngcontent-%COMP%]{flex:1;padding:32px;overflow-y:auto;transition:all .3s ease}.patients-table[_ngcontent-%COMP%]{width:100%;overflow-x:auto}.table-header[_ngcontent-%COMP%]{display:grid;grid-template-columns:50px 1fr 1fr 1fr 1fr;padding:16px 32px;background:#f9fafb;border-bottom:1px solid #E5E7EB;gap:32px;align-items:center;text-align:left;min-width:600px}.table-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:50px 1fr 1fr 1fr 1fr;padding:16px 32px;border-bottom:1px solid #E5E7EB;gap:32px;align-items:center;min-width:600px}.table-cell[_ngcontent-%COMP%]{color:#374151;font-size:14px;text-align:left;padding:8px;display:flex;align-items:center;justify-content:flex-start;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.table-cell[_ngcontent-%COMP%]:nth-child(1){justify-content:center}.table-cell[_ngcontent-%COMP%]:nth-child(2){padding-left:0}.header-cell[_ngcontent-%COMP%]{color:#6b7280;font-size:13px;font-weight:600;text-transform:uppercase;letter-spacing:.05em;padding:0 8px;text-align:left;white-space:nowrap}.header-cell[_ngcontent-%COMP%]:nth-child(1){text-align:center}.header-cell[_ngcontent-%COMP%]:nth-child(2){padding-left:0}.number-cell[_ngcontent-%COMP%]{justify-content:center!important;font-weight:600;color:#6b7280;width:50px}.header-cell.number-cell[_ngcontent-%COMP%]{text-align:center;width:50px}.modal-content[_ngcontent-%COMP%]{background:#fff;border-radius:16px;width:90%;max-width:450px;box-shadow:0 20px 25px -5px #0000001a,0 10px 10px -5px #0000000a;transform:scale(.95) translateY(-30px);opacity:0;transition:transform .3s ease-out,opacity .2s ease-out;margin:0 16px}@media (max-width: 1024px){.sidebar[_ngcontent-%COMP%]{width:220px}.main-content[_ngcontent-%COMP%]{padding:24px}.card-header[_ngcontent-%COMP%], .search-section[_ngcontent-%COMP%]{padding:20px 24px}.table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]{padding:12px 24px;gap:24px;grid-template-columns:40px 1fr 1fr 1fr 1fr}}@media (max-width: 768px){.dashboard-container[_ngcontent-%COMP%]{flex-direction:column}.sidebar[_ngcontent-%COMP%]{width:100%;height:auto;border-right:none;border-bottom:1px solid var(--border-color)}.logo-section[_ngcontent-%COMP%]{justify-content:space-between}.nav-menu[_ngcontent-%COMP%]{display:flex;flex-direction:row;overflow-x:auto;padding:8px 16px}.nav-item[_ngcontent-%COMP%]{padding:8px 16px;white-space:nowrap}.logout-section[_ngcontent-%COMP%]{display:none}.main-content[_ngcontent-%COMP%]{padding:16px;height:calc(100vh - 130px)}.card-header[_ngcontent-%COMP%]{flex-direction:column;gap:16px;align-items:flex-start;padding:16px}.search-section[_ngcontent-%COMP%]{padding:16px}.action-links[_ngcontent-%COMP%]{flex-direction:column;gap:8px;align-items:flex-start}.action-link[_ngcontent-%COMP%]{width:100%}.table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]{grid-template-columns:40px 1fr 1fr 1fr 1fr}}@media (max-width: 640px){.nav-menu[_ngcontent-%COMP%]{justify-content:space-between}.nav-item[_ngcontent-%COMP%]{flex:1;justify-content:center;padding:8px}.nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:none}.nav-icon[_ngcontent-%COMP%]{margin-right:0;font-size:24px}.table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]{padding:12px 16px;gap:16px}.header-cell[_ngcontent-%COMP%], .table-cell[_ngcontent-%COMP%]{font-size:12px}.action-link[_ngcontent-%COMP%]{padding:4px 8px;font-size:12px}.action-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.modal-body[_ngcontent-%COMP%]{padding:1.5rem}.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:100px;padding:.5rem 1rem}}@media (max-width: 480px){.main-content[_ngcontent-%COMP%]{padding:12px}.card-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:18px}.btn-primary[_ngcontent-%COMP%]{padding:6px 12px;font-size:13px}.table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]{grid-template-columns:30px 1fr 1fr 1fr 1fr;gap:8px;padding:10px}.header-cell[_ngcontent-%COMP%], .table-cell[_ngcontent-%COMP%]{padding:4px;font-size:11px}.action-links[_ngcontent-%COMP%]{gap:4px}.action-link[_ngcontent-%COMP%]{padding:3px 6px;font-size:11px}.warning-icon[_ngcontent-%COMP%]{font-size:2.5rem}.warning-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem}}@media (max-width: 576px){.table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]{grid-template-columns:30px 1fr 1fr 1fr 1fr;gap:8px;padding:8px;font-size:12px}.header-cell[_ngcontent-%COMP%]{font-size:11px}.table-cell[_ngcontent-%COMP%]{font-size:12px;padding:4px}.action-links[_ngcontent-%COMP%]{flex-direction:column;gap:4px}.action-link[_ngcontent-%COMP%]{padding:4px 8px;font-size:12px}.main-content[_ngcontent-%COMP%]{padding:10px}.card-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.btn-primary[_ngcontent-%COMP%]{padding:8px 12px;font-size:12px}}.mobile-menu-toggle[_ngcontent-%COMP%]{display:none;background:none;border:none;cursor:pointer;font-size:24px;color:var(--primary-color)}@media (max-width: 768px){.mobile-menu-toggle[_ngcontent-%COMP%]{display:block}.sidebar.collapsed[_ngcontent-%COMP%]{height:70px;overflow:hidden}.sidebar.collapsed[_ngcontent-%COMP%]   .nav-menu[_ngcontent-%COMP%], .sidebar.collapsed[_ngcontent-%COMP%]   .logout-section[_ngcontent-%COMP%]{display:none}}.patients-table-container[_ngcontent-%COMP%]{width:100%;overflow-x:auto;-webkit-overflow-scrolling:touch}[_nghost-%COMP%]{--primary-color: #199A8E;--primary-color-80: rgba(25, 154, 142, .8);--text-primary: #111827;--text-secondary: #6B7280;--text-success: #166534;--bg-white: #ffffff;--bg-light: #F9FAFB;--bg-success-light: #DCFCE7;--bg-icon-light: #F0F9FF;--border-color: #E5E7EB;--shadow-sm: 0px 1px 2px rgba(0, 0, 0, .05);--border-radius-lg: 12px;--border-radius-md: 8px;--border-radius-full: 9999px}.dashboard-container[_ngcontent-%COMP%]{display:flex;width:100%;height:100%}.sidebar[_ngcontent-%COMP%]{width:256px;height:100%;background:var(--bg-white);border-right:1px solid var(--border-color);display:flex;flex-direction:column;flex-shrink:0}.logo-section[_ngcontent-%COMP%]{height:70px;border-bottom:1px solid var(--border-color);display:flex;align-items:center;padding:0 24px}.action-links[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:center;justify-content:flex-start;flex-wrap:nowrap}.action-link[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.5rem;padding:.5rem 1rem;border-radius:var(--border-radius-md);cursor:pointer;text-decoration:none;transition:background-color .2s ease;white-space:nowrap;color:var(--text-primary)}.action-link.primary[_ngcontent-%COMP%]{color:var(--primary-color)}.action-link[_ngcontent-%COMP%]:hover{background-color:var(--bg-light)}.action-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem}.logo[_ngcontent-%COMP%]{font-size:22.88px;line-height:32px}.logo[_ngcontent-%COMP%]   .primary[_ngcontent-%COMP%]{color:var(--primary-color);font-weight:700}.logo[_ngcontent-%COMP%]   .secondary[_ngcontent-%COMP%]{color:var(--primary-color-80);font-weight:400}.nav-menu[_ngcontent-%COMP%]{padding:16px 0;flex:1}.btn-primary[_ngcontent-%COMP%]{background-color:#199a8e;border:none;color:#fff;padding:8px 16px;border-radius:6px;transition:all .3s ease;display:flex;align-items:center;gap:8px}.btn-primary[_ngcontent-%COMP%]:hover{background-color:#168076;transform:translateY(-2px);box-shadow:0 4px 12px #199a8e33}.btn-primary[_ngcontent-%COMP%]:active{transform:translateY(0);box-shadow:0 2px 8px #199a8e33}.btn-primary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem;color:#fff}.nav-item[_ngcontent-%COMP%]{display:flex;align-items:center;height:44px;padding:0 24px;color:var(--text-primary);font-size:15.38px;cursor:pointer;text-decoration:none}.nav-item[_ngcontent-%COMP%]:hover{background:#f9fafb}.nav-item.active[_ngcontent-%COMP%]{background:var(--bg-icon-light);color:var(--primary-color)}.nav-icon[_ngcontent-%COMP%]{font-size:20px;margin-right:12px;color:var(--text-secondary)}.active[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{color:var(--primary-color)}.logout-section[_ngcontent-%COMP%]{padding:0 32px;margin-top:auto}.logout-button[_ngcontent-%COMP%]{display:flex;align-items:center;color:#4b5563;text-decoration:none;padding:12px 0;cursor:pointer}.main-content[_ngcontent-%COMP%]{flex:1;padding:32px;overflow-y:auto}.content-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 6px -2px #0000000d}.card-header[_ngcontent-%COMP%]{padding:24px 32px;border-bottom:1px solid #E5E7EB;display:flex;justify-content:space-between;align-items:center}.card-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#111827;margin:0}.header-actions[_ngcontent-%COMP%]{display:flex;gap:16px}.action-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:8px 16px;background:#199a8e;color:#fff;border:none;border-radius:6px;font-size:14px;font-weight:500;cursor:pointer;transition:all .2s ease}.action-button[_ngcontent-%COMP%]:hover{background:#168076}.search-section[_ngcontent-%COMP%]{padding:24px 32px;border-bottom:1px solid #E5E7EB}.search-bar[_ngcontent-%COMP%]{position:relative;width:100%}.search-icon[_ngcontent-%COMP%]{position:absolute;left:12px;top:50%;transform:translateY(-50%);color:#9ca3af;font-size:16px}input[type=text][_ngcontent-%COMP%]{width:100%;padding:8px 12px 8px 36px;border:1px solid #D1D5DB;border-radius:6px;font-size:14px;color:#374151}input[type=text][_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 3px #199a8e1a}.patients-table[_ngcontent-%COMP%]{width:100%}.table-body[_ngcontent-%COMP%]{display:flex;flex-direction:column}.table-row[_ngcontent-%COMP%]:hover{background:#f9fafb}.action-links[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:center}.action-link[_ngcontent-%COMP%]{color:#4b5563;font-size:14px;text-decoration:none;display:inline-flex;align-items:center;padding:6px 12px;border-radius:6px;transition:all .2s ease;cursor:pointer;gap:8px}.logout-section[_ngcontent-%COMP%]{height:44px;border-top:1px solid var(--border-color);display:flex;align-items:center;padding:0 24px}.logout-button[_ngcontent-%COMP%]{display:flex;align-items:center;color:var(--text-primary);font-size:15.25px;cursor:pointer;text-decoration:none;margin:0}.action-link[_ngcontent-%COMP%]:hover{background:#f3f4f6}.action-link.primary[_ngcontent-%COMP%]{color:#199a8e}.action-link.primary[_ngcontent-%COMP%]:hover{background:#199a8e1a}.action-link.delete[_ngcontent-%COMP%]{color:#dc2626;transition:all .2s ease}.action-link.delete[_ngcontent-%COMP%]:hover{background:#fef2f2;color:#b91c1c}.action-link.remove[_ngcontent-%COMP%]{color:#ef4444;transition:all .2s ease}.action-link.remove[_ngcontent-%COMP%]:hover{background:#fee2e2;color:#dc2626}.action-link.danger[_ngcontent-%COMP%]{color:#dc2626;transition:all .2s ease}.action-link.danger[_ngcontent-%COMP%]:hover{background-color:#dc2626;color:#fff;border-color:#dc2626}.action-link.danger[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{color:#fff}.no-results[_ngcontent-%COMP%]{padding:32px;text-align:center;color:#6b7280;font-size:14px}.modal-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);display:flex;justify-content:center;align-items:center;z-index:1000;opacity:0;visibility:hidden;transition:opacity .3s ease,visibility .3s ease}.modal-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}.modal-content[_ngcontent-%COMP%]{background:#fff;border-radius:16px;width:90%;max-width:450px;box-shadow:0 20px 25px -5px #0000001a,0 10px 10px -5px #0000000a;transform:scale(.95) translateY(-30px);opacity:0;transition:transform .3s ease-out,opacity .2s ease-out}.modal-overlay.show[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]{transform:scale(1) translateY(0);opacity:1}.form-group[_ngcontent-%COMP%]{margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:.5rem;font-weight:500;color:var(--text-primary);font-size:.875rem}.form-control[_ngcontent-%COMP%]{width:100%;padding:.75rem 1rem;border:1px solid var(--border-color);border-radius:8px;font-size:1rem;color:var(--text-primary);transition:all .2s ease;background-color:#fff}.form-control[_ngcontent-%COMP%]:hover{border-color:var(--primary-color-80)}.form-control[_ngcontent-%COMP%]:focus{outline:none;border-color:var(--primary-color);box-shadow:0 0 0 3px #199a8e1a}.form-control[_ngcontent-%COMP%]::placeholder{color:var(--text-secondary)}@media (max-width: 640px){.modal-content[_ngcontent-%COMP%]{width:95%;margin:1rem}.modal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.1rem}.modal-body[_ngcontent-%COMP%], .modal-footer[_ngcontent-%COMP%]{padding:1rem}}.modal-header[_ngcontent-%COMP%]{padding:1.5rem;border-bottom:1px solid var(--border-color);display:flex;justify-content:space-between;align-items:center;background-color:var(--bg-light);border-top-left-radius:16px;border-top-right-radius:16px}.modal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:1.25rem;color:var(--text-primary);font-weight:600}.close-button[_ngcontent-%COMP%]{background:none;border:none;cursor:pointer;padding:8px;color:var(--text-secondary);transition:color .2s;border-radius:50%;display:flex;align-items:center;justify-content:center}.close-button[_ngcontent-%COMP%]:hover{color:var(--text-primary);background-color:#0000000d}.modal-body[_ngcontent-%COMP%]{padding:2rem}.warning-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center;padding:1rem}.warning-icon[_ngcontent-%COMP%]{font-size:3.5rem;color:#f59e0b;margin-bottom:1.5rem;animation:_ngcontent-%COMP%_warning-pulse 2s infinite}@keyframes _ngcontent-%COMP%_warning-pulse{0%{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}to{transform:scale(1);opacity:1}}.warning-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem;color:var(--text-primary);margin-bottom:.5rem;line-height:1.5}.warning-text[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:.875rem;margin-top:1rem;font-style:italic}.modal-footer[_ngcontent-%COMP%]{padding:1.5rem;border-top:1px solid var(--border-color);display:flex;justify-content:center;gap:1.5rem;background-color:var(--bg-light);border-bottom-left-radius:16px;border-bottom-right-radius:16px}.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:140px;font-weight:500;padding:.75rem 1.5rem;transition:all .2s ease;text-align:center;justify-content:center;display:flex;align-items:center;border-radius:8px}.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:translateY(-1px)}.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:active{transform:translateY(0)}.btn-secondary[_ngcontent-%COMP%]{border:1px solid var(--border-color);background:#fff;color:var(--text-primary);font-size:.9rem}.btn-secondary[_ngcontent-%COMP%]:hover{background:var(--bg-light)}.btn-danger[_ngcontent-%COMP%]{border:1px solid #dc2626;background:#dc2626;color:#fff;font-size:.9rem}.btn-danger[_ngcontent-%COMP%]:hover{background:#b91c1c;border-color:#b91c1c}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:10px;padding:12px;border-radius:6px;background-color:#dc35451a;border:1px solid rgba(220,53,69,.3);display:flex;align-items:center;font-weight:500}.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;font-size:16px;color:#dc3545}.spinning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@media (max-width: 768px){.action-links[_ngcontent-%COMP%]{flex-direction:column;gap:8px;align-items:flex-start}.action-link[_ngcontent-%COMP%]{width:100%;justify-content:flex-start}.btn-primary[_ngcontent-%COMP%]{width:100%;justify-content:center}.modal-content[_ngcontent-%COMP%]{width:95%;max-width:400px}}@media (max-width: 480px){.card-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:18px}.btn-primary[_ngcontent-%COMP%]{padding:6px 12px;font-size:13px}.modal-body[_ngcontent-%COMP%]{padding:1rem}.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:100px;padding:.5rem 1rem;font-size:.8rem}.form-control[_ngcontent-%COMP%]{padding:.5rem;font-size:.9rem}}@supports (-webkit-touch-callout: none){[_nghost-%COMP%]{height:-webkit-fill-available}.dashboard-container[_ngcontent-%COMP%]{height:-webkit-fill-available}}.patients-table-container[_ngcontent-%COMP%]{width:100%;overflow-x:auto;-webkit-overflow-scrolling:touch;scrollbar-width:thin}.patients-table-container[_ngcontent-%COMP%]::-webkit-scrollbar{height:6px}.patients-table-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:10px}.patients-table-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:10px}.patients-table-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}@media (max-width: 768px){.action-link[_ngcontent-%COMP%], .btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%], .btn-danger[_ngcontent-%COMP%], .nav-item[_ngcontent-%COMP%], input[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{min-height:44px}}.empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:48px 24px;text-align:center}.empty-icon[_ngcontent-%COMP%]{font-size:64px;width:100px;height:100px;border-radius:50%;background-color:#f0f9ff;color:#199a8e;display:flex;align-items:center;justify-content:center;margin-bottom:24px}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;margin-bottom:12px;color:#111827}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:16px;color:#6b7280;margin-bottom:24px;max-width:400px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:48px 24px;text-align:center}.spinner[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:3px solid #f3f3f3;border-top:3px solid #199a8e;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:16px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.btn-outline[_ngcontent-%COMP%]{background:transparent;border:1px solid #199a8e;color:#199a8e;padding:10px 16px;border-radius:8px;font-weight:500;cursor:pointer;transition:all .2s ease}.btn-outline[_ngcontent-%COMP%]:hover{background-color:#f0f9ff}.success-message[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px;margin:12px 0;border-radius:8px;background-color:#ecfdf5;color:#166534}.success-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px;font-size:18px}.spinning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite;display:inline-block}"]})};function oc(o,e){if(o&1){let t=V();r(0,"div",27)(1,"div",28),l(2,"Doctor"),a(),r(3,"div",29)(4,"button",30),h("click",function(){let i=v(t),s=i.$implicit,c=i.index,m=b();return x(m.editStaff(s,c))}),g(5,"i",31),a(),r(6,"button",32),h("click",function(){let i=v(t).index,s=b();return x(s.deleteStaff(i))}),g(7,"i",33),a()(),r(8,"div",34)(9,"h2"),l(10),a(),r(11,"div",35),g(12,"i",36),l(13),a()(),r(14,"div",37)(15,"div",38)(16,"div",39),g(17,"i",40),l(18,"Email "),a(),r(19,"div",41),l(20),a()(),r(21,"div",38)(22,"div",39),g(23,"i",42),l(24,"Contact "),a(),r(25,"div",41),l(26),a()(),r(27,"div",38)(28,"div",39),g(29,"i",43),l(30,"Address "),a(),r(31,"div",41),l(32),a()(),r(33,"div",38)(34,"div",39),g(35,"i",44),l(36,"Qualifications "),a(),r(37,"div",41),l(38),a()()()()}if(o&2){let t=e.$implicit;d(10),T(t.name),d(3),M("",t.specialization," "),d(7),T(t.email),d(6),T(t.phone),d(6),T(t.address),d(6),T(t.qualifications)}}var ii=class o{constructor(e){this.router=e}doctors=[];currentEditStaff=null;currentEditIndex=-1;ngOnInit(){this.loadStaffData()}loadStaffData(){let e=localStorage.getItem("doctors");e&&(this.doctors=JSON.parse(e))}addNewDoctor(){this.router.navigate(["/doctors/add"])}editStaff(e,t){let n={staff:B({},e),index:t};localStorage.setItem("staffToEdit",JSON.stringify(n)),this.router.navigate(["/doctors/add"])}deleteStaff(e){confirm("Are you sure you want to delete this doctor?")&&(this.doctors.splice(e,1),localStorage.setItem("doctors",JSON.stringify(this.doctors)))}logout(){this.router.navigate(["/"])}static \u0275fac=function(t){return new(t||o)(P(j))};static \u0275cmp=E({type:o,selectors:[["app-doctors"]],decls:43,vars:1,consts:[[1,"dashboard-container"],[1,"sidebar"],[1,"logo-section"],[1,"logo"],[1,"primary"],[1,"secondary"],[1,"nav-menu"],["routerLink","/dashboard","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-grid-1x2-fill","nav-icon"],["routerLink","/doctors-patient","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-people-fill","nav-icon"],["routerLink","/doctors","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-person-badge-fill","nav-icon"],["routerLink","/appointments","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-calendar2-week-fill","nav-icon"],["routerLink","/settings","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-gear-fill","nav-icon"],[1,"logout-section"],[1,"logout-button",3,"click"],[1,"bi","bi-box-arrow-right","nav-icon"],[1,"main-content"],[1,"header"],[1,"title"],[1,"add-staff-btn",3,"click"],[1,"bi","bi-plus"],[1,"staff-grid"],["class","staff-card",4,"ngFor","ngForOf"],[1,"staff-card"],[1,"role-badge"],[1,"card-actions"],[1,"action-btn","edit-btn",3,"click"],[1,"bi","bi-pencil-fill"],[1,"action-btn","delete-btn",3,"click"],[1,"bi","bi-trash-fill"],[1,"card-header"],[1,"specialization"],[1,"bi","bi-shield-fill-check"],[1,"card-content"],[1,"info-item"],[1,"info-label"],[1,"bi","bi-envelope-fill"],[1,"info-value"],[1,"bi","bi-telephone-fill"],[1,"bi","bi-geo-alt-fill"],[1,"bi","bi-award-fill"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"span",4),l(5,"Med"),a(),r(6,"span",5),l(7,"Secura"),a()()(),r(8,"nav",6)(9,"a",7),g(10,"i",8),r(11,"span"),l(12,"Dashboard"),a()(),r(13,"a",9),g(14,"i",10),r(15,"span"),l(16,"Patients"),a()(),r(17,"a",11),g(18,"i",12),r(19,"span"),l(20,"Doctors"),a()(),r(21,"a",13),g(22,"i",14),r(23,"span"),l(24,"Appointments"),a()(),r(25,"a",15),g(26,"i",16),r(27,"span"),l(28,"Settings"),a()()(),r(29,"div",17)(30,"a",18),h("click",function(){return n.logout()}),g(31,"i",19),r(32,"span"),l(33,"Logout"),a()()()(),r(34,"div",20)(35,"header",21)(36,"h1",22),l(37,"Doctors List"),a(),r(38,"button",23),h("click",function(){return n.addNewDoctor()}),g(39,"i",24),l(40,"Add New Doctor "),a()(),r(41,"div",25),_(42,oc,39,6,"div",26),a()()()),t&2&&(d(42),p("ngForOf",n.doctors))},dependencies:[A,Se,ve,_e,ft,ee],styles:[".dashboard-container[_ngcontent-%COMP%]{display:flex;width:100%;min-height:100vh;background:var(--bg-light)}.sidebar[_ngcontent-%COMP%]{width:256px;background:var(--bg-white);border-right:1px solid var(--border-color);display:flex;flex-direction:column;position:sticky;top:0;height:100vh;z-index:10}.logo-section[_ngcontent-%COMP%]{height:70px;border-bottom:1px solid var(--border-color);display:flex;align-items:center;padding:0 24px}.logo[_ngcontent-%COMP%]{font-size:22.88px;line-height:32px}.logo[_ngcontent-%COMP%]   .primary[_ngcontent-%COMP%]{color:var(--primary-color);font-weight:700}.logo[_ngcontent-%COMP%]   .secondary[_ngcontent-%COMP%]{color:var(--primary-color-80);font-weight:400}.nav-menu[_ngcontent-%COMP%]{padding:16px 0;flex:1}.nav-item[_ngcontent-%COMP%]{display:flex;align-items:center;height:44px;padding:0 24px;color:var(--text-primary);font-size:15.38px;cursor:pointer;text-decoration:none}.nav-item[_ngcontent-%COMP%]:hover{background:var(--bg-light)}.nav-item.active[_ngcontent-%COMP%]{background:var(--bg-icon-light);color:var(--primary-color)}.nav-icon[_ngcontent-%COMP%]{font-size:20px;margin-right:12px;color:var(--text-secondary)}.active[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{color:var(--primary-color)}.logout-section[_ngcontent-%COMP%]{height:44px;border-top:1px solid var(--border-color);display:flex;align-items:center;padding:0 24px}.logout-button[_ngcontent-%COMP%]{display:flex;align-items:center;color:var(--text-primary);font-size:15.25px;cursor:pointer;text-decoration:none}.main-content[_ngcontent-%COMP%]{flex:1;padding:24px;display:flex;flex-direction:column;overflow-x:hidden}.header[_ngcontent-%COMP%]{background:var(--bg-white);padding:16px 24px;border-radius:var(--border-radius-md);box-shadow:var(--shadow-sm);display:flex;justify-content:space-between;align-items:center;margin-bottom:24px;position:sticky;z-index:5}.title[_ngcontent-%COMP%]{font-size:24px;font-weight:600;color:var(--text-primary);margin:0}.add-staff-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background:var(--primary-color);color:#fff;border:none;border-radius:var(--border-radius-md);padding:12px 24px;font-size:14px;font-weight:500;cursor:pointer;transition:all .2s ease}.add-staff-btn[_ngcontent-%COMP%]:hover{background:var(--primary-color-80);transform:translateY(-1px)}.add-staff-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px}.section-header[_ngcontent-%COMP%]{margin:24px 0 12px}.section-title[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--text-primary);margin:0;padding-bottom:8px;border-bottom:1px solid var(--border-color)}.staff-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(320px,1fr));gap:24px;padding:0 1px;margin-bottom:24px}.staff-card[_ngcontent-%COMP%]{background:var(--bg-white);border-radius:12px;border:1px solid var(--border-color);overflow:hidden;position:relative;transition:all .3s ease;box-shadow:0 2px 4px #0000000d}.staff-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 12px 24px #0000001a}.role-badge[_ngcontent-%COMP%]{position:absolute;top:16px;right:16px;padding:6px 16px;background:#ffffffe6;color:var(--primary-color);border-radius:20px;font-size:12px;font-weight:600;z-index:2;transition:opacity .3s ease}.role-badge.doctor[_ngcontent-%COMP%], .role-badge.receptionist[_ngcontent-%COMP%]{background-color:#199a8e1a}.card-actions[_ngcontent-%COMP%]{position:absolute;top:16px;right:16px;display:flex;gap:8px;opacity:0;visibility:hidden;transform:translateY(-10px);transition:all .3s ease;z-index:3}.staff-card[_ngcontent-%COMP%]:hover   .card-actions[_ngcontent-%COMP%]{opacity:1;visibility:visible;transform:translateY(0)}.staff-card[_ngcontent-%COMP%]:hover   .role-badge[_ngcontent-%COMP%]{opacity:0;visibility:hidden}.action-btn[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:8px;display:flex;align-items:center;justify-content:center;border:none;cursor:pointer;transition:all .2s ease}.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;color:#fff}.edit-btn[_ngcontent-%COMP%]{background:#0026260e;color:#199a8e}.delete-btn[_ngcontent-%COMP%]{background:#dc26261a;color:#dc2626}.edit-btn[_ngcontent-%COMP%]:hover{background:var(--primary-color);transform:translateY(-2px)}.delete-btn[_ngcontent-%COMP%]:hover{background:#dc2626;color:#fff;transform:translateY(-2px)}.card-header[_ngcontent-%COMP%]{padding:24px;background:linear-gradient(135deg,var(--primary-color),var(--primary-color-80));color:#fff}.doctor-header[_ngcontent-%COMP%], .receptionist-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#199a8e,#147a70)}.card-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin:0 0 8px;color:#fff;line-height:1.4;padding-right:40px}.specialization[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;opacity:.9}.specialization[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;margin-right:8px;color:#ffffffe6}.card-content[_ngcontent-%COMP%]{padding:24px}.info-item[_ngcontent-%COMP%]{margin-bottom:16px}.info-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.info-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;color:var(--text-secondary);font-size:13px;margin-bottom:4px}.info-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--primary-color);font-size:14px;flex-shrink:0}.info-value[_ngcontent-%COMP%]{color:var(--text-primary);font-size:14px;padding-left:24px}@media (min-width: 1400px){.staff-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(4,1fr)}}@media (min-width: 1024px) and (max-width: 1399px){.staff-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(3,1fr)}}@media (max-width: 1024px){.staff-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(280px,1fr))}}@media (min-width: 768px) and (max-width: 1023px){.staff-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}@media (max-width: 768px){.dashboard-container[_ngcontent-%COMP%]{flex-direction:column}.sidebar[_ngcontent-%COMP%]{width:100%;height:auto;position:relative}.main-content[_ngcontent-%COMP%]{padding:16px}.header[_ngcontent-%COMP%]{position:static;flex-direction:column;gap:16px;align-items:stretch;text-align:center;padding:16px}.add-staff-btn[_ngcontent-%COMP%]{justify-content:center}.staff-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.nav-menu[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;padding:8px}.nav-item[_ngcontent-%COMP%]{width:auto;padding:8px 16px}.logo-section[_ngcontent-%COMP%]{justify-content:center}}@media (max-width: 767px){.main-content[_ngcontent-%COMP%]{padding:12px}.staff-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(220px,1fr));gap:12px}.staff-card[_ngcontent-%COMP%]{max-width:none}}@media (max-width: 480px){.main-content[_ngcontent-%COMP%], .header[_ngcontent-%COMP%]{padding:12px}.card-header[_ngcontent-%COMP%], .card-content[_ngcontent-%COMP%]{padding:16px}}"]})};var oi=class o{menuItems=[{path:"/dashboard",icon:"bi-grid-1x2-fill",label:"Dashboard"},{path:"/doctors-patient",icon:"bi-people-fill",label:"Patients"},{path:"/doctors",icon:"bi-person-badge-fill",label:"Doctors"},{path:"/appointments",icon:"bi-calendar2-week-fill",label:"Appointments"},{path:"/settings",icon:"bi-gear-fill",label:"Settings"}];logout(){console.log("Logout clicked")}static \u0275fac=function(t){return new(t||o)};static \u0275cmp=E({type:o,selectors:[["app-sidebar"]],decls:0,vars:0,template:function(t,n){},dependencies:[A,ve],encapsulation:2})};function rc(o,e){o&1&&(r(0,"div",28),l(1," Name is required and must be at least 2 characters "),a())}function ac(o,e){o&1&&(r(0,"div",28),l(1," Please enter a valid email address "),a())}function sc(o,e){o&1&&(r(0,"div",28),l(1," Password must be at least 6 characters "),a())}function lc(o,e){o&1&&(r(0,"div",28),l(1," Please enter a valid 10-digit phone number "),a())}function cc(o,e){o&1&&(r(0,"div",28),l(1," Address is required "),a())}function dc(o,e){o&1&&(r(0,"div",28),l(1," Specialization is required for doctors "),a())}function mc(o,e){o&1&&(r(0,"div",28),l(1," Qualifications are required "),a())}var ri=class o{constructor(e,t,n){this.fb=e;this.router=t;this.authService=n;this.staffForm=this.fb.group({name:["",[S.required,S.minLength(2)]],email:["",[S.required,S.email]],password:["",[S.required,S.minLength(6)]],phone:["",[S.required,S.pattern("^[0-9]{10}$")]],address:["",S.required],specialization:["",S.required],qualifications:["",S.required]})}staffForm;isEditMode=!1;editInfo=null;pageTitle="Add New Doctor";ngOnInit(){let e=localStorage.getItem("staffToEdit");e&&(this.isEditMode=!0,this.editInfo=JSON.parse(e),this.pageTitle="Edit Doctor",this.editInfo&&this.editInfo.staff&&this.staffForm.patchValue({name:this.editInfo.staff.name,email:this.editInfo.staff.email,password:this.editInfo.staff.password,phone:this.editInfo.staff.phone,address:this.editInfo.staff.address,specialization:this.editInfo.staff.specialization,qualifications:this.editInfo.staff.qualifications}))}onSubmit(){if(this.staffForm.valid){let e=this.staffForm.value,t=Ce(B({},e),{profilePicture:this.isEditMode?this.editInfo?.staff.profilePicture:null});if(this.isEditMode&&this.editInfo){let n=localStorage.getItem("doctors");if(n){let i=JSON.parse(n);i[this.editInfo.index]=t,localStorage.setItem("doctors",JSON.stringify(i))}localStorage.removeItem("staffToEdit")}else{let n=localStorage.getItem("doctors"),i=n?JSON.parse(n):[];i.push(t),localStorage.setItem("doctors",JSON.stringify(i))}this.router.navigate(["/doctors"])}else Object.keys(this.staffForm.controls).forEach(e=>{let t=this.staffForm.get(e);t?.invalid&&t.markAsTouched()})}onCancel(){this.isEditMode&&localStorage.removeItem("staffToEdit"),this.router.navigate(["/doctors"])}static \u0275fac=function(t){return new(t||o)(P(Ye),P(j),P(le))};static \u0275cmp=E({type:o,selectors:[["app-add-staff"]],decls:64,vars:11,consts:[[1,"dashboard-container"],[1,"main-content"],[1,"form-container"],[1,"form-card"],[1,"form-title"],[1,"staff-form",3,"ngSubmit","formGroup"],[1,"form-group"],[1,"input-wrapper"],[1,"bi","bi-person-fill"],["type","text","formControlName","name","placeholder","Enter full name"],["class","error-message",4,"ngIf"],[1,"bi","bi-envelope-fill"],["type","email","formControlName","email","placeholder","Enter email address"],[1,"bi","bi-lock-fill"],["type","password","formControlName","password","placeholder","Enter password"],[1,"bi","bi-telephone-fill"],["type","tel","formControlName","phone","placeholder","Enter phone number"],[1,"bi","bi-geo-alt-fill"],["formControlName","address","placeholder","Enter address","rows","2"],[1,"bi","bi-shield-fill-check"],["type","text","formControlName","specialization","placeholder","Enter specialization"],[1,"bi","bi-award-fill"],["formControlName","qualifications","placeholder","Enter qualifications","rows","2"],[1,"form-actions"],["type","button",1,"cancel-btn",3,"click"],[1,"bi","bi-x"],["type","submit",1,"submit-btn",3,"disabled"],[1,"bi","bi-check2"],[1,"error-message"]],template:function(t,n){if(t&1&&(r(0,"div",0),g(1,"app-sidebar"),r(2,"div",1)(3,"div",2)(4,"div",3)(5,"h1",4),l(6),a(),r(7,"form",5),h("ngSubmit",function(){return n.onSubmit()}),r(8,"div",6)(9,"label"),l(10,"Full Name"),a(),r(11,"div",7),g(12,"i",8)(13,"input",9),a(),_(14,rc,2,0,"div",10),a(),r(15,"div",6)(16,"label"),l(17,"Email"),a(),r(18,"div",7),g(19,"i",11)(20,"input",12),a(),_(21,ac,2,0,"div",10),a(),r(22,"div",6)(23,"label"),l(24,"Password"),a(),r(25,"div",7),g(26,"i",13)(27,"input",14),a(),_(28,sc,2,0,"div",10),a(),r(29,"div",6)(30,"label"),l(31,"Phone Number"),a(),r(32,"div",7),g(33,"i",15)(34,"input",16),a(),_(35,lc,2,0,"div",10),a(),r(36,"div",6)(37,"label"),l(38,"Address"),a(),r(39,"div",7),g(40,"i",17)(41,"textarea",18),a(),_(42,cc,2,0,"div",10),a(),r(43,"div",6)(44,"label"),l(45,"Specialization"),a(),r(46,"div",7),g(47,"i",19)(48,"input",20),a(),_(49,dc,2,0,"div",10),a(),r(50,"div",6)(51,"label"),l(52,"Qualifications"),a(),r(53,"div",7),g(54,"i",21)(55,"textarea",22),a(),_(56,mc,2,0,"div",10),a(),r(57,"div",23)(58,"button",24),h("click",function(){return n.onCancel()}),g(59,"i",25),l(60," Cancel "),a(),r(61,"button",26),g(62,"i",27),l(63),a()()()()()()()),t&2){let i,s,c,m,u,f,C;d(6),T(n.pageTitle),d(),p("formGroup",n.staffForm),d(7),p("ngIf",((i=n.staffForm.get("name"))==null?null:i.touched)&&((i=n.staffForm.get("name"))==null?null:i.invalid)),d(7),p("ngIf",((s=n.staffForm.get("email"))==null?null:s.touched)&&((s=n.staffForm.get("email"))==null?null:s.invalid)),d(7),p("ngIf",((c=n.staffForm.get("password"))==null?null:c.touched)&&((c=n.staffForm.get("password"))==null?null:c.invalid)),d(7),p("ngIf",((m=n.staffForm.get("phone"))==null?null:m.touched)&&((m=n.staffForm.get("phone"))==null?null:m.invalid)),d(7),p("ngIf",((u=n.staffForm.get("address"))==null?null:u.touched)&&((u=n.staffForm.get("address"))==null?null:u.invalid)),d(7),p("ngIf",((f=n.staffForm.get("specialization"))==null?null:f.touched)&&((f=n.staffForm.get("specialization"))==null?null:f.invalid)),d(7),p("ngIf",((C=n.staffForm.get("qualifications"))==null?null:C.touched)&&((C=n.staffForm.get("qualifications"))==null?null:C.invalid)),d(5),p("disabled",n.staffForm.invalid),d(2),M(" ",n.isEditMode?"Update Doctor":"Save Doctor"," ")}},dependencies:[A,U,Fe,Ae,H,Z,De,We,$e,oi],styles:[".dashboard-container[_ngcontent-%COMP%]{display:flex;width:100%;min-height:100vh;background:var(--bg-light)}.main-content[_ngcontent-%COMP%]{flex:1;padding:24px;overflow-y:auto}.form-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;width:100%}.form-card[_ngcontent-%COMP%]{background:var(--bg-white);border-radius:var(--border-radius-md);box-shadow:var(--shadow-sm);padding:32px}.form-title[_ngcontent-%COMP%]{font-size:24px;font-weight:600;color:var(--text-primary);margin:0 0 32px}.staff-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.form-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:var(--text-primary);font-size:14px;font-weight:500}.role-selector[_ngcontent-%COMP%]{display:flex;gap:16px}.role-option[_ngcontent-%COMP%]{flex:1}.role-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{display:none}.role-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:12px;border:1px solid var(--border-color);border-radius:var(--border-radius-md);cursor:pointer;color:var(--text-primary);font-size:14px;font-weight:500}.role-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + .role-label[_ngcontent-%COMP%]{border-color:var(--primary-color);background-color:#199a8e1a;color:var(--primary-color)}.role-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + .role-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--primary-color)}.input-wrapper[_ngcontent-%COMP%]{position:relative;display:flex;align-items:flex-start}.input-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:12px;top:12px;color:var(--text-secondary);font-size:16px}.input-wrapper[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-wrapper[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:10px 12px 10px 40px;border:1px solid var(--border-color);border-radius:var(--border-radius-md);font-size:14px;color:var(--text-primary);background:var(--bg-white);transition:all .2s ease}.input-wrapper[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:vertical;min-height:60px}.input-wrapper[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .input-wrapper[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:var(--primary-color);box-shadow:0 0 0 3px #199a8e1a}.error-message[_ngcontent-%COMP%]{color:#dc2626;font-size:12px;margin-top:4px}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:16px;margin-top:32px}.submit-btn[_ngcontent-%COMP%], .cancel-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:12px 24px;border-radius:var(--border-radius-md);font-size:14px;font-weight:500;cursor:pointer;transition:all .2s ease}.submit-btn[_ngcontent-%COMP%]{background:var(--primary-color);color:#fff;border:none}.submit-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:var(--primary-color-80);transform:translateY(-1px)}.submit-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.cancel-btn[_ngcontent-%COMP%]{background:#fff;color:var(--text-primary);border:1px solid var(--border-color)}.cancel-btn[_ngcontent-%COMP%]:hover{background:var(--bg-light);border-color:var(--text-secondary)}@media (max-width: 768px){.main-content[_ngcontent-%COMP%]{padding:16px}.form-card[_ngcontent-%COMP%]{padding:24px}.role-selector[_ngcontent-%COMP%]{flex-direction:column}.form-actions[_ngcontent-%COMP%]{flex-direction:column-reverse;gap:12px}.submit-btn[_ngcontent-%COMP%], .cancel-btn[_ngcontent-%COMP%]{width:100%;justify-content:center}}@media (max-width: 480px){.main-content[_ngcontent-%COMP%]{padding:12px}.form-card[_ngcontent-%COMP%]{padding:16px}.form-title[_ngcontent-%COMP%]{font-size:20px;margin-bottom:24px}.staff-form[_ngcontent-%COMP%]{gap:20px}}"]})};function pc(o,e){o&1&&(r(0,"div",70),l(1," Full name is required "),a())}function gc(o,e){o&1&&(r(0,"div",70),l(1," Date of birth is required "),a())}function uc(o,e){o&1&&(r(0,"div",70),l(1," Gender is required "),a())}function fc(o,e){o&1&&(r(0,"div",70),l(1," Phone number is required "),a())}function hc(o,e){o&1&&(r(0,"div",70),l(1," Email is required "),a())}function bc(o,e){o&1&&(r(0,"div",70),l(1," Please enter a valid email address "),a())}function _c(o,e){o&1&&(r(0,"div",70),l(1," Address is required "),a())}function vc(o,e){o&1&&(r(0,"div",70),l(1," Emergency contact name is required "),a())}function xc(o,e){o&1&&(r(0,"div",70),l(1," Relationship is required "),a())}function yc(o,e){o&1&&(r(0,"div",70),l(1," Emergency contact phone is required "),a())}function Cc(o,e){o&1&&(r(0,"div",70),l(1," Diagnoses are required "),a())}function Pc(o,e){o&1&&(r(0,"div",70),l(1," Treatment plans are required "),a())}function Mc(o,e){o&1&&(r(0,"div",70),l(1," Consent is required "),a())}function wc(o,e){o&1&&(r(0,"div",70),l(1," Patient signature is required "),a())}function Oc(o,e){o&1&&(r(0,"div",70),l(1," Physician signature is required "),a())}function Sc(o,e){o&1&&(r(0,"div",70),l(1," Signature date is required "),a())}function Ec(o,e){if(o&1){let t=V();r(0,"app-success-dialog",71),h("closed",function(){v(t);let i=b();return x(i.onCloseSuccessModal())}),a()}if(o&2){let t=b();p("title","Success!")("message",t.successMessage)}}var ai=class o{constructor(e,t,n,i){this.fb=e;this.router=t;this.route=n;this.patientService=i;this.medicalRecordForm=this.fb.group({fullName:["",S.required],dateOfBirth:["",S.required],gender:["",S.required],phone:["",S.required],email:["",[S.required,S.email]],address:["",S.required],emergencyName:["",S.required],emergencyRelationship:["",S.required],emergencyPhone:["",S.required],chronicConditions:[""],previousSurgeries:[""],allergies:[""],currentMedications:[""],heredityConditions:[""],familyMemberAffected:[""],smokingStatus:["Never"],alcoholConsumption:["None"],exerciseFrequency:["None"],occupation:[""],bloodPressure:[""],heartRate:[""],temperature:[""],physicalFindings:[""],diagnoses:["",S.required],treatmentPlans:["",S.required],followUpDate:[""],consentChecked:[!1,S.requiredTrue],patientSignature:["",S.required],physicianSignature:["",S.required],signatureDate:["",S.required]})}medicalRecordForm;patientId="";recordId;isEditMode=!1;recordSub;showSuccessModal=!1;successMessage="";ngOnInit(){}ngOnDestroy(){}parseNotes(e){let t={};return e.split(`

`).forEach(i=>{i.includes("Physical Examination:")&&i.split(`
`).forEach(c=>{c.includes("BP:")&&(t.bloodPressure=c.split("BP:")[1].trim()),c.includes("HR:")&&(t.heartRate=c.split("HR:")[1].trim()),c.includes("Temp:")&&(t.temperature=c.split("Temp:")[1].trim()),c.includes("Findings:")&&(t.physicalFindings=c.split("Findings:")[1].trim())}),i.includes("Medical History:")&&i.split(`
`).forEach(c=>{c.includes("Chronic Conditions:")&&(t.chronicConditions=c.split("Chronic Conditions:")[1].trim()),c.includes("Previous Surgeries:")&&(t.previousSurgeries=c.split("Previous Surgeries:")[1].trim()),c.includes("Allergies:")&&(t.allergies=c.split("Allergies:")[1].trim())}),i.includes("Family History:")&&i.split(`
`).forEach(c=>{c.includes("Hereditary Conditions:")&&(t.heredityConditions=c.split("Hereditary Conditions:")[1].trim()),c.includes("Affected Family Members:")&&(t.familyMemberAffected=c.split("Affected Family Members:")[1].trim())}),i.includes("Social History:")&&i.split(`
`).forEach(c=>{c.includes("Smoking:")&&(t.smokingStatus=c.split("Smoking:")[1].trim()),c.includes("Alcohol:")&&(t.alcoholConsumption=c.split("Alcohol:")[1].trim()),c.includes("Exercise:")&&(t.exerciseFrequency=c.split("Exercise:")[1].trim()),c.includes("Occupation:")&&(t.occupation=c.split("Occupation:")[1].trim())}),i.includes("Emergency Contact:")&&i.split(`
`).forEach(c=>{c.includes("Name:")&&(t.emergencyName=c.split("Name:")[1].trim()),c.includes("Relationship:")&&(t.emergencyRelationship=c.split("Relationship:")[1].trim()),c.includes("Phone:")&&(t.emergencyPhone=c.split("Phone:")[1].trim())})}),t}onSubmit(){}onCloseSuccessModal(){this.showSuccessModal=!1,this.router.navigate(["/doctor/patients"])}markFormGroupTouched(e){Object.values(e.controls).forEach(t=>{t.markAsTouched(),t instanceof Zo&&this.markFormGroupTouched(t)})}saveDraft(){console.log("Saving draft...")}cancel(){this.router.navigate(["/doctor/patients",this.patientId,"medical-record"])}static \u0275fac=function(t){return new(t||o)(P(Ye),P(j),P(Ft),P(Ke))};static \u0275cmp=E({type:o,selectors:[["app-add-medical-record"]],decls:160,vars:19,consts:[[1,"form-container"],[1,"header"],[1,"bi","bi-clipboard2-pulse-fill","header-icon"],[1,"header-title"],[1,"form-content",3,"ngSubmit","formGroup"],[1,"section"],[1,"section-title"],[1,"form-row"],[1,"form-group"],["for","fullName",1,"form-label"],["type","text","id","fullName","formControlName","fullName","placeholder","Enter patient's full name",1,"form-control"],["class","error-message",4,"ngIf"],["for","dateOfBirth",1,"form-label"],["type","date","id","dateOfBirth","formControlName","dateOfBirth",1,"form-control","date"],["for","gender",1,"form-label"],["id","gender","formControlName","gender",1,"select-control"],["value","","disabled","","selected",""],["value","male"],["value","female"],["value","other"],["for","phone",1,"form-label"],["type","tel","id","phone","formControlName","phone","placeholder","Enter phone number",1,"form-control"],["for","email",1,"form-label"],["type","email","id","email","formControlName","email","placeholder","Enter email address",1,"form-control"],["for","address",1,"form-label"],["id","address","formControlName","address","placeholder","Enter full address",1,"form-control","textarea"],["for","emergencyName",1,"form-label"],["type","text","id","emergencyName","formControlName","emergencyName","placeholder","Enter emergency contact name",1,"form-control"],["for","emergencyRelationship",1,"form-label"],["type","text","id","emergencyRelationship","formControlName","emergencyRelationship","placeholder","Enter relationship to patient",1,"form-control"],["for","emergencyPhone",1,"form-label"],["type","tel","id","emergencyPhone","formControlName","emergencyPhone","placeholder","Enter emergency contact phone",1,"form-control"],[1,"form-row","triple"],["for","smokingStatus",1,"form-label"],["id","smokingStatus","formControlName","smokingStatus",1,"select-control"],["value","Never"],["value","Former"],["value","Current"],["for","alcoholConsumption",1,"form-label"],["id","alcoholConsumption","formControlName","alcoholConsumption",1,"select-control"],["value","None"],["value","Occasional"],["value","Moderate"],["value","Heavy"],["for","exerciseFrequency",1,"form-label"],["id","exerciseFrequency","formControlName","exerciseFrequency",1,"select-control"],["value","Regular"],["value","Frequent"],["for","occupation",1,"form-label"],["type","text","id","occupation","formControlName","occupation","placeholder","Enter occupation",1,"form-control"],["for","diagnoses",1,"form-label"],["id","diagnoses","formControlName","diagnoses","placeholder","Enter diagnoses",1,"form-control","textarea"],["for","treatmentPlans",1,"form-label"],["id","treatmentPlans","formControlName","treatmentPlans","placeholder","Medications, Therapies",1,"form-control","textarea"],["for","followUpDate",1,"form-label"],["type","date","id","followUpDate","formControlName","followUpDate",1,"form-control","date"],[1,"checkbox-group"],["type","checkbox","id","consent","formControlName","consentChecked",1,"checkbox-input"],["for","consent",1,"checkbox-label"],["for","patientSignature",1,"form-label"],["type","text","id","patientSignature","formControlName","patientSignature","placeholder","Enter patient signature",1,"form-control"],["for","physicianSignature",1,"form-label"],["type","text","id","physicianSignature","formControlName","physicianSignature","placeholder","Enter physician signature",1,"form-control"],["for","signatureDate",1,"form-label"],["type","date","id","signatureDate","formControlName","signatureDate",1,"form-control","date"],[1,"button-group"],["type","button",1,"btn","btn-cancel",3,"click"],["type","button",1,"btn","btn-draft",3,"click"],["type","submit",1,"btn","btn-submit",3,"disabled"],[3,"title","message","closed",4,"ngIf"],[1,"error-message"],[3,"closed","title","message"]],template:function(t,n){if(t&1&&(r(0,"div",0)(1,"div",1),g(2,"i",2),r(3,"h1",3),l(4,"Patient Medical Record"),a()(),r(5,"form",4),h("ngSubmit",function(){return n.onSubmit()}),r(6,"div",5)(7,"h2",6),l(8,"Patient Information"),a(),r(9,"div",7)(10,"div",8)(11,"label",9),l(12,"Full Name"),a(),g(13,"input",10),_(14,pc,2,0,"div",11),a(),r(15,"div",8)(16,"label",12),l(17,"Date of Birth"),a(),g(18,"input",13),_(19,gc,2,0,"div",11),a()(),r(20,"div",7)(21,"div",8)(22,"label",14),l(23,"Gender"),a(),r(24,"select",15)(25,"option",16),l(26,"Select Gender"),a(),r(27,"option",17),l(28,"Male"),a(),r(29,"option",18),l(30,"Female"),a(),r(31,"option",19),l(32,"Other"),a()(),_(33,uc,2,0,"div",11),a(),r(34,"div",8)(35,"label",20),l(36,"Phone"),a(),g(37,"input",21),_(38,fc,2,0,"div",11),a()(),r(39,"div",8)(40,"label",22),l(41,"Email"),a(),g(42,"input",23),_(43,hc,2,0,"div",11)(44,bc,2,0,"div",11),a(),r(45,"div",8)(46,"label",24),l(47,"Address"),a(),g(48,"textarea",25),_(49,_c,2,0,"div",11),a()(),r(50,"div",5)(51,"h2",6),l(52,"Emergency Contact"),a(),r(53,"div",7)(54,"div",8)(55,"label",26),l(56,"Name"),a(),g(57,"input",27),_(58,vc,2,0,"div",11),a(),r(59,"div",8)(60,"label",28),l(61,"Relationship"),a(),g(62,"input",29),_(63,xc,2,0,"div",11),a()(),r(64,"div",8)(65,"label",30),l(66,"Phone"),a(),g(67,"input",31),_(68,yc,2,0,"div",11),a()(),r(69,"div",5)(70,"h2",6),l(71,"Social History"),a(),r(72,"div",32)(73,"div",8)(74,"label",33),l(75,"Smoking Status"),a(),r(76,"select",34)(77,"option",35),l(78,"Never"),a(),r(79,"option",36),l(80,"Former"),a(),r(81,"option",37),l(82,"Current"),a()()(),r(83,"div",8)(84,"label",38),l(85,"Alcohol Consumption"),a(),r(86,"select",39)(87,"option",40),l(88,"None"),a(),r(89,"option",41),l(90,"Occasional"),a(),r(91,"option",42),l(92,"Moderate"),a(),r(93,"option",43),l(94,"Heavy"),a()()(),r(95,"div",8)(96,"label",44),l(97,"Exercise Frequency"),a(),r(98,"select",45)(99,"option",40),l(100,"None"),a(),r(101,"option",41),l(102,"Occasional"),a(),r(103,"option",46),l(104,"Regular"),a(),r(105,"option",47),l(106,"Frequent"),a()()()(),r(107,"div",8)(108,"label",48),l(109,"Occupation"),a(),g(110,"input",49),a()(),r(111,"div",5)(112,"h2",6),l(113,"Assessment and Plan"),a(),r(114,"div",8)(115,"label",50),l(116,"Diagnoses"),a(),g(117,"textarea",51),_(118,Cc,2,0,"div",11),a(),r(119,"div",8)(120,"label",52),l(121,"Treatment Plans"),a(),g(122,"textarea",53),_(123,Pc,2,0,"div",11),a(),r(124,"div",8)(125,"label",54),l(126,"Follow-Up Appointments"),a(),g(127,"input",55),a()(),r(128,"div",5)(129,"h2",6),l(130,"Consent and Signatures"),a(),r(131,"div",56),g(132,"input",57),r(133,"label",58),l(134," I consent to treatment and acknowledge that I have received and understand the privacy practices. "),a()(),_(135,Mc,2,0,"div",11),r(136,"div",7)(137,"div",8)(138,"label",59),l(139,"Patient Signature"),a(),g(140,"input",60),_(141,wc,2,0,"div",11),a(),r(142,"div",8)(143,"label",61),l(144,"Physician Signature"),a(),g(145,"input",62),_(146,Oc,2,0,"div",11),a()(),r(147,"div",8)(148,"label",63),l(149,"Date"),a(),g(150,"input",64),_(151,Sc,2,0,"div",11),a()(),r(152,"div",65)(153,"button",66),h("click",function(){return n.cancel()}),l(154,"Cancel"),a(),r(155,"button",67),h("click",function(){return n.saveDraft()}),l(156,"Save as Draft"),a(),r(157,"button",68),l(158,"Submit Record"),a()()()(),_(159,Ec,1,2,"app-success-dialog",69)),t&2){let i,s,c,m,u,f,C,y,D,N,O,I,K,J,X,oe;d(5),p("formGroup",n.medicalRecordForm),d(9),p("ngIf",((i=n.medicalRecordForm.get("fullName"))==null||i.errors==null?null:i.errors.required)&&((i=n.medicalRecordForm.get("fullName"))==null?null:i.touched)),d(5),p("ngIf",((s=n.medicalRecordForm.get("dateOfBirth"))==null||s.errors==null?null:s.errors.required)&&((s=n.medicalRecordForm.get("dateOfBirth"))==null?null:s.touched)),d(14),p("ngIf",((c=n.medicalRecordForm.get("gender"))==null||c.errors==null?null:c.errors.required)&&((c=n.medicalRecordForm.get("gender"))==null?null:c.touched)),d(5),p("ngIf",((m=n.medicalRecordForm.get("phone"))==null||m.errors==null?null:m.errors.required)&&((m=n.medicalRecordForm.get("phone"))==null?null:m.touched)),d(5),p("ngIf",((u=n.medicalRecordForm.get("email"))==null||u.errors==null?null:u.errors.required)&&((u=n.medicalRecordForm.get("email"))==null?null:u.touched)),d(),p("ngIf",((f=n.medicalRecordForm.get("email"))==null||f.errors==null?null:f.errors.email)&&((f=n.medicalRecordForm.get("email"))==null?null:f.touched)),d(5),p("ngIf",((C=n.medicalRecordForm.get("address"))==null||C.errors==null?null:C.errors.required)&&((C=n.medicalRecordForm.get("address"))==null?null:C.touched)),d(9),p("ngIf",((y=n.medicalRecordForm.get("emergencyName"))==null||y.errors==null?null:y.errors.required)&&((y=n.medicalRecordForm.get("emergencyName"))==null?null:y.touched)),d(5),p("ngIf",((D=n.medicalRecordForm.get("emergencyRelationship"))==null||D.errors==null?null:D.errors.required)&&((D=n.medicalRecordForm.get("emergencyRelationship"))==null?null:D.touched)),d(5),p("ngIf",((N=n.medicalRecordForm.get("emergencyPhone"))==null||N.errors==null?null:N.errors.required)&&((N=n.medicalRecordForm.get("emergencyPhone"))==null?null:N.touched)),d(50),p("ngIf",((O=n.medicalRecordForm.get("diagnoses"))==null||O.errors==null?null:O.errors.required)&&((O=n.medicalRecordForm.get("diagnoses"))==null?null:O.touched)),d(5),p("ngIf",((I=n.medicalRecordForm.get("treatmentPlans"))==null||I.errors==null?null:I.errors.required)&&((I=n.medicalRecordForm.get("treatmentPlans"))==null?null:I.touched)),d(12),p("ngIf",((K=n.medicalRecordForm.get("consentChecked"))==null||K.errors==null?null:K.errors.required)&&((K=n.medicalRecordForm.get("consentChecked"))==null?null:K.touched)),d(6),p("ngIf",((J=n.medicalRecordForm.get("patientSignature"))==null||J.errors==null?null:J.errors.required)&&((J=n.medicalRecordForm.get("patientSignature"))==null?null:J.touched)),d(5),p("ngIf",((X=n.medicalRecordForm.get("physicianSignature"))==null||X.errors==null?null:X.errors.required)&&((X=n.medicalRecordForm.get("physicianSignature"))==null?null:X.touched)),d(5),p("ngIf",((oe=n.medicalRecordForm.get("signatureDate"))==null||oe.errors==null?null:oe.errors.required)&&((oe=n.medicalRecordForm.get("signatureDate"))==null?null:oe.touched)),d(6),p("disabled",!n.medicalRecordForm.valid),d(2),p("ngIf",n.showSuccessModal)}},dependencies:[A,U,Fe,Ae,yn,Cn,H,Ho,xn,Z,De,We,$e,ve,Yt],styles:[`.form-container[_ngcontent-%COMP%]{width:896px;margin:32px auto;padding:0 24px}.header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;margin-bottom:32px}.header-icon[_ngcontent-%COMP%]{width:32px;height:32px;color:#199a8e;font-size:32px}.header-title[_ngcontent-%COMP%]{color:#199a8e;font-size:28px;font-family:Inter,sans-serif;font-weight:700;line-height:36px;margin:0}.form-content[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:40px;box-shadow:0 4px 6px -4px #0000001a;max-width:100%}.section[_ngcontent-%COMP%]{margin-bottom:40px;padding-bottom:40px;border-bottom:1px solid #E5E7EB;display:flex;flex-direction:column;gap:24px}.section[_ngcontent-%COMP%]:last-child{margin-bottom:24px;padding-bottom:0;border-bottom:none}.section-title[_ngcontent-%COMP%]{color:#199a8e;font-size:20px;font-family:Inter,sans-serif;font-weight:600;line-height:28px;padding-bottom:8px;border-bottom:2px solid #199A8E;display:inline-block;margin:0}.form-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:40px;width:100%;align-items:flex-start}.form-row.triple[_ngcontent-%COMP%]{grid-template-columns:repeat(3,1fr)}.form-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;width:100%}.form-label[_ngcontent-%COMP%]{color:#374151;font-size:14px;font-family:Inter,sans-serif;font-weight:500;line-height:20px;display:block;margin:0}.form-control[_ngcontent-%COMP%]{width:100%;height:36px;background:#fff;border:1px solid #D1D5DB;border-radius:6px;padding:8px 12px;font-size:14px;font-family:Inter,sans-serif;color:#1f2937;transition:all .2s ease;margin:0;box-sizing:border-box}.form-control[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 3px #199a8e1a}.form-control.textarea[_ngcontent-%COMP%]{height:80px;resize:none;line-height:1.5;padding:12px}.form-control.date[_ngcontent-%COMP%]{height:36px;padding-right:12px}.select-control[_ngcontent-%COMP%]{width:100%;height:36px;background:#fff;border:1px solid #D1D5DB;border-radius:6px;padding:0 36px 0 12px;font-size:14px;font-family:Inter,sans-serif;color:#1f2937;cursor:pointer;transition:all .2s ease;appearance:none;background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236B7280' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");background-repeat:no-repeat;background-position:right 12px center;margin:0;box-sizing:border-box}.checkbox-group[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:12px;padding:12px 16px;background:#f9fafb;border-radius:6px;margin:0;box-sizing:border-box}.checkbox-input[_ngcontent-%COMP%]{width:16px;height:16px;margin:2px 0 0;accent-color:#199A8E;cursor:pointer}.checkbox-label[_ngcontent-%COMP%]{color:#374151;font-size:14px;font-family:Inter,sans-serif;font-weight:500;line-height:20px;cursor:pointer;margin:0}.button-group[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:16px;margin-top:40px;padding-top:24px;border-top:1px solid #E5E7EB}.btn[_ngcontent-%COMP%]{min-width:100px;height:36px;padding:0 20px;border-radius:6px;font-size:14px;font-family:Inter,sans-serif;font-weight:500;line-height:36px;cursor:pointer;transition:all .2s ease;margin:0;display:inline-flex;align-items:center;justify-content:center}.btn-cancel[_ngcontent-%COMP%]{background:#f3f4f6;color:#4b5563;border:none}.btn-cancel[_ngcontent-%COMP%]:hover{background:#e5e7eb}.btn-draft[_ngcontent-%COMP%]{background:#fff;color:#374151;border:1px solid #D1D5DB}.btn-draft[_ngcontent-%COMP%]:hover{background:#f9fafb;border-color:#9ca3af}.btn-submit[_ngcontent-%COMP%]{background:#199a8e;color:#fff;border:none}.btn-submit[_ngcontent-%COMP%]:hover{background:#168076}.btn-submit[_ngcontent-%COMP%]:disabled{background:#e5e7eb;color:#9ca3af;cursor:not-allowed}.form-control.ng-touched.ng-invalid[_ngcontent-%COMP%]{border-color:#dc2626}.error-message[_ngcontent-%COMP%]{color:#dc2626;font-size:12px;margin-top:4px;font-family:Inter,sans-serif}@media (max-width: 960px){.form-container[_ngcontent-%COMP%]{width:100%;padding:16px}.form-content[_ngcontent-%COMP%]{padding:24px}.form-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:24px}.form-row.triple[_ngcontent-%COMP%]{grid-template-columns:1fr}.button-group[_ngcontent-%COMP%]{flex-direction:column-reverse;gap:12px}.btn[_ngcontent-%COMP%]{width:100%}}.form-control[_ngcontent-%COMP%]::placeholder{color:#9ca3af;opacity:.8}.form-control[_ngcontent-%COMP%]:focus, .select-control[_ngcontent-%COMP%]:focus{background-color:#fff}.form-group[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{width:100%}`]})};var si=class o{constructor(e,t,n){this.patientService=e;this.route=t;this.router=n}records=[];patientId="";patient=null;isModalOpen=!1;selectedRecord=null;ngOnInit(){}static \u0275fac=function(t){return new(t||o)(P(Ke),P(Ft),P(j))};static \u0275cmp=E({type:o,selectors:[["app-medical-record-list"]],decls:0,vars:0,template:function(t,n){},dependencies:[A,ve],styles:[".records-container[_ngcontent-%COMP%]{max-width:1000px;margin:32px auto;padding:0 24px}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.header-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}h2[_ngcontent-%COMP%]{color:#199a8e;margin:0;font-size:1.5rem;font-weight:600}.btn-back[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:8px 16px;background:#6b7280;color:#fff;border:none;border-radius:6px;cursor:pointer;font-size:14px;font-weight:500;transition:background-color .2s}.btn-back[_ngcontent-%COMP%]:hover{background:#4b5563}.btn-add[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:8px 16px;background:#199a8e;color:#fff;border:none;border-radius:6px;cursor:pointer;font-size:14px;font-weight:500;transition:background-color .2s}.btn-add[_ngcontent-%COMP%]:hover{background:#168076}.records-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px}.record-card[_ngcontent-%COMP%]{background:#fff;border-radius:8px;box-shadow:0 2px 4px #0000001a;overflow:hidden;transition:transform .2s,box-shadow .2s}.record-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #0000001a}.record-header[_ngcontent-%COMP%]{padding:16px;background:#f9fafb;border-bottom:1px solid #E5E7EB;display:flex;justify-content:space-between;align-items:center}.date[_ngcontent-%COMP%]{color:#4b5563;font-size:14px;font-weight:500}.record-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.btn-view[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;padding:6px 12px;border-radius:4px;font-size:13px;font-weight:500;cursor:pointer;transition:background-color .2s;background:#e5e7eb;color:#4b5563;border:none}.btn-view[_ngcontent-%COMP%]:hover{background:#d1d5db}.record-content[_ngcontent-%COMP%]{padding:20px}.record-section[_ngcontent-%COMP%]{margin-bottom:20px}.record-section[_ngcontent-%COMP%]:last-child{margin-bottom:0}.record-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#374151;font-size:16px;font-weight:600;margin:0 0 8px}.record-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#4b5563;font-size:14px;line-height:1.5;margin:0;white-space:pre-line}.no-records[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:300px}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:48px;background:#fff;border-radius:8px;box-shadow:0 2px 4px #0000001a;width:100%;max-width:500px}.empty-icon[_ngcontent-%COMP%]{font-size:48px;color:#9ca3af;margin-bottom:16px}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6b7280;margin-bottom:24px;font-size:16px}@media (max-width: 640px){.records-container[_ngcontent-%COMP%]{padding:16px;margin:16px auto}.header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:16px}.header-left[_ngcontent-%COMP%]{width:100%;flex-direction:column;align-items:flex-start;gap:12px}.btn-add[_ngcontent-%COMP%]{width:100%;justify-content:center}.record-header[_ngcontent-%COMP%]{flex-direction:column;gap:12px;align-items:flex-start}.record-actions[_ngcontent-%COMP%]{width:100%}.btn-view[_ngcontent-%COMP%]{flex:1;justify-content:center}}"]})};function Tc(o,e){o&1&&(r(0,"div",13),l(1," Processing authentication... "),a())}function Ic(o,e){if(o&1){let t=V();r(0,"div",17)(1,"h4"),l(2,"Currently Logged In"),a(),r(3,"p"),l(4),a(),r(5,"p"),l(6),a(),r(7,"div",18)(8,"button",19),h("click",function(){v(t);let i=b(2);return x(i.logout())}),l(9,"Logout"),a(),r(10,"button",20),h("click",function(){v(t);let i=b(2);return x(i.createUserDocument())}),l(11,"Create User Document"),a()()()}if(o&2){let t=b(2);d(4),M("Email: ",t.currentUser.email,""),d(2),M("UID: ",t.currentUser.uid,"")}}function Dc(o,e){if(o&1){let t=V();r(0,"div",8)(1,"div",5)(2,"h4"),l(3,"Authentication Test"),a(),r(4,"div",21)(5,"label",22),l(6,"Email"),a(),r(7,"input",23),L("ngModelChange",function(i){v(t);let s=b(2);return R(s.testEmail,i)||(s.testEmail=i),x(i)}),a()(),r(8,"div",21)(9,"label",24),l(10,"Password"),a(),r(11,"input",25),L("ngModelChange",function(i){v(t);let s=b(2);return R(s.testPassword,i)||(s.testPassword=i),x(i)}),a()(),r(12,"div",26)(13,"button",27),h("click",function(){v(t);let i=b(2);return x(i.login())}),l(14,"Test Login"),a(),r(15,"button",28),h("click",function(){v(t);let i=b(2);return x(i.register())}),l(16,"Test Register"),a()()()()}if(o&2){let t=b(2);d(7),z("ngModel",t.testEmail),d(4),z("ngModel",t.testPassword)}}function Ac(o,e){if(o&1&&(r(0,"div",29)(1,"h4"),l(2,"Authentication Error"),a(),r(3,"p"),l(4),a()()),o&2){let t=b(2);d(4),T(t.authError)}}function Fc(o,e){if(o&1&&(r(0,"div"),_(1,Ic,12,2,"div",14)(2,Dc,17,2,"div",15)(3,Ac,5,1,"div",16),a()),o&2){let t=b();d(),p("ngIf",t.currentUser),d(),p("ngIf",!t.currentUser),d(),p("ngIf",t.authError)}}function Nc(o,e){o&1&&(r(0,"div",13),l(1," Testing Firebase connection... "),a())}function zc(o,e){if(o&1&&(r(0,"div",32)(1,"h4"),l(2,"Connection Error"),a(),r(3,"p"),l(4),a(),r(5,"p"),l(6,"Check the console for more details."),a()()),o&2){let t=b(2);d(4),T(t.error)}}function Rc(o,e){o&1&&(r(0,"div",17)(1,"h4"),l(2,"Connection Successful!"),a(),r(3,"p"),l(4,"Firebase is properly configured and connected."),a()())}function Lc(o,e){if(o&1&&(r(0,"li",35),l(1),tt(2,"date"),a()),o&2){let t=e.$implicit;d(),pn(" ",t.message," (created: ",_o(2,2,t.timestamp,"medium"),") ")}}function Vc(o,e){if(o&1&&(r(0,"div",10)(1,"h4"),l(2,"Test Collection Data:"),a(),r(3,"ul",33),_(4,Lc,3,5,"li",34),a()()),o&2){let t=b(2);d(4),p("ngForOf",t.testData)}}function Uc(o,e){if(o&1&&(r(0,"div"),_(1,zc,7,1,"div",30)(2,Rc,5,0,"div",14)(3,Vc,5,1,"div",31),a()),o&2){let t=b();d(),p("ngIf",t.error),d(),p("ngIf",!t.error),d(),p("ngIf",t.testData.length>0)}}var li=class o{constructor(e,t,n){this.firestore=e;this.auth=t;this.ngZone=n}loading=!0;error=null;testData=[];authLoading=!1;authError=null;currentUser=null;testEmail="";testPassword="";authSubscription=null;authErrorMapper=Pe(ir);ngOnInit(){this.runTest(),this.checkAuthState()}ngOnDestroy(){this.authSubscription&&this.authSubscription.unsubscribe()}checkAuthState(){this.ngZone.runOutsideAngular(()=>{this.authSubscription=new an;let e=bn(this.auth,t=>{this.ngZone.run(()=>{this.currentUser=t})});this.authSubscription.add(()=>e())})}runTest(){return ke(this,null,function*(){this.loading=!0,this.error=null,this.testData=[];try{yield this.ngZone.runOutsideAngular(()=>ke(this,null,function*(){let e=Q(this.firestore,"public_test"),t=fe(e,Bo(10)),n=yield xe(t);this.ngZone.run(()=>{this.testData=n.docs.map(i=>B({id:i.id},i.data())),this.loading=!1})}))}catch(e){this.ngZone.run(()=>{this.error=`Firebase test failed: ${e.message}`,this.loading=!1})}})}createTestDocument(){return ke(this,null,function*(){this.loading=!0,this.error=null;try{yield this.ngZone.runOutsideAngular(()=>ke(this,null,function*(){let e=Q(this.firestore,"public_test"),t={timestamp:new Date,message:"Test document for Firebase validation",createdBy:this.currentUser?.uid||"anonymous",email:this.currentUser?.email||"anonymous"};yield it(e,t),this.ngZone.run(()=>{this.runTest()})}))}catch(e){this.ngZone.run(()=>{this.error=`Failed to create test document: ${e.message}`,this.loading=!1,console.error("Error adding document:",e),e.message.includes("Missing or insufficient permissions")&&this.currentUser&&this.updateUserDocument()})}})}createUserDocument(){return ke(this,null,function*(){if(!this.currentUser){this.error="You must be logged in to create a user document";return}this.loading=!0,this.error=null;try{yield this.ngZone.runOutsideAngular(()=>ke(this,null,function*(){let e=me(this.firestore,"users",this.currentUser.uid);(yield qe(e)).exists()||(yield ht(e,{email:this.currentUser.email,displayName:this.currentUser.displayName||this.currentUser.email,photoURL:this.currentUser.photoURL||null,createdAt:new Date,role:"user"})),this.ngZone.run(()=>{this.error=null,this.loading=!1})}))}catch(e){this.ngZone.run(()=>{this.error=`Failed to create user document: ${e.message}`,this.loading=!1})}})}login(){return ke(this,null,function*(){if(!this.testEmail||!this.testPassword){this.authError="Email and password are required";return}this.authLoading=!0,this.authError=null;try{yield this.ngZone.runOutsideAngular(()=>ke(this,null,function*(){yield Lo(this.auth,this.testEmail,this.testPassword),this.ngZone.run(()=>{this.authLoading=!1,this.testEmail="",this.testPassword=""})}))}catch(e){this.ngZone.run(()=>{let t=this.authErrorMapper.mapFirebaseError(e);this.authError=t,this.authLoading=!1})}})}register(){return ke(this,null,function*(){if(!this.testEmail||!this.testPassword){this.authError="Email and password are required";return}this.authLoading=!0,this.authError=null;try{yield this.ngZone.runOutsideAngular(()=>ke(this,null,function*(){yield zo(this.auth,this.testEmail,this.testPassword),this.ngZone.run(()=>{this.authLoading=!1,this.testEmail="",this.testPassword=""})}))}catch(e){this.ngZone.run(()=>{let t=this.authErrorMapper.mapRegistrationError(e);this.authError=t,this.authLoading=!1})}})}logout(){return ke(this,null,function*(){this.authLoading=!0,this.authError=null;try{yield this.ngZone.runOutsideAngular(()=>ke(this,null,function*(){yield Vo(this.auth),this.ngZone.run(()=>{this.authLoading=!1})}))}catch(e){this.ngZone.run(()=>{this.authError=`Logout failed: ${e.message}`,this.authLoading=!1})}})}updateUserDocument(){if(!this.currentUser){this.error="You must be logged in to update your user document",this.loading=!1;return}this.ngZone.runOutsideAngular(()=>{let e=me(this.firestore,`users/${this.currentUser.uid}`),t={lastTestTimestamp:new Date().toISOString(),testMessage:"Document updated successfully",updatedAt:new Date().toISOString()};ht(e,t,{merge:!0}).then(()=>{this.ngZone.run(()=>{console.log("User document updated successfully"),this.tryFallbackCollection()})}).catch(n=>{this.ngZone.run(()=>{this.error=`Error updating user document: ${n.message}`,this.loading=!1,console.error("Error updating user document:",n)})})})}tryFallbackCollection(){this.loading=!0,this.ngZone.runOutsideAngular(()=>{if(this.currentUser){let e=me(this.firestore,`users/${this.currentUser.uid}`);qe(e).then(t=>{this.ngZone.run(()=>{if(t.exists())this.testData=[B({id:this.currentUser.uid,message:"Successfully accessed your user document",timestamp:new Date().toISOString()},t.data())],this.loading=!1,this.error=null;else throw new Error("User document does not exist - please create it first")})}).catch(t=>{this.ngZone.run(()=>{this.error=`Error accessing user document: ${t.message}`,this.loading=!1,console.error("Error accessing user document:",t)})})}else this.ngZone.run(()=>{this.error="You must be logged in to test Firestore access",this.loading=!1})})}static \u0275fac=function(t){return new(t||o)(P(Be),P(zt),P(je))};static \u0275cmp=E({type:o,selectors:[["app-firebase-test"]],decls:27,vars:7,consts:[[1,"container","mt-5"],[1,"card","mb-4"],[1,"card-header","bg-primary","text-white","d-flex","justify-content-between","align-items-center"],["routerLink","/firebase-validation",1,"btn","btn-outline-light","me-2"],["routerLink","/firebase-availability-debug",1,"btn","btn-outline-light"],[1,"card-body"],["class","alert alert-info",4,"ngIf"],[4,"ngIf"],[1,"card"],["routerLink","/firebase-debug",1,"btn","btn-outline-light"],[1,"mt-4"],[1,"btn","btn-primary","me-2",3,"click","disabled"],[1,"btn","btn-success",3,"click","disabled"],[1,"alert","alert-info"],["class","alert alert-success",4,"ngIf"],["class","card",4,"ngIf"],["class","alert alert-danger mt-3",4,"ngIf"],[1,"alert","alert-success"],[1,"d-flex","gap-2","mt-3"],[1,"btn","btn-warning",3,"click"],[1,"btn","btn-info",3,"click"],[1,"mb-3"],["for","email",1,"form-label"],["type","email","id","email",1,"form-control",3,"ngModelChange","ngModel"],["for","password",1,"form-label"],["type","password","id","password",1,"form-control",3,"ngModelChange","ngModel"],[1,"d-flex","gap-2"],[1,"btn","btn-primary",3,"click"],[1,"btn","btn-secondary",3,"click"],[1,"alert","alert-danger","mt-3"],["class","alert alert-danger",4,"ngIf"],["class","mt-4",4,"ngIf"],[1,"alert","alert-danger"],[1,"list-group"],["class","list-group-item",4,"ngFor","ngForOf"],[1,"list-group-item"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2"),l(4,"Firebase Authentication Test"),a(),r(5,"div")(6,"a",3),l(7,"Advanced Validation"),a(),r(8,"a",4),l(9,"Availability Debug"),a()()(),r(10,"div",5),_(11,Tc,2,0,"div",6)(12,Fc,4,3,"div",7),a()(),r(13,"div",8)(14,"div",2)(15,"h2"),l(16,"Firebase Firestore Test"),a(),r(17,"a",9),l(18,"Advanced Debug"),a()(),r(19,"div",5),_(20,Nc,2,0,"div",6)(21,Uc,4,3,"div",7),r(22,"div",10)(23,"button",11),h("click",function(){return n.runTest()}),l(24),a(),r(25,"button",12),h("click",function(){return n.createTestDocument()}),l(26," Add Test Document "),a()()()()()),t&2&&(d(11),p("ngIf",n.authLoading),d(),p("ngIf",!n.authLoading),d(8),p("ngIf",n.loading),d(),p("ngIf",!n.loading),d(2),p("disabled",n.loading),d(),M(" ",n.loading?"Testing...":"Run Test Again"," "),d(),p("disabled",n.loading||n.error))},dependencies:[A,Se,U,yo,ee,H,Z,pe,ve,_e],encapsulation:2})};var jc=(o,e)=>({"list-group-item-success":o,"list-group-item-danger":e});function Bc(o,e){o&1&&(r(0,"div",29)(1,"h4"),l(2,"Not Authenticated"),a(),r(3,"p"),l(4,"Please log in first to test Firebase rules."),a()())}function qc(o,e){if(o&1&&(r(0,"div",30)(1,"h4"),l(2,"Authenticated as:"),a(),r(3,"p"),l(4),a(),r(5,"p"),l(6),a()()),o&2){let t=b();d(4),M("Email: ",t.currentUser.email,""),d(2),M("UID: ",t.currentUser.uid,"")}}function Wc(o,e){if(o&1&&(r(0,"div",38),l(1),a()),o&2){let t=b().$implicit;d(),M(" Error: ",t.error," ")}}function $c(o,e){if(o&1&&(r(0,"div",39)(1,"pre"),l(2),tt(3,"json"),a()()),o&2){let t=b().$implicit;d(2),T(pt(3,1,t.data))}}function Yc(o,e){if(o&1&&(r(0,"div",34)(1,"div",35)(2,"strong"),l(3),a(),r(4,"span"),l(5),a(),r(6,"span"),l(7),a()(),_(8,Wc,2,1,"div",36)(9,$c,4,3,"div",37),a()),o&2){let t=e.$implicit;p("ngClass",Dt(6,jc,t.success,!t.success)),d(3),T(t.operation),d(2),T(t.path),d(2),T(t.success?"Success":"Failed"),d(),p("ngIf",t.error),d(),p("ngIf",t.data)}}function Hc(o,e){if(o&1&&(r(0,"div",31)(1,"h4"),l(2,"Test Results:"),a(),r(3,"div",32),_(4,Yc,10,9,"div",33),a()()),o&2){let t=b();d(4),p("ngForOf",t.results)}}var ci=class o{constructor(e,t,n){this.firestore=e;this.auth=t;this.ngZone=n}currentUser=null;authSubscription=null;testOperation="read";testCollection="public_test";testDocId="";results=[];ngOnInit(){this.ngZone.runOutsideAngular(()=>{this.authSubscription=new an;let e=bn(this.auth,t=>{this.ngZone.run(()=>{this.currentUser=t})});this.authSubscription.add(()=>e())})}ngOnDestroy(){this.authSubscription&&this.authSubscription.unsubscribe()}createPublicTestDoc(){if(!this.currentUser){alert("Please log in first");return}this.ngZone.runOutsideAngular(()=>{let e=Q(this.firestore,"public_test");it(e,{message:"Public test document created by validation component",createdBy:this.currentUser?.uid,createdAt:new Date().toISOString(),email:this.currentUser?.email}).then(t=>{this.ngZone.run(()=>{this.results.unshift({operation:"Create Public Document",path:`public_test/${t.id}`,success:!0,data:{id:t.id,message:"Public test document created successfully"}})})}).catch(t=>{this.ngZone.run(()=>{this.results.unshift({operation:"Create Public Document",path:"public_test/new_doc",success:!1,error:t.message})})})})}runValidationTest(){if(!this.currentUser){alert("Please log in first");return}let e=`${this.testCollection}/${this.testDocId||"new_doc_"+Date.now()}`;switch(this.testOperation){case"read":this.testRead(e);break;case"write":this.testWrite(e);break;case"update":this.testUpdate(e);break;case"delete":this.testDelete(e);break}}testRead(e){this.ngZone.runOutsideAngular(()=>{let t=me(this.firestore,e);qe(t).then(n=>{this.ngZone.run(()=>{n.exists()?this.results.unshift({operation:"Read",path:e,success:!0,data:n.data()}):this.results.unshift({operation:"Read",path:e,success:!0,data:"Document does not exist"})})}).catch(n=>{this.ngZone.run(()=>{this.results.unshift({operation:"Read",path:e,success:!1,error:n.message})})})})}testWrite(e){this.ngZone.runOutsideAngular(()=>{let t=me(this.firestore,e),n={createdBy:this.currentUser?.uid,createdAt:new Date().toISOString(),testValue:"Test document "+Date.now(),userId:this.currentUser?.uid};ht(t,n).then(()=>{this.ngZone.run(()=>{this.results.unshift({operation:"Write",path:e,success:!0,data:n})})}).catch(i=>{this.ngZone.run(()=>{this.results.unshift({operation:"Write",path:e,success:!1,error:i.message})})})})}testUpdate(e){this.ngZone.runOutsideAngular(()=>{let t=me(this.firestore,e),n={updatedBy:this.currentUser?.uid,updatedAt:new Date().toISOString(),lastUpdated:Date.now()};ht(t,n,{merge:!0}).then(()=>{this.ngZone.run(()=>{this.results.unshift({operation:"Update",path:e,success:!0,data:n})})}).catch(i=>{this.ngZone.run(()=>{this.results.unshift({operation:"Update",path:e,success:!1,error:i.message})})})})}testDelete(e){this.ngZone.runOutsideAngular(()=>{let t=me(this.firestore,e);Ct(t).then(()=>{this.ngZone.run(()=>{this.results.unshift({operation:"Delete",path:e,success:!0})})}).catch(n=>{this.ngZone.run(()=>{this.results.unshift({operation:"Delete",path:e,success:!1,error:n.message})})})})}static \u0275fac=function(t){return new(t||o)(P(Be),P(zt),P(je))};static \u0275cmp=E({type:o,selectors:[["app-firebase-validation"]],decls:83,vars:8,consts:[[1,"container","mt-5"],[1,"card"],[1,"card-header","bg-primary","text-white","d-flex","justify-content-between","align-items-center"],["routerLink","/firebase-test",1,"btn","btn-outline-light","me-2"],["routerLink","/",1,"btn","btn-outline-light"],[1,"card-body"],["class","alert alert-warning",4,"ngIf"],["class","alert alert-info",4,"ngIf"],[1,"card","mt-3"],[1,"card-header"],[1,"alert","alert-secondary"],[1,"mb-0"],[1,"btn","btn-primary",3,"click"],[1,"mb-3"],[1,"form-label"],[1,"form-select",3,"ngModelChange","ngModel"],["value","read"],["value","write"],["value","update"],["value","delete"],["value","users"],["value","public_test"],["value","appointments"],["value","doctorPatients"],["value","medicalRecords"],["value","firebase_test"],["type","text",1,"form-control",3,"ngModelChange","ngModel"],[1,"btn","btn-primary",3,"click","disabled"],["class","mt-4",4,"ngIf"],[1,"alert","alert-warning"],[1,"alert","alert-info"],[1,"mt-4"],[1,"list-group"],["class","list-group-item",3,"ngClass",4,"ngFor","ngForOf"],[1,"list-group-item",3,"ngClass"],[1,"d-flex","justify-content-between"],["class","text-danger mt-2",4,"ngIf"],["class","mt-2",4,"ngIf"],[1,"text-danger","mt-2"],[1,"mt-2"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2"),l(4,"Firebase Rules Validation"),a(),r(5,"div")(6,"a",3),l(7,"Basic Test"),a(),r(8,"a",4),l(9,"Home"),a()()(),r(10,"div",5),_(11,Bc,5,0,"div",6)(12,qc,7,2,"div",7),r(13,"div",8)(14,"div",9)(15,"h4"),l(16,"Troubleshooting Guide"),a()(),r(17,"div",5)(18,"p"),l(19,"If you're having permission issues, try these steps:"),a(),r(20,"ol")(21,"li"),l(22,"Make sure your user has a document in the "),r(23,"code"),l(24,"users"),a(),l(25," collection with your UID"),a(),r(26,"li"),l(27,"Try accessing the "),r(28,"code"),l(29,"public_test"),a(),l(30," collection which has looser permissions"),a(),r(31,"li"),l(32,"Check if your security rules are deployed correctly"),a()(),r(33,"div",10)(34,"p")(35,"strong"),l(36,"Currently using Firebase project:"),a(),l(37," medsecura-5abb2"),a(),r(38,"p",11)(39,"strong"),l(40,"Authentication status:"),a(),l(41),a()(),r(42,"button",12),h("click",function(){return n.createPublicTestDoc()}),l(43," Create Public Test Document "),a()()(),r(44,"div",8)(45,"div",5)(46,"h4"),l(47,"Test Specific Permissions"),a(),r(48,"div",13)(49,"label",14),l(50,"Operation"),a(),r(51,"select",15),L("ngModelChange",function(s){return R(n.testOperation,s)||(n.testOperation=s),s}),r(52,"option",16),l(53,"Read Document"),a(),r(54,"option",17),l(55,"Write Document"),a(),r(56,"option",18),l(57,"Update Document"),a(),r(58,"option",19),l(59,"Delete Document"),a()()(),r(60,"div",13)(61,"label",14),l(62,"Collection"),a(),r(63,"select",15),L("ngModelChange",function(s){return R(n.testCollection,s)||(n.testCollection=s),s}),r(64,"option",20),l(65,"Users"),a(),r(66,"option",21),l(67,"Public Test"),a(),r(68,"option",22),l(69,"Appointments"),a(),r(70,"option",23),l(71,"Doctor-Patient Relationships"),a(),r(72,"option",24),l(73,"Medical Records"),a(),r(74,"option",25),l(75,"Test Collection"),a()()(),r(76,"div",13)(77,"label",14),l(78,"Document ID (leave empty for new doc)"),a(),r(79,"input",26),L("ngModelChange",function(s){return R(n.testDocId,s)||(n.testDocId=s),s}),a()(),r(80,"button",27),h("click",function(){return n.runValidationTest()}),l(81," Run Test "),a()()(),_(82,Hc,5,1,"div",28),a()()()),t&2&&(d(11),p("ngIf",!n.currentUser),d(),p("ngIf",n.currentUser),d(29),M(" ",n.currentUser?"Authenticated":"Not authenticated",""),d(10),z("ngModel",n.testOperation),d(12),z("ngModel",n.testCollection),d(16),z("ngModel",n.testDocId),d(),p("disabled",!n.currentUser),d(2),p("ngIf",n.results.length>0))},dependencies:[A,gt,Se,U,fn,ee,yn,Cn,H,xn,Z,pe,ve,_e],encapsulation:2})};function Zc(o,e){if(o&1&&(r(0,"div"),l(1),a()),o&2){let t=b();Oe(t.connectionStatus==="Connected"?"success":"error"),d(),M(" Status: ",t.connectionStatus," ")}}function Kc(o,e){if(o&1&&(r(0,"div"),l(1),a()),o&2){let t=b();Oe(t.addResult.success?"success":"error"),d(),M(" ",t.addResult.message," ")}}function Qc(o,e){if(o&1&&(r(0,"div"),l(1),a()),o&2){let t=b();Oe(t.readResult.success?"success":"error"),d(),M(" ",t.readResult.message," ")}}function Gc(o,e){if(o&1&&(r(0,"div")(1,"h4"),l(2),a(),r(3,"pre"),l(4),tt(5,"json"),a()()),o&2){let t=b();d(2),M("Found ",t.documents.length," documents:"),d(2),T(pt(5,2,t.documents))}}var di=class o{constructor(e){this.firestore=e}connectionStatus="";testCollection="test_collection";readCollection="doctorAvailability";addResult=null;readResult=null;documents=[];ngOnInit(){console.log("Firebase Debug Component initialized"),console.log("Firestore instance:",this.firestore?"Available":"Not available")}testFirebaseConnection(){try{let e=Q(this.firestore,"connection_test");console.log("Collection reference created successfully"),this.connectionStatus="Connected"}catch(e){console.error("Error connecting to Firebase:",e),this.connectionStatus="Connection Failed"}}addTestDocument(){try{let e=Q(this.firestore,this.testCollection),t={test:!0,createdAt:new Date().toISOString(),message:"Test document"};it(e,t).then(n=>{console.log("Document added with ID:",n.id),this.addResult={success:!0,message:`Test document added successfully with ID: ${n.id}`}}).catch(n=>{console.error("Error adding document:",n),this.addResult={success:!1,message:`Error adding document: ${n.message}`}})}catch(e){console.error("Exception when trying to add document:",e),this.addResult={success:!1,message:`Exception: ${e.message}`}}}readTestDocuments(){try{let e=Q(this.firestore,this.readCollection);xe(e).then(t=>{this.documents=t.docs.map(n=>B({id:n.id},n.data())),this.readResult={success:!0,message:`Found ${this.documents.length} documents in ${this.readCollection}`}}).catch(t=>{console.error("Error reading documents:",t),this.readResult={success:!1,message:`Error reading documents: ${t.message}`}})}catch(e){console.error("Exception when trying to read documents:",e),this.readResult={success:!1,message:`Exception: ${e.message}`}}}static \u0275fac=function(t){return new(t||o)(P(Be))};static \u0275cmp=E({type:o,selectors:[["app-firebase-debug"]],decls:30,vars:6,consts:[[1,"debug-container"],[1,"test-section"],[3,"click"],[3,"class",4,"ngIf"],[1,"form-group"],["type","text",1,"form-control",3,"ngModelChange","ngModel"],[4,"ngIf"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"h2"),l(2,"Firebase Debug Tool"),a(),r(3,"div",1)(4,"h3"),l(5,"Test Connection"),a(),r(6,"button",2),h("click",function(){return n.testFirebaseConnection()}),l(7,"Test Connection"),a(),_(8,Zc,2,3,"div",3),a(),r(9,"div",1)(10,"h3"),l(11,"Add Test Document"),a(),r(12,"div",4)(13,"label"),l(14,"Collection Name"),a(),r(15,"input",5),L("ngModelChange",function(s){return R(n.testCollection,s)||(n.testCollection=s),s}),a()(),r(16,"button",2),h("click",function(){return n.addTestDocument()}),l(17,"Add Test Document"),a(),_(18,Kc,2,3,"div",3),a(),r(19,"div",1)(20,"h3"),l(21,"Read Test Documents"),a(),r(22,"div",4)(23,"label"),l(24,"Collection Name"),a(),r(25,"input",5),L("ngModelChange",function(s){return R(n.readCollection,s)||(n.readCollection=s),s}),a()(),r(26,"button",2),h("click",function(){return n.readTestDocuments()}),l(27,"Read Documents"),a(),_(28,Qc,2,3,"div",3)(29,Gc,6,4,"div",6),a()()),t&2&&(d(8),p("ngIf",n.connectionStatus),d(7),z("ngModel",n.testCollection),d(3),p("ngIf",n.addResult),d(7),z("ngModel",n.readCollection),d(3),p("ngIf",n.readResult),d(),p("ngIf",n.documents.length>0))},dependencies:[A,U,fn,ee,H,Z,pe],styles:[".debug-container[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}.test-section[_ngcontent-%COMP%]{margin-bottom:30px;padding:15px;border:1px solid #ddd;border-radius:5px}.form-group[_ngcontent-%COMP%]{margin-bottom:15px}label[_ngcontent-%COMP%]{display:block;margin-bottom:5px}.form-control[_ngcontent-%COMP%]{width:100%;padding:8px;border:1px solid #ddd;border-radius:4px}button[_ngcontent-%COMP%]{background-color:#4285f4;color:#fff;border:none;padding:10px 15px;border-radius:4px;cursor:pointer;margin-bottom:10px}.success[_ngcontent-%COMP%]{color:#4caf50;margin-top:10px;padding:10px;background-color:#e8f5e9;border-radius:4px}.error[_ngcontent-%COMP%]{color:#f44336;margin-top:10px;padding:10px;background-color:#ffebee;border-radius:4px}pre[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:10px;border-radius:4px;max-height:300px;overflow:auto}"]})};function Jc(o,e){if(o&1&&(r(0,"div"),l(1),a()),o&2){let t=b();Oe(t.addResult.success?"success":"error"),d(),M(" ",t.addResult.message," ")}}function Xc(o,e){if(o&1&&(r(0,"div"),l(1),a()),o&2){let t=b();Oe(t.viewResult.success?"success":"error"),d(),M(" ",t.viewResult.message," ")}}function ed(o,e){if(o&1){let t=V();r(0,"div",8)(1,"div"),l(2),a(),r(3,"div"),l(4),a(),r(5,"div"),l(6),a(),r(7,"button",9),h("click",function(){let i=v(t).$implicit,s=b(2);return x(s.deleteAvailability(i.id))}),l(8,"Delete"),a()()}if(o&2){let t=e.$implicit;d(2),M("Date: ",t.date,""),d(2),pn("Time: ",t.start_time," to ",t.end_time,""),d(2),M("ID: ",t.id,"")}}function td(o,e){if(o&1&&(r(0,"div")(1,"h4"),l(2),a(),_(3,ed,9,4,"div",7),a()),o&2){let t=b();d(2),M("Found ",t.availabilitySlots.length," availability slots:"),d(),p("ngForOf",t.availabilitySlots)}}function nd(o,e){if(o&1&&(r(0,"div"),l(1),a()),o&2){let t=b();Oe(t.collectionResult.success?"success":"error"),d(),M(" ",t.collectionResult.message," ")}}function id(o,e){if(o&1&&(r(0,"div")(1,"h4"),l(2),a(),r(3,"pre"),l(4),a()()),o&2){let t=b();d(2),M("Found ",t.collectionDocs.length," documents in collection:"),d(2),T(t.collectionDocsSummary)}}var mi=class o{firebaseService=Pe(lr);doctorId="";date="";startTime="";endTime="";addResult=null;viewDoctorId="";availabilitySlots=[];viewResult=null;collectionName="doctorAvailability";collectionDocs=[];collectionDocsSummary="";collectionResult=null;ngOnInit(){console.log("Firebase Availability Debug Component initialized");let e=new Date;this.date=e.toISOString().split("T")[0],this.startTime="09:00",this.endTime="10:00"}addTestAvailability(){if(!this.doctorId||!this.date||!this.startTime||!this.endTime){this.addResult={success:!1,message:"Please fill out all fields"};return}let e={doctor_id:this.doctorId,date:this.date,start_time:this.startTime,end_time:this.endTime};this.firebaseService.addAvailability(e).subscribe({next:t=>{this.addResult={success:!0,message:`Successfully added availability with ID: ${t}`}},error:t=>{this.addResult={success:!1,message:`Error adding availability: ${t.message||t}`}}})}viewAvailability(){if(!this.viewDoctorId){this.viewResult={success:!1,message:"Please enter a doctor ID"};return}this.firebaseService.loadDoctorAvailability(this.viewDoctorId).subscribe({next:e=>{this.availabilitySlots=e,this.viewResult={success:!0,message:`Loaded ${e.length} availability slots for doctor ${this.viewDoctorId}`}},error:e=>{this.viewResult={success:!1,message:`Error loading availability: ${e.message||e}`}}})}deleteAvailability(e){this.firebaseService.removeAvailability(e).subscribe({next:()=>{this.availabilitySlots=this.availabilitySlots.filter(t=>t.id!==e),this.viewResult={success:!0,message:`Successfully deleted availability slot with ID: ${e}`}},error:t=>{this.viewResult={success:!1,message:`Error deleting availability: ${t.message||t}`}}})}checkCollection(){if(!this.collectionName){this.collectionResult={success:!1,message:"Please enter a collection name"};return}this.collectionName==="doctorAvailability"?this.firebaseService.loadDoctorAvailability("").subscribe({next:e=>{this.collectionDocs=e,this.collectionDocsSummary=JSON.stringify(e,null,2),this.collectionResult={success:!0,message:`Found ${e.length} documents in collection ${this.collectionName}`}},error:e=>{this.collectionResult={success:!1,message:`Error checking collection: ${e.message||e}`}}}):this.collectionResult={success:!1,message:"Debug tool can only check the 'doctorAvailability' collection for now"}}static \u0275fac=function(t){return new(t||o)};static \u0275cmp=E({type:o,selectors:[["app-firebase-availability-debug"]],decls:47,vars:11,consts:[[1,"debug-container"],[1,"test-section"],[1,"form-group"],["type","text",1,"form-control",3,"ngModelChange","ngModel"],[3,"click"],[3,"class",4,"ngIf"],[4,"ngIf"],["class","availability-slot",4,"ngFor","ngForOf"],[1,"availability-slot"],[1,"delete-btn",3,"click"]],template:function(t,n){t&1&&(r(0,"div",0)(1,"h2"),l(2,"Firebase Availability Debug Tool"),a(),r(3,"div",1)(4,"h3"),l(5,"Add Test Availability"),a(),r(6,"div",2)(7,"label"),l(8,"Doctor ID"),a(),r(9,"input",3),L("ngModelChange",function(s){return R(n.doctorId,s)||(n.doctorId=s),s}),a()(),r(10,"div",2)(11,"label"),l(12,"Date (YYYY-MM-DD)"),a(),r(13,"input",3),L("ngModelChange",function(s){return R(n.date,s)||(n.date=s),s}),a()(),r(14,"div",2)(15,"label"),l(16,"Start Time (HH:MM)"),a(),r(17,"input",3),L("ngModelChange",function(s){return R(n.startTime,s)||(n.startTime=s),s}),a()(),r(18,"div",2)(19,"label"),l(20,"End Time (HH:MM)"),a(),r(21,"input",3),L("ngModelChange",function(s){return R(n.endTime,s)||(n.endTime=s),s}),a()(),r(22,"button",4),h("click",function(){return n.addTestAvailability()}),l(23,"Add Availability"),a(),_(24,Jc,2,3,"div",5),a(),r(25,"div",1)(26,"h3"),l(27,"View Availability for Doctor"),a(),r(28,"div",2)(29,"label"),l(30,"Doctor ID"),a(),r(31,"input",3),L("ngModelChange",function(s){return R(n.viewDoctorId,s)||(n.viewDoctorId=s),s}),a()(),r(32,"button",4),h("click",function(){return n.viewAvailability()}),l(33,"View Availability"),a(),_(34,Xc,2,3,"div",5)(35,td,4,2,"div",6),a(),r(36,"div",1)(37,"h3"),l(38,"Check Collections"),a(),r(39,"div",2)(40,"label"),l(41,"Collection Name"),a(),r(42,"input",3),L("ngModelChange",function(s){return R(n.collectionName,s)||(n.collectionName=s),s}),a()(),r(43,"button",4),h("click",function(){return n.checkCollection()}),l(44,"Check Collection"),a(),_(45,nd,2,3,"div",5)(46,id,5,2,"div",6),a()()),t&2&&(d(9),z("ngModel",n.doctorId),d(4),z("ngModel",n.date),d(4),z("ngModel",n.startTime),d(4),z("ngModel",n.endTime),d(3),p("ngIf",n.addResult),d(7),z("ngModel",n.viewDoctorId),d(3),p("ngIf",n.viewResult),d(),p("ngIf",n.availabilitySlots.length>0),d(7),z("ngModel",n.collectionName),d(3),p("ngIf",n.collectionResult),d(),p("ngIf",n.collectionDocs.length>0))},dependencies:[A,Se,U,ee,H,Z,pe],styles:[".debug-container[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}.test-section[_ngcontent-%COMP%]{margin-bottom:30px;padding:15px;border:1px solid #ddd;border-radius:5px}.form-group[_ngcontent-%COMP%]{margin-bottom:15px}label[_ngcontent-%COMP%]{display:block;margin-bottom:5px}.form-control[_ngcontent-%COMP%]{width:100%;padding:8px;border:1px solid #ddd;border-radius:4px}button[_ngcontent-%COMP%]{background-color:#4285f4;color:#fff;border:none;padding:10px 15px;border-radius:4px;cursor:pointer;margin-bottom:10px}.delete-btn[_ngcontent-%COMP%]{background-color:#f44336;margin-top:5px}.success[_ngcontent-%COMP%]{color:#4caf50;margin-top:10px;padding:10px;background-color:#e8f5e9;border-radius:4px}.error[_ngcontent-%COMP%]{color:#f44336;margin-top:10px;padding:10px;background-color:#ffebee;border-radius:4px}.availability-slot[_ngcontent-%COMP%]{padding:10px;margin:10px 0;background-color:#f5f5f5;border-radius:4px}pre[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:10px;border-radius:4px;max-height:300px;overflow:auto}"]})};var Ur=[{path:"",component:on},{path:"login",component:Un},{path:"register",component:Bn},{path:"dashboard",component:qn},{path:"doctors-patient",component:ni},{path:"doctors",component:ii},{path:"doctors/add",component:ri},{path:"doctors-profile",component:Kn},{path:"doctor-availability",component:Gn},{path:"firebase-test",component:li},{path:"firebase-validation",component:ci},{path:"firebase-debug",component:di},{path:"firebase-availability-debug",component:mi},{path:"medical-record/list",component:si},{path:"medical-record/add/:id",component:ai},{path:"appointments",loadComponent:()=>import("./chunk-Z765KNXI.js").then(o=>o.AppointmentsComponent)},{path:"settings",component:Wn},{path:"change-password",component:Jn},{path:"doctor-information",component:Xn},{path:"subscription",component:Hn},{path:"subscription-confirmation",component:ei},{path:"payment",children:[{path:"practice-info",component:$n},{path:"payment-info",component:Yn},{path:"",component:on},{path:"about",component:$t},{path:"benefits",component:Wt},{path:"",redirectTo:"practice-info",pathMatch:"full"}]},{path:"mobile",loadChildren:()=>import("./chunk-CAI4ERJU.js").then(o=>o.routes)}];var jr=class o{constructor(e){this.router=e}intercept(e,t){let n=localStorage.getItem("med_secure_token");return console.log("AuthInterceptor - token:",n?"present":"not found"),n&&(e=e.clone({setHeaders:{Authorization:`Bearer ${n}`}})),t.handle(e).pipe(re(i=>(i.status===401&&(console.log("Unauthorized request, redirecting to login"),localStorage.clear(),this.router.navigate(["/login"])),ce(()=>i))))}static \u0275fac=function(t){return new(t||o)(de(j))};static \u0275prov=be({token:o,factory:o.\u0275fac})},Br=(o,e)=>{let t=localStorage.getItem("med_secure_token");return t&&(o=o.clone({setHeaders:{Authorization:`Bearer ${t}`}})),e(o)};var pi=class o{storage=Pe(_n);auth=Pe(zt);ngZone=Pe(je);constructor(){}uploadProfilePicture(e,t){return this.uploadFile(e,`profilePictures/${t}`,{userId:t,type:"profile"})}uploadMedicalRecordFile(e,t,n,i){return this.uploadFile(e,`medicalRecords/${t}`,{recordId:t,patientId:n,doctorId:i,type:"medical-record"})}uploadDocument(e,t,n){return this.uploadFile(e,`documents/${t}/${n}`,{userId:n,category:t,type:"document"})}uploadFile(e,t,n){return this.ngZone.runOutsideAngular(()=>{let s=`${Date.now()}_${e.name}`,c=`${t}/${s}`,m=vn(this.storage,c),u={customMetadata:Ce(B({},n),{uploadedAt:new Date().toISOString(),uploadedBy:this.auth.currentUser?.uid||"anonymous"})},f=Ci(m,e,u);return new Zt(C=>{f.on("state_changed",y=>{let D=y.bytesTransferred/y.totalBytes*100;console.log(`Upload is ${D}% done`)},y=>{this.ngZone.run(()=>{console.error("Upload failed:",y),C.error(y)})},()=>{yi(f.snapshot.ref).then(y=>{this.ngZone.run(()=>{let D={downloadURL:y,fullPath:c,name:s};C.next(D),C.complete()})}).catch(y=>{this.ngZone.run(()=>{C.error(y)})})})})})}uploadFileWithProgress(e,t,n){return this.ngZone.runOutsideAngular(()=>{let s=`${Date.now()}_${e.name}`,c=`${t}/${s}`,m=vn(this.storage,c),u={customMetadata:Ce(B({},n),{uploadedAt:new Date().toISOString(),uploadedBy:this.auth.currentUser?.uid||"anonymous"})},f=Ci(m,e,u);return new Zt(C=>{f.on("state_changed",y=>{this.ngZone.run(()=>{let D={progress:y.bytesTransferred/y.totalBytes*100,bytesTransferred:y.bytesTransferred,totalBytes:y.totalBytes,state:y.state};C.next(D)})},y=>{this.ngZone.run(()=>{console.error("Upload failed:",y),C.error(y)})},()=>{yi(f.snapshot.ref).then(y=>{this.ngZone.run(()=>{let D={downloadURL:y,fullPath:c,name:s};C.next(D),C.complete()})}).catch(y=>{this.ngZone.run(()=>{C.error(y)})})})})})}deleteFile(e){return this.ngZone.runOutsideAngular(()=>{let t=vn(this.storage,e);return ie($o(t)).pipe(re(n=>{throw console.error("Error deleting file:",n),n}))})}deleteProfilePicture(e,t){let n=`profilePictures/${e}/${t}`;return this.deleteFile(n)}deleteMedicalRecordFile(e,t){let n=`medicalRecords/${e}/${t}`;return this.deleteFile(n)}validateFile(e,t=10,n=[]){let i=t*1024*1024;return e.size>i?{valid:!1,error:`File size exceeds ${t}MB limit`}:n.length>0&&!n.includes(e.type)?{valid:!1,error:`File type ${e.type} is not allowed. Allowed types: ${n.join(", ")}`}:{valid:!0}}getImageFileTypes(){return["image/jpeg","image/jpg","image/png","image/gif","image/webp"]}getDocumentFileTypes(){return["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain"]}getMedicalRecordFileTypes(){return[...this.getImageFileTypes(),...this.getDocumentFileTypes()]}static \u0275fac=function(t){return new(t||o)};static \u0275prov=be({token:o,factory:o.\u0275fac,providedIn:"root"})};var qr={providers:[vo({eventCoalescing:!0}),To(Ur,Io(ko)),Co(wo(),Po([Br]),Mo()),Rr(),Ao(()=>Fo(Lt.firebase)),No(()=>Ro()),Uo(()=>jo()),Wo(()=>Yo()),Ht,tr,nr,Pn,pi]};var gi=class o{title="MedSecura";static \u0275fac=function(t){return new(t||o)};static \u0275cmp=E({type:o,selectors:[["app-root"]],hostAttrs:[2,"display","block","min-height","100vh","background-color","var(--bg-main)"],decls:1,vars:0,template:function(t,n){t&1&&g(0,"router-outlet")},dependencies:[ve,Eo],styles:["[_nghost-%COMP%]{display:block;width:100%;height:100%}router-outlet[_ngcontent-%COMP%]{display:none}"]})};So(gi,qr).catch(o=>console.error(o));
