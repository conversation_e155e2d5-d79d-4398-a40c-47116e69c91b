import { Injectable, inject, signal } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, map, take } from 'rxjs';
import { FirebaseDataService } from './firebase-data.service';
import { FirebaseMigrationService } from './firebase-migration.service';
import {
  UserType,
  DoctorType,
  PatientType,
  AppointmentType,
  MedicalRecordType,
  AvailabilityType,
  DoctorPatient,
  AppointmentStatusType
} from '../../type';

/**
 * Firebase-based Database Service
 * Replaces the localStorage-based Db service
 */
@Injectable({
  providedIn: 'root'
})
export class FirebaseDbService {
  private firebaseDataService = inject(FirebaseDataService);
  private migrationService = inject(FirebaseMigrationService);
  private router = inject(Router);

  // Signals for reactive data (computed from Firebase observables)
  public userTable = signal<UserType[]>([]);
  public doctorTable = signal<DoctorType[]>([]);
  public patientTable = signal<PatientType[]>([]);
  public availabilityTable = signal<AvailabilityType[]>([]);
  public doctorPatientTable = signal<DoctorPatient[]>([]);
  public medicalRecordTable = signal<MedicalRecordType[]>([]);
  public appointmentTable = signal<AppointmentType[]>([]);

  public current_doctor = signal<UserType | null>(null);
  public current_patient = signal<UserType | null>(null);

  private migrationCompleted = false;

  constructor() {
    this.initializeFirebaseSubscriptions();
    this.checkAndMigrateData();
  }

  /**
   * Initialize subscriptions to Firebase data
   */
  private initializeFirebaseSubscriptions(): void {
    // Subscribe to Firebase data and update signals
    this.firebaseDataService.users$.subscribe(users => {
      this.userTable.set(users);
    });

    this.firebaseDataService.doctors$.subscribe(doctors => {
      this.doctorTable.set(doctors);
    });

    this.firebaseDataService.patients$.subscribe(patients => {
      this.patientTable.set(patients);
    });

    this.firebaseDataService.availability$.subscribe(availability => {
      this.availabilityTable.set(availability);
    });

    this.firebaseDataService.doctorPatient$.subscribe(relations => {
      this.doctorPatientTable.set(relations);
    });

    this.firebaseDataService.medicalRecords$.subscribe(records => {
      this.medicalRecordTable.set(records);
    });

    this.firebaseDataService.appointments$.subscribe(appointments => {
      this.appointmentTable.set(appointments);
    });

    this.firebaseDataService.currentDoctor$.subscribe(doctor => {
      this.current_doctor.set(doctor);
    });

    this.firebaseDataService.currentPatient$.subscribe(patient => {
      this.current_patient.set(patient);
    });
  }

  /**
   * Check for localStorage data and migrate if needed
   */
  private checkAndMigrateData(): void {
    if (!this.migrationCompleted && this.migrationService.hasDataToMigrate()) {
      console.log('Found localStorage data, starting migration...');
      
      this.migrationService.migrateAllData().subscribe({
        next: (result) => {
          console.log('Migration result:', result);
          
          if (result.success) {
            console.log('Migration successful, clearing localStorage...');
            this.migrationService.clearLocalStorageData();
            this.migrationCompleted = true;
          } else {
            console.error('Migration failed:', result.errors);
          }
        },
        error: (error) => {
          console.error('Migration error:', error);
        }
      });
    }
  }

  // ==================== USER OPERATIONS ====================

  /**
   * Register a new user
   */
  register(user: UserType): void {
    console.log('Registering user:', user);
    
    this.firebaseDataService.registerUser(user).subscribe({
      next: (registeredUser) => {
        console.log('User registered successfully:', registeredUser);
        
        if (user.role === 'DOCTOR') {
          this.firebaseDataService.setCurrentDoctor(user);
          this.router.navigate(['/doctors-profile']);
        } else if (user.role === 'PATIENT') {
          this.firebaseDataService.setCurrentPatient(user);
          this.router.navigate(['/mobile/patient-dashboard']);
        }
      },
      error: (error) => {
        console.error('Error registering user:', error);
      }
    });
  }

  /**
   * Login user
   */
  login(email: string, password: string): UserType | null {
    return this.firebaseDataService.loginUser(email, password);
  }

  /**
   * Set current doctor
   */
  setCurrentDoctor(user: UserType): void {
    this.firebaseDataService.setCurrentDoctor(user);
  }

  /**
   * Set current patient
   */
  setCurrentPatient(user: UserType): void {
    this.firebaseDataService.setCurrentPatient(user);
  }

  // ==================== DOCTOR OPERATIONS ====================

  /**
   * Add doctor profile
   */
  addDoctor(doctor: DoctorType): void {
    this.firebaseDataService.addDoctor(doctor).subscribe({
      next: (addedDoctor) => {
        console.log('Doctor added successfully:', addedDoctor);
      },
      error: (error) => {
        console.error('Error adding doctor:', error);
      }
    });
  }

  /**
   * Update doctor profile
   */
  updateDoctor(doctorId: string, doctorData: Partial<DoctorType>): void {
    this.firebaseDataService.updateDoctor(doctorId, doctorData).subscribe({
      next: () => {
        console.log('Doctor updated successfully');
      },
      error: (error) => {
        console.error('Error updating doctor:', error);
      }
    });
  }

  // ==================== PATIENT OPERATIONS ====================

  /**
   * Add patient profile
   */
  addPatient(patient: PatientType): void {
    this.firebaseDataService.addPatient(patient).subscribe({
      next: (addedPatient) => {
        console.log('Patient added successfully:', addedPatient);
      },
      error: (error) => {
        console.error('Error adding patient:', error);
      }
    });
  }

  /**
   * Update patient profile
   */
  updatePatient(patientId: string, patientData: Partial<PatientType>): void {
    this.firebaseDataService.updatePatient(patientId, patientData).subscribe({
      next: () => {
        console.log('Patient updated successfully');
      },
      error: (error) => {
        console.error('Error updating patient:', error);
      }
    });
  }

  // ==================== APPOINTMENT OPERATIONS ====================

  /**
   * Add appointment
   */
  addAppointment(appointment: AppointmentType): void {
    this.firebaseDataService.addAppointment(appointment).subscribe({
      next: (addedAppointment) => {
        console.log('Appointment added successfully:', addedAppointment);
      },
      error: (error) => {
        console.error('Error adding appointment:', error);
      }
    });
  }

  /**
   * Update appointment
   */
  updateAppointment(appointmentId: string, appointmentData: Partial<AppointmentType>): void {
    this.firebaseDataService.updateAppointment(appointmentId, appointmentData).subscribe({
      next: () => {
        console.log('Appointment updated successfully');
      },
      error: (error) => {
        console.error('Error updating appointment:', error);
      }
    });
  }

  /**
   * Delete appointment
   */
  deleteAppointment(appointmentId: string): void {
    this.firebaseDataService.deleteAppointment(appointmentId).subscribe({
      next: () => {
        console.log('Appointment deleted successfully');
      },
      error: (error) => {
        console.error('Error deleting appointment:', error);
      }
    });
  }

  // ==================== MEDICAL RECORD OPERATIONS ====================

  /**
   * Add medical record
   */
  addMedicalRecord(record: MedicalRecordType): void {
    this.firebaseDataService.addMedicalRecord(record).subscribe({
      next: (addedRecord) => {
        console.log('Medical record added successfully:', addedRecord);
      },
      error: (error) => {
        console.error('Error adding medical record:', error);
      }
    });
  }

  /**
   * Update medical record
   */
  updateMedicalRecord(recordId: string, recordData: Partial<MedicalRecordType>): void {
    this.firebaseDataService.updateMedicalRecord(recordId, recordData).subscribe({
      next: () => {
        console.log('Medical record updated successfully');
      },
      error: (error) => {
        console.error('Error updating medical record:', error);
      }
    });
  }

  // ==================== AVAILABILITY OPERATIONS ====================

  /**
   * Add availability slot
   */
  addAvailability(availability: AvailabilityType): void {
    this.firebaseDataService.addAvailability(availability).subscribe({
      next: (addedAvailability) => {
        console.log('Availability added successfully:', addedAvailability);
      },
      error: (error) => {
        console.error('Error adding availability:', error);
      }
    });
  }

  /**
   * Update availability slot
   */
  updateAvailability(availabilityId: string, availabilityData: Partial<AvailabilityType>): void {
    this.firebaseDataService.updateAvailability(availabilityId, availabilityData).subscribe({
      next: () => {
        console.log('Availability updated successfully');
      },
      error: (error) => {
        console.error('Error updating availability:', error);
      }
    });
  }

  /**
   * Delete availability slot
   */
  deleteAvailability(availabilityId: string): void {
    this.firebaseDataService.deleteAvailability(availabilityId).subscribe({
      next: () => {
        console.log('Availability deleted successfully');
      },
      error: (error) => {
        console.error('Error deleting availability:', error);
      }
    });
  }

  // ==================== DOCTOR-PATIENT RELATION OPERATIONS ====================

  /**
   * Add doctor-patient relation
   */
  addDoctorPatientRelation(relation: DoctorPatient): void {
    this.firebaseDataService.addDoctorPatientRelation(relation).subscribe({
      next: (addedRelation) => {
        console.log('Doctor-patient relation added successfully:', addedRelation);
      },
      error: (error) => {
        console.error('Error adding doctor-patient relation:', error);
      }
    });
  }

  // ==================== LEGACY COMPATIBILITY METHODS ====================

  /**
   * Legacy method - no longer saves to localStorage
   * Data is automatically saved to Firebase through the service
   */
  saveToLocalStorage(): void {
    console.log('saveToLocalStorage called - data is now automatically saved to Firebase');
  }

  /**
   * Legacy method - data is automatically loaded from Firebase
   */
  loadAllTable(): void {
    console.log('loadAllTable called - data is now automatically loaded from Firebase');
  }
}
