import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink, RouterLinkActive, Router } from '@angular/router';
import { AuthService } from '../../../auth/services/auth.service';
import { AppointmentsService } from '../../services/appointments.service';
import { ProfileUpdateService } from '../../services/profile-update.service';
import { Subscription, of } from 'rxjs';
import { Db } from '../../../../db';
import { FirebaseResetComponent } from '../../components/firebase-reset/firebase-reset.component';

import {
  Firestore,
  doc,
  getDoc,
  collection,
  query,
  where,
  getDocs
} from '@angular/fire/firestore';

interface DashboardStats {
  totalPatients: number;
  appointmentsToday: number;
  pendingConsultations: number;
}

interface DoctorInfo {
  profilePicture?: string;
  specialization?: string;
  phoneNumber?: string;
  address?: string;
  email?: string;
  bio?: string;
  hospitalAffiliations?: string[] | string;
  qualifications?: string[] | string;
  services?: string[] | string;
  firstName?: string;
  lastName?: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterLink, RouterLinkActive, FirebaseResetComponent],
  templateUrl: './dashboard.component.html',
  styleUrls: [
    './dashboard.component.css',
    '../../../../shared/styles/sidebar.css',
  ],
})
export class DashboardComponent implements OnInit, OnDestroy {
  doctorName = '';
  doctorInfo: DoctorInfo | null = null;
  stats: DashboardStats = {
    totalPatients: 0,
    appointmentsToday: 0,
    pendingConsultations: 0
  };
  appointments: any[] = [];
  alerts: any[] = [];
  private appointmentsSub?: Subscription;
  private profileUpdateSub?: Subscription;
  isLoading = true;

  constructor(
    private authService: AuthService,
    private appointmentsService: AppointmentsService,
    private profileUpdateService: ProfileUpdateService,
    private router: Router,
    private db: Db,
    private firestore: Firestore
  ) {}

  // Add public getter for doctor's first name
  getDoctorFirstName(): string {
    const doctor = this.db.current_doctor();
    return doctor?.firstname || '';
  }

  // Add getter for doctor's full name that prioritizes Firebase data
  getDoctorFullName(): string {
    // First check if we have data from Firebase via doctorInfo
    if (this.doctorInfo && this.doctorInfo.firstName && this.doctorInfo.lastName) {
      return `${this.doctorInfo.firstName} ${this.doctorInfo.lastName}`;
    }

    // Then fallback to local storage
    const doctor = this.db.current_doctor();
    if (doctor) {
      return `${doctor.firstname} ${doctor.lastname}`;
    }
    return '';
  }

  ngOnInit() {
    this.isLoading = true;

    // Load initial user info from localStorage
    const userInfo = this.authService.getUserInfo();
    if (userInfo) {
      // Extract first and last name from full name
      this.doctorName = userInfo.firstName || userInfo.name.split(' ')[0];

      // Initialize with user info data from localStorage
      this.doctorInfo = {
        profilePicture: this.ensureFullUrl(userInfo.profilePicture),
        specialization: userInfo.specialization,
        phoneNumber: userInfo.phoneNumber,
        address: userInfo.address,
        email: userInfo.email,
        bio: userInfo.bio,
        hospitalAffiliations: userInfo.hospitalAffiliations,
        qualifications: userInfo.qualifications,
        services: userInfo.services,
        firstName: userInfo.firstName,
        lastName: userInfo.lastName
      };

      // Attempt to fetch the doctor data from Firestore
      this.fetchDoctorFromFirestore(userInfo.id || userInfo.doctorId);

      // Load initial data
      this.loadData();

      // Subscribe to appointment changes
      this.appointmentsSub = this.appointmentsService.appointments$.subscribe(() => {
        this.loadData();
      });

      // Subscribe to profile updates
      this.profileUpdateSub = this.profileUpdateService.profileUpdated$.subscribe(updates => {
        console.log('Dashboard received profile update:', updates);
        if (updates.profilePicture) {
          if (this.doctorInfo) {
            this.doctorInfo.profilePicture = this.ensureFullUrl(updates.profilePicture);
          }
        }

        if (updates.name) {
          this.doctorName = updates.name.split(' ')[0]; // Get first name
        }

        if (updates.specialization && this.doctorInfo) {
          this.doctorInfo.specialization = updates.specialization;
        }

        if (updates.bio && this.doctorInfo) {
          this.doctorInfo.bio = updates.bio;
        }
      });
    }
  }

  private fetchDoctorFromFirestore(doctorId?: string) {
    if (!doctorId) {
      console.error('No doctor ID provided for Firestore fetch');
      this.isLoading = false;
      return;
    }

    console.log('Fetching doctor profile from Firestore for ID:', doctorId);

    // Create a reference to the doctor document
    const docRef = doc(this.firestore, `users/${doctorId}`);

    // Get the document
    getDoc(docRef)
      .then(docSnapshot => {
        this.isLoading = false;

        if (docSnapshot.exists()) {
          const firestoreData = docSnapshot.data() as Record<string, any>;
          console.log('Doctor data fetched from Firestore:', firestoreData);

          // Always use Firebase data as the primary source for doctor info
          this.doctorInfo = {
            profilePicture: this.ensureFullUrl(firestoreData['profilePicture']),
            specialization: firestoreData['specialization'],
            phoneNumber: firestoreData['phoneNumber'],
            email: firestoreData['email'],
            bio: firestoreData['bio'],
            address: firestoreData['address'],
            hospitalAffiliations: firestoreData['hospitalAffiliations'],
            qualifications: firestoreData['qualifications'],
            services: firestoreData['services'],
            firstName: firestoreData['firstName'] || firestoreData['firstname'],
            lastName: firestoreData['lastName'] || firestoreData['lastname']
          };

          // Update doctor name from Firebase
          if (firestoreData['firstName'] || firestoreData['firstname']) {
            this.doctorName = firestoreData['firstName'] || firestoreData['firstname'];
          }

          // Also update localStorage with the Firebase data for future sessions
          const userInfo = this.authService.getUserInfo();
          if (userInfo) {
            const updatedUserInfo = {
              ...userInfo,
              profilePicture: firestoreData['profilePicture'],
              specialization: firestoreData['specialization'],
              phoneNumber: firestoreData['phoneNumber'],
              bio: firestoreData['bio'],
              email: firestoreData['email'],
              address: firestoreData['address'],
              hospitalAffiliations: firestoreData['hospitalAffiliations'],
              qualifications: firestoreData['qualifications'],
              services: firestoreData['services'],
              firstName: firestoreData['firstName'] || firestoreData['firstname'],
              lastName: firestoreData['lastName'] || firestoreData['lastname']
            };
            this.authService.saveUserInfo(updatedUserInfo);
          }
        } else {
          console.log('No doctor document found in Firestore');
        }
      })
      .catch(error => {
        this.isLoading = false;
        console.error('Error fetching doctor from Firestore:', error);
      });
  }

  ngOnDestroy() {
    if (this.appointmentsSub) {
      this.appointmentsSub.unsubscribe();
    }
    if (this.profileUpdateSub) {
      this.profileUpdateSub.unsubscribe();
    }
  }

  private loadData() {
    // First load data from Firebase
    const doctorId = this.authService.getDoctorId();
    if (doctorId) {
      // Set loading indicator
      this.isLoading = true;

      // Get patient count from Firebase first
      this.fetchPatientCountFromFirestore(doctorId);

      // Get appointments from Firebase
      this.fetchAppointmentsFromFirestore(doctorId);

      // As a fallback, also get local data
      this.loadLocalPatientCount();
      this.loadLocalAppointments();
    }
  }

  private fetchPatientCountFromFirestore(doctorId: string) {
    // Create a query against the doctor-patients collection
    const doctorPatientsRef = collection(this.firestore, 'doctorPatients');
    const q = query(doctorPatientsRef, where('doctorId', '==', doctorId));

    getDocs(q)
      .then(querySnapshot => {
        // Always set patient count from Firebase, even if empty
        const uniquePatientIds = new Set();

        if (!querySnapshot.empty) {
          // Count unique patients
          querySnapshot.forEach(doc => {
            const data = doc.data() as Record<string, any>;
            if (data['patientId']) {
              uniquePatientIds.add(data['patientId']);
            }
          });
        }

        // Use Firestore's count as the source of truth
        const firestoreCount = uniquePatientIds.size;
        this.stats.totalPatients = firestoreCount;
        console.log('Firestore patient count:', firestoreCount);
      })
      .catch(error => {
        console.error('Error fetching patient count from Firestore:', error);
        // On error, fall back to local data
        this.loadLocalPatientCount();
      });
  }

  private fetchAppointmentsFromFirestore(doctorId: string) {
    // Create a query against the appointments collection
    const appointmentsRef = collection(this.firestore, 'appointments');
    const q = query(appointmentsRef, where('doctorId', '==', doctorId));

    getDocs(q)
      .then(querySnapshot => {
        // Always process Firebase data, even if empty
        const firestoreAppointments: any[] = [];

        if (!querySnapshot.empty) {
          querySnapshot.forEach(doc => {
            const data = doc.data() as Record<string, any>;
            firestoreAppointments.push({
              id: doc.id,
              doctor_id: data['doctorId'],
              patient_id: data['patientId'],
              date: data['date'],
              time: data['time'],
              status: data['status'] || 'Pending',
              patientName: data['patientName'] || 'Unknown Patient',
              reasonForVisit: data['reason'] || '',
              doctorNotes: data['notes'] || ''
            });
          });
        }

        // Use Firebase appointments as the primary source
        if (firestoreAppointments.length > 0) {
          this.updateAppointments(firestoreAppointments);
        } else {
          console.log('No appointments found in Firebase, falling back to local data');
        }
      })
      .catch(error => {
        console.error('Error fetching appointments from Firestore:', error);
        // On error, fall back to local data
      });
  }

  private updateAppointments(appointments: any[]) {
    // Get today's date for comparison
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Filter and sort today's approved appointments
    const todaysAppointments = appointments.filter((app) => {
      const appDate = new Date(app.date);
      appDate.setHours(0, 0, 0, 0);
      return appDate.getTime() === today.getTime() && app.status === 'Approved';
    });

    // Sort appointments by time
    this.appointments = todaysAppointments.sort((a, b) =>
      a.time.localeCompare(b.time)
    );

    // Update dashboard stats
    this.stats = {
      ...this.stats,
      appointmentsToday: todaysAppointments.length,
      pendingConsultations: appointments.filter(app => app.status === 'Pending').length
    };

    // Generate alerts for pending and today's appointments
    this.generateAlerts(appointments);
  }

  private generateAlerts(appointments: any[]) {
    const alerts: any[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Add alert for pending appointments
    const pendingCount = appointments.filter(app => app.status === 'Pending').length;
    if (pendingCount > 0) {
      alerts.push({
        type: 'info',
        message: `You have ${pendingCount} pending appointment requests`,
        time: 'Today'
      });
    }

    // Add alert for today's appointments
    const todayCount = appointments.filter(app => {
      const appDate = new Date(app.date);
      appDate.setHours(0, 0, 0, 0);
      return appDate.getTime() === today.getTime() && app.status === 'Approved';
    }).length;

    if (todayCount > 0) {
      alerts.push({
        type: 'warning',
        message: `You have ${todayCount} appointments scheduled for today`,
        time: 'Today'
      });
    }

    this.alerts = alerts;
  }

  // Updated to properly handle base64 images
  private ensureFullUrl(url: string | undefined | null): string | undefined {
    if (!url) return '/hospital.svg';

    // If it's already a data URL (base64), return as is
    if (url.startsWith('data:image')) {
      return url;
    }

    // If it starts with http, it's already a full URL
    if (url.startsWith('http')) {
      return url;
    }

    // Default fallback image
    return '/hospital.svg';
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  logout(): void {
    this.authService.logout();
  }

  // Renamed from loadPatientCount to loadLocalPatientCount to reflect its new backup role
  private loadLocalPatientCount() {
    const doctorId = this.authService.getDoctorId();
    if (!doctorId) return;

    // Get all doctor-patient relationships for this doctor
    const doctorPatients = this.db.doctorPatientTable()
      .filter(dp => dp.doctor_id === doctorId);

    // Get unique patient IDs from doctor-patient relationships
    const uniquePatientIds = new Set(doctorPatients.map(dp => dp.patient_id));

    // Also include patients from appointments
    const appointments = this.db.appointmentTable()
      .filter(a => a.doctor_id === doctorId && a.patient_id);

    // Add patient IDs from appointments to the set (only if patient exists in users table)
    appointments.forEach(app => {
      if (app.patient_id) {
        // Only count patients that exist in the user table
        const patientExists = this.db.userTable().some(user => user.id === app.patient_id);
        if (patientExists) {
          uniquePatientIds.add(app.patient_id);
        }
      }
    });

    // Only update stats if we don't have data from Firebase yet
    if (this.stats.totalPatients === 0) {
      this.stats.totalPatients = uniquePatientIds.size;
    }
  }

  private loadLocalAppointments() {
    const doctorId = this.authService.getDoctorId();
    if (!doctorId) return;

    const allAppointments = this.db.appointmentTable()
      .filter(a => a.doctor_id === doctorId)
      .map(app => {
        const patient = this.db.patientTable().find(p => p.id === app.patient_id);
        const patientUser = patient ? this.db.userTable().find(u => u.id === patient.user_id) : null;

        return {
          ...app,
          patientName: patientUser ? `${patientUser.firstname} ${patientUser.lastname}` : 'Unknown Patient'
        };
      });

    this.updateAppointments(allAppointments);
  }
}
